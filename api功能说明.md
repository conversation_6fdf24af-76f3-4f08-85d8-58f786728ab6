# 法律案件管理OA系统 API功能说明文档

## 项目概述

本系统是一个基于Spring Boot + Vue.js的法律案件管理OA系统，主要用于律师事务所或法律机构的案件管理、客户管理、文档管理等业务场景。

### 技术架构
- **后端框架**: Spring Boot + MyBatis + Shiro
- **前端框架**: Vue.js + Element UI
- **数据库**: MySQL
- **文档编辑**: WPS在线编辑集成
- **API文档**: Swagger2

### 主要功能模块
- 系统管理（用户、角色、权限、机构管理）
- 案件管理（案件信息、程序管理、审核流程）
- 客户管理（客户信息、联系人管理）
- 文档管理（案件文档、目录管理、在线编辑）
- 待办事项（任务管理、流程跟踪）
- WPS办公（文档模板、在线编辑）
- 移动端支持（移动端专用API）
- 监控管理（系统监控、定时任务）

## 通用接口说明

### 认证方式
系统采用JWT Token认证方式，除登录接口外，所有接口都需要在请求头中携带Token：
```
Authorization: Bearer {token}
```

### 请求格式
- **Content-Type**: `application/x-www-form-urlencoded` 或 `application/json`
- **字符编码**: UTF-8

### 返回格式
所有接口统一返回JSON格式：
```json
{
  "success": true,          // 请求是否成功
  "message": "操作成功",     // 返回消息
  "data": {},              // 返回数据
  "page": {}               // 分页信息（列表接口）
}
```

### 分页参数
列表查询接口通用分页参数：
- `pageNo`: 当前页码（从1开始）
- `pageSize`: 每页数量
- `orderBy`: 排序字段

### 权限控制
系统基于Shiro实现权限控制，接口权限要求：
- `user`: 登录用户权限
- `sys:user:list`: 具体功能权限
- `admin`: 管理员权限

## API接口详情

## 1. 系统管理模块

### 1.1 用户管理

#### 1.1.1 用户登录
- **接口路径**: `POST /sys/login`
- **功能描述**: 用户登录认证
- **权限要求**: 无需认证
- **请求参数**:
  - `userName` (string, 必填): 用户名
  - `password` (string, 必填): 密码
  - `clientId` (string, 可选): 客户端ID
- **返回数据**: 
  ```json
  {
    "success": true,
    "data": {
      "token": "jwt_token_string",
      "user": {
        "id": "用户ID",
        "name": "用户姓名",
        "loginName": "登录名"
      }
    }
  }
  ```

#### 1.1.2 获取用户列表
- **接口路径**: `POST /sys/user/list`
- **功能描述**: 分页查询用户列表
- **权限要求**: `sys:user:list` 或 `user`
- **请求参数**:
  - `pageNo` (int, 必填): 当前页码
  - `pageSize` (int, 必填): 每页数量
  - `name` (string, 可选): 用户姓名（模糊查询）
  - `loginName` (string, 可选): 登录名（模糊查询）
- **返回数据**: 分页用户列表

#### 1.1.3 保存用户
- **接口路径**: `POST /sys/user/save`
- **功能描述**: 新增或编辑用户信息
- **权限要求**: `sys:user:add` 或 `sys:user:edit` 或 `user`
- **请求参数**:
  - `id` (string, 可选): 用户ID（编辑时必填）
  - `name` (string, 必填): 用户姓名
  - `loginName` (string, 必填): 登录名
  - `newPassword` (string, 可选): 新密码
  - `type` (string, 必填): 用户类型
  - `office.id` (string, 必填): 所属机构ID
  - `roleIdList` (array, 可选): 角色ID列表

#### 1.1.4 查询用户详情
- **接口路径**: `POST /sys/user/queryById`
- **功能描述**: 根据ID查询用户详细信息
- **权限要求**: `user`
- **请求参数**:
  - `id` (string, 必填): 用户ID

#### 1.1.5 删除用户
- **接口路径**: `POST /sys/user/delete`
- **功能描述**: 删除用户（支持批量删除）
- **权限要求**: `sys:user:del`
- **请求参数**:
  - `ids` (string, 必填): 用户ID，多个用逗号分隔

#### 1.1.6 获取用户权限
- **接口路径**: `GET /sys/user/getPermissions`
- **功能描述**: 获取当前用户的权限列表
- **权限要求**: `user`
- **返回数据**: 权限字符串集合

#### 1.1.7 获取用户菜单
- **接口路径**: `GET /sys/user/getMenus`
- **功能描述**: 获取用户菜单、权限、字典等信息
- **权限要求**: `user`
- **返回数据**: 
  ```json
  {
    "menuList": [],      // 菜单列表
    "permissions": [],   // 权限列表
    "dictList": {},      // 字典数据
    "routerList": []     // 路由列表
  }
  ```

#### 1.1.8 获取所有字典
- **接口路径**: `GET /sys/user/getAllDict`
- **功能描述**: 获取系统所有字典数据
- **权限要求**: `user`

#### 1.1.9 简要用户列表
- **接口路径**: `POST /sys/user/allBriefList`
- **功能描述**: 获取用户简要信息列表（姓名、联系方式等）
- **权限要求**: `user`

### 1.2 角色管理

#### 1.2.1 角色列表
- **接口路径**: `GET /sys/role/list`
- **功能描述**: 分页查询角色列表
- **权限要求**: `sys:role:list`
- **请求参数**:
  - `pageNo` (int, 必填): 当前页码
  - `pageSize` (int, 必填): 每页数量
  - `name` (string, 可选): 角色名称

#### 1.2.2 保存角色
- **接口路径**: `POST /sys/role/save`
- **功能描述**: 新增或编辑角色
- **权限要求**: `sys:role:add` 或 `sys:role:edit`
- **请求参数**:
  - `id` (string, 可选): 角色ID
  - `name` (string, 必填): 角色名称
  - `enname` (string, 必填): 英文名称
  - `menuIds` (string, 可选): 菜单权限ID列表
  - `dataRuleIds` (string, 可选): 数据权限ID列表

#### 1.2.3 删除角色
- **接口路径**: `DELETE /sys/role/delete`
- **功能描述**: 删除角色（支持批量）
- **权限要求**: `sys:role:del`
- **请求参数**:
  - `ids` (string, 必填): 角色ID，多个用逗号分隔

#### 1.2.4 角色分配用户
- **接口路径**: `POST /sys/role/assignrole`
- **功能描述**: 为角色分配用户
- **权限要求**: `sys:role:assign`
- **请求参数**:
  - `id` (string, 必填): 角色ID
  - `ids` (array, 必填): 用户ID数组

### 1.3 机构管理

#### 1.3.1 机构列表
- **接口路径**: `GET /sys/office/list`
- **功能描述**: 获取机构列表
- **权限要求**: `sys:office:list`

#### 1.3.2 机构树形数据
- **接口路径**: `GET /sys/office/treeData`
- **功能描述**: 获取机构树形结构数据
- **权限要求**: `user`
- **请求参数**:
  - `extId` (string, 可选): 排除的机构ID
  - `type` (string, 可选): 机构类型（1:公司 2:部门）
  - `grade` (long, 可选): 显示级别
  - `isAll` (boolean, 可选): 是否显示所有

#### 1.3.3 保存机构
- **接口路径**: `POST /sys/office/save`
- **功能描述**: 新增或编辑机构
- **权限要求**: `sys:office:add` 或 `sys:office:edit`
- **请求参数**:
  - `id` (string, 可选): 机构ID
  - `name` (string, 必填): 机构名称
  - `parent.id` (string, 必填): 父机构ID
  - `type` (string, 必填): 机构类型
  - `grade` (string, 必填): 机构级别

#### 1.3.4 删除机构
- **接口路径**: `DELETE /sys/office/delete`
- **功能描述**: 删除机构
- **权限要求**: `sys:office:del`
- **请求参数**:
  - `ids` (string, 必填): 机构ID，多个用逗号分隔

### 1.4 区域管理

#### 1.4.1 区域列表
- **接口路径**: `GET /sys/area/list`
- **功能描述**: 获取所有区域列表
- **权限要求**: `sys:area:list`

#### 1.4.2 区域树形数据
- **接口路径**: `GET /sys/area/treeData`
- **功能描述**: 获取区域树形结构数据
- **权限要求**: `user`
- **请求参数**:
  - `extId` (string, 可选): 排除的区域ID

#### 1.4.3 保存区域
- **接口路径**: `POST /sys/area/save`
- **功能描述**: 新增或编辑区域
- **权限要求**: `sys:area:add` 或 `sys:area:edit`
- **请求参数**:
  - `id` (string, 可选): 区域ID
  - `name` (string, 必填): 区域名称
  - `parent.id` (string, 必填): 父区域ID
  - `type` (string, 必填): 区域类型

### 1.5 菜单管理

#### 1.5.1 菜单列表
- **接口路径**: `GET /sys/menu/list`
- **功能描述**: 获取所有菜单列表
- **权限要求**: `sys:menu:list`

### 1.6 系统配置

#### 1.6.1 获取系统配置
- **接口路径**: `GET /sys/sysConfig/queryById`
- **功能描述**: 获取系统配置信息
- **权限要求**: 无需认证

#### 1.6.2 获取配置信息
- **接口路径**: `GET /sys/sysConfig/getConfig`
- **功能描述**: 获取系统配置（公开接口）
- **权限要求**: 无需认证

### 1.7 岗位管理

#### 1.7.1 岗位列表
- **接口路径**: `GET /sys/post/list`
- **功能描述**: 分页查询岗位列表
- **权限要求**: `sys:post:list`

#### 1.7.2 保存岗位
- **接口路径**: `POST /sys/post/save`
- **功能描述**: 新增或编辑岗位
- **权限要求**: `sys:post:add` 或 `sys:post:edit`

#### 1.7.3 查询岗位详情
- **接口路径**: `GET /sys/post/queryById`
- **功能描述**: 根据ID查询岗位详情
- **权限要求**: `sys:post:view` 或 `sys:post:add` 或 `sys:post:edit`

## 2. 案件管理模块

### 2.1 案件信息管理

#### 2.1.1 案件列表
- **接口路径**: `POST /law/lawcase/case/list`
- **功能描述**: 分页查询案件列表
- **权限要求**: `lawcase:case:list` 或 `user`
- **请求参数**:
  - `pageNo` (int, 必填): 当前页码
  - `pageSize` (int, 必填): 每页数量
  - `status` (string, 可选): 案件状态（字典：case_status）
  - `auditStatus` (string, 可选): 审核状态（字典：case_audit_status）
  - `checkWord` (string, 可选): 检测字段（1:共享他人 2:他人共享）
  - `queryConcernPersonName` (string, 可选): 当事人名称
  - `type` (string, 可选): 案件类型
  - `name` (string, 可选): 案件名称
  - `number` (string, 可选): 案号
  - `acceptUnitName` (string, 可选): 受理单位

#### 2.1.2 所有案件数据
- **接口路径**: `POST /law/lawcase/case/allData`
- **功能描述**: 获取所有审核已通过的案件信息（无分页）
- **权限要求**: `lawcase:case:list` 或 `user`
- **请求参数**:
  - `name` (string, 可选): 案件名称

#### 2.1.3 保存案件信息
- **接口路径**: `POST /law/lawcase/case/saveInfo`
- **功能描述**: 新增案件信息
- **权限要求**: `lawcase:case:add`
- **请求参数**:
  - `name` (string, 必填): 案件名称
  - `type` (string, 必填): 案件类型
  - `customer.id` (string, 必填): 客户ID
  - `description` (string, 可选): 案件描述

#### 2.1.4 更新案件信息
- **接口路径**: `POST /law/lawcase/case/save`
- **功能描述**: 编辑更新案件信息
- **权限要求**: `lawcase:case:edit`
- **请求参数**:
  - `id` (string, 必填): 案件ID
  - `name` (string, 必填): 案件名称
  - `type` (string, 必填): 案件类型
  - `status` (string, 可选): 案件状态

#### 2.1.5 查询案件详情
- **接口路径**: `POST /law/lawcase/case/queryById`
- **功能描述**: 根据ID查询案件详细信息
- **权限要求**: `lawcase:case:view`
- **请求参数**:
  - `id` (string, 必填): 案件ID

#### 2.1.6 案件详情信息
- **接口路径**: `POST /law/lawcase/case/detailInfo`
- **功能描述**: 获取案件完整详情信息
- **权限要求**: `lawcase:case:view` 或 `user`
- **请求参数**:
  - `id` (string, 必填): 案件ID

#### 2.1.7 案件审核
- **接口路径**: `POST /law/lawcase/case/audit`
- **功能描述**: 案件审核操作
- **权限要求**: `lawcase:case:audit`
- **请求参数**:
  - `id` (string, 必填): 案件ID
  - `auditStatus` (string, 必填): 审核状态
  - `auditRemark` (string, 可选): 审核备注

#### 2.1.8 提交审核
- **接口路径**: `POST /law/lawcase/case/submitAudit`
- **功能描述**: 提交案件审核
- **权限要求**: `lawcase:case:edit`
- **请求参数**:
  - `id` (string, 必填): 案件ID

#### 2.1.9 案件结案
- **接口路径**: `POST /law/lawcase/case/settle`
- **功能描述**: 案件结案操作
- **权限要求**: `lawcase:case:settle`
- **请求参数**:
  - `id` (string, 必填): 案件ID
  - `settleDate` (string, 必填): 结案日期
  - `settleRemark` (string, 可选): 结案备注

#### 2.1.10 案件终止
- **接口路径**: `POST /law/lawcase/case/caseStop`
- **功能描述**: 案件终止操作
- **权限要求**: `lawcase:case:edit`
- **请求参数**:
  - `id` (string, 必填): 案件ID
  - `stopReason` (string, 必填): 终止原因

#### 2.1.11 编辑受理单位
- **接口路径**: `POST /law/lawcase/case/saveAccept`
- **功能描述**: 编辑案件受理单位信息
- **权限要求**: `lawcase:case:edit`
- **请求参数**:
  - `id` (string, 必填): 案件ID
  - `acceptUnitName` (string, 必填): 受理单位名称
  - `acceptDate` (string, 可选): 受理日期

#### 2.1.12 案件归档下载
- **接口路径**: `POST /law/lawcase/case/archiveDownload`
- **功能描述**: 下载案件归档文件
- **权限要求**: `lawcase:case:view`
- **请求参数**:
  - `id` (string, 必填): 案件ID

#### 2.1.13 删除案件
- **接口路径**: `POST /law/lawcase/case/delete`
- **功能描述**: 删除案件（支持批量）
- **权限要求**: `lawcase:case:del`
- **请求参数**:
  - `ids` (string, 必填): 案件ID，多个用逗号分隔

### 2.2 案件程序管理

#### 2.2.1 案件程序树形数据
- **接口路径**: `POST /law/lawcase/caseProgram/treeData`
- **功能描述**: 获取案件程序树形结构数据
- **权限要求**: `user`
- **请求参数**:
  - `extId` (string, 可选): 排除的ID

#### 2.2.2 根据类型获取程序数据
- **接口路径**: `POST /law/lawcase/caseProgram/typeTreeData`
- **功能描述**: 根据案件类型获取程序数据
- **权限要求**: `user`
- **请求参数**:
  - `type` (string, 必填): 案件类型

### 2.3 案件原因管理

#### 2.3.1 案件原因树形数据
- **接口路径**: `POST /law/lawcase/caseCause/treeData`
- **功能描述**: 获取案件原因树形数据
- **权限要求**: `user`

#### 2.3.2 案件模型类型数据
- **接口路径**: `POST /law/lawcase/caseCause/modelTypeTreeData`
- **功能描述**: 获取案件模型类型树形数据
- **权限要求**: `user`

### 2.4 案件阶段管理

#### 2.4.1 阶段列表
- **接口路径**: `GET /law/lawcase/stage/list`
- **功能描述**: 获取阶段信息列表
- **权限要求**: `case:stage:list`

#### 2.4.2 保存阶段
- **接口路径**: `POST /law/lawcase/stage/save`
- **功能描述**: 新增或编辑阶段信息
- **权限要求**: `case:stage:add` 或 `case:stage:edit`
- **请求参数**:
  - `id` (string, 可选): 阶段ID
  - `name` (string, 必填): 阶段名称
  - `description` (string, 可选): 阶段描述

#### 2.4.3 查询阶段详情
- **接口路径**: `GET /law/lawcase/stage/queryById`
- **功能描述**: 根据ID查询阶段详情
- **权限要求**: `case:stage:view` 或 `case:stage:add` 或 `case:stage:edit`
- **请求参数**:
  - `id` (string, 必填): 阶段ID

## 3. 客户管理模块

### 3.1 客户信息管理

#### 3.1.1 客户列表
- **接口路径**: `POST /law/lawcase/customer/list`
- **功能描述**: 分页查询客户列表
- **权限要求**: `lawcase:customer:list` 或 `user`
- **请求参数**:
  - `pageNo` (int, 必填): 当前页码
  - `pageSize` (int, 必填): 每页数量
  - `name` (string, 可选): 客户姓名/单位名称
  - `type` (string, 可选): 客户类型
  - `phone` (string, 可选): 联系电话

#### 3.1.2 所有客户数据
- **接口路径**: `POST /law/lawcase/customer/allData`
- **功能描述**: 获取所有客户数据（无分页）
- **权限要求**: `lawcase:customer:list` 或 `user`
- **请求参数**:
  - `name` (string, 可选): 姓名/单位名称

#### 3.1.3 新增客户
- **接口路径**: `POST /law/lawcase/customer/saveInfo`
- **功能描述**: 新增客户信息
- **权限要求**: `lawcase:customer:add`
- **请求参数**:
  - `name` (string, 必填): 客户姓名/单位名称
  - `type` (string, 必填): 客户类型
  - `phone` (string, 可选): 联系电话
  - `email` (string, 可选): 邮箱地址
  - `address` (string, 可选): 地址

#### 3.1.4 编辑客户
- **接口路径**: `POST /law/lawcase/customer/save`
- **功能描述**: 编辑客户信息
- **权限要求**: `lawcase:customer:edit`
- **请求参数**:
  - `id` (string, 必填): 客户ID
  - `name` (string, 必填): 客户姓名/单位名称
  - `type` (string, 必填): 客户类型
  - `phone` (string, 可选): 联系电话
  - `email` (string, 可选): 邮箱地址

#### 3.1.5 查询客户详情
- **接口路径**: `POST /law/lawcase/customer/queryById`
- **功能描述**: 根据ID查询客户详细信息
- **权限要求**: `lawcase:customer:view`
- **请求参数**:
  - `id` (string, 必填): 客户ID

#### 3.1.6 删除客户
- **接口路径**: `POST /law/lawcase/customer/delete`
- **功能描述**: 删除客户（支持批量）
- **权限要求**: `lawcase:customer:del`
- **请求参数**:
  - `ids` (string, 必填): 客户ID，多个用逗号分隔

#### 3.1.7 行业数据
- **接口路径**: `POST /law/lawcase/industry/treeData`
- **功能描述**: 获取行业分类树形数据
- **权限要求**: `user`

### 3.2 客户联系人管理

#### 3.2.1 联系人列表
- **接口路径**: `POST /law/case/customerContacts/list`
- **功能描述**: 分页查询客户联系人列表
- **权限要求**: `case:customerContacts:list` 或 `user`
- **请求参数**:
  - `pageNo` (int, 必填): 当前页码
  - `pageSize` (int, 必填): 每页数量
  - `customer.id` (string, 必填): 客户信息ID

#### 3.2.2 保存联系人
- **接口路径**: `POST /law/case/customerContacts/save`
- **功能描述**: 新增或编辑客户联系人
- **权限要求**: `case:customerContacts:add` 或 `case:customerContacts:edit` 或 `user`
- **请求参数**:
  - `id` (string, 可选): 联系人ID
  - `customer.id` (string, 必填): 客户ID
  - `name` (string, 必填): 联系人姓名
  - `phone` (string, 可选): 联系电话
  - `position` (string, 可选): 职位

#### 3.2.3 查询联系人详情
- **接口路径**: `POST /law/case/customerContacts/queryById`
- **功能描述**: 根据ID查询联系人详情
- **权限要求**: `case:customerContacts:view`
- **请求参数**:
  - `id` (string, 必填): 联系人ID

#### 3.2.4 删除联系人
- **接口路径**: `POST /law/case/customerContacts/delete`
- **功能描述**: 删除客户联系人
- **权限要求**: `case:customerContacts:del`
- **请求参数**:
  - `ids` (string, 必填): 联系人ID，多个用逗号分隔

## 4. 文档管理模块

### 4.1 案件文档管理

#### 4.1.1 文档列表
- **接口路径**: `POST /law/lawcase/caseFile/list`
- **功能描述**: 分页查询案件文档列表
- **权限要求**: `lawcase:caseFile:list` 或 `user`
- **请求参数**:
  - `pageNo` (int, 必填): 当前页码
  - `pageSize` (int, 必填): 每页数量
  - `lawCase.id` (string, 必填): 案件ID
  - `queryFileDirectoryId` (string, 可选): 文件目录ID

#### 4.1.2 查询文档详情
- **接口路径**: `GET /law/lawcase/caseFile/queryById`
- **功能描述**: 根据ID查询文档详情
- **权限要求**: `lawcase:caseFile:view` 或 `lawcase:caseFile:add` 或 `lawcase:caseFile:edit`
- **请求参数**:
  - `id` (string, 必填): 文档ID

#### 4.1.3 模板使用
- **接口路径**: `POST /law/lawcase/caseFile/create`
- **功能描述**: 使用模板创建文档
- **权限要求**: `lawcase:caseFile:add` 或 `lawcase:caseFile:edit` 或 `user`
- **请求参数**:
  - `lawCaseId` (string, 必填): 案件ID
  - `fileDirectoryId` (string, 可选): 文件目录ID（为空默认根目录）
  - `templateId` (string, 必填): 模板ID

#### 4.1.4 保存文档
- **接口路径**: `POST /law/lawcase/caseFile/save`
- **功能描述**: 新增或编辑文档信息
- **权限要求**: `lawcase:caseFile:add` 或 `lawcase:caseFile:edit` 或 `user`
- **请求参数**:
  - `id` (string, 可选): 文档ID
  - `lawCase.id` (string, 必填): 案件ID
  - `name` (string, 必填): 文档名称
  - `fileDirectory.id` (string, 可选): 文件目录ID

#### 4.1.5 文档上传
- **接口路径**: `POST /law/lawcase/caseFile/upload`
- **功能描述**: 上传案件文档
- **权限要求**: `lawcase:caseFile:add` 或 `user`
- **请求参数**:
  - `lawCaseId` (string, 必填): 案件ID
  - `fileDirectoryId` (string, 可选): 文件目录ID
  - `file` (file, 必填): 上传的文件

#### 4.1.6 批量上传目录
- **接口路径**: `POST /law/lawcase/caseFile/uploadDir`
- **功能描述**: 批量上传文档目录
- **权限要求**: `lawcase:caseFile:add` 或 `user`
- **请求参数**:
  - `lawCaseId` (string, 必填): 案件ID
  - `fileDirectoryId` (string, 可选): 文件目录ID
  - `files` (files, 必填): 上传的文件列表

#### 4.1.7 移动文档
- **接口路径**: `POST /law/lawcase/caseFile/move`
- **功能描述**: 移动文档到指定目录
- **权限要求**: `lawcase:caseFile:edit` 或 `user`
- **请求参数**:
  - `ids` (string, 必填): 文档ID，多个用逗号分隔
  - `targetDirectoryId` (string, 必填): 目标目录ID

#### 4.1.8 删除文档
- **接口路径**: `POST /law/lawcase/caseFile/delete`
- **功能描述**: 删除文档（支持批量）
- **权限要求**: `lawcase:caseFile:del` 或 `user`
- **请求参数**:
  - `ids` (string, 必填): 文档ID，多个用逗号分隔

### 4.2 文档目录管理

#### 4.2.1 目录树形数据
- **接口路径**: `POST /law/lawcase/caseFileDirectory/treeData`
- **功能描述**: 获取案件文档目录树形数据
- **权限要求**: `lawcase:caseFileDirectory:list` 或 `user`
- **请求参数**:
  - `lawCase.id` (string, 必填): 案件ID
  - `queryFileDirectoryId` (string, 可选): 查询的目录ID

#### 4.2.2 保存目录
- **接口路径**: `POST /law/lawcase/caseFileDirectory/save`
- **功能描述**: 新增或编辑文档目录
- **权限要求**: `lawcase:caseFileDirectory:add` 或 `lawcase:caseFileDirectory:edit` 或 `user`
- **请求参数**:
  - `id` (string, 可选): 目录ID
  - `lawCase.id` (string, 必填): 案件ID
  - `name` (string, 必填): 目录名称
  - `parent.id` (string, 可选): 父目录ID

#### 4.2.3 删除目录
- **接口路径**: `POST /law/lawcase/caseFileDirectory/delete`
- **功能描述**: 删除文档目录
- **权限要求**: `lawcase:caseFileDirectory:del` 或 `user`
- **请求参数**:
  - `ids` (string, 必填): 目录ID，多个用逗号分隔

## 5. 待办事项模块

### 5.1 待办事项管理

#### 5.1.1 待办事项列表
- **接口路径**: `GET /law/lawcase/todoInfo/list`
- **功能描述**: 获取待办事项列表
- **权限要求**: `lawcase:todoInfo:list`
- **请求参数**:
  - `relevanceType` (string, 可选): 关联类型
  - `relevanceId` (string, 可选): 关联ID

#### 5.1.2 案件待办事项列表
- **接口路径**: `POST /law/lawcase/todoInfo/caseList`
- **功能描述**: 获取案件相关的待办事项列表
- **权限要求**: `lawcase:todoInfo:list` 或 `user`
- **请求参数**:
  - `relevanceId` (string, 必填): 案件ID
  - `stage.id` (string, 可选): 阶段ID

#### 5.1.3 案件待办事项树形数据
- **接口路径**: `POST /law/lawcase/todoInfo/caseTreeData`
- **功能描述**: 获取案件待办事项树形结构数据
- **权限要求**: `lawcase:todoInfo:list` 或 `user`
- **请求参数**:
  - `relevanceId` (string, 必填): 案件ID
  - `stage.id` (string, 可选): 阶段ID

#### 5.1.4 客户待办事项列表
- **接口路径**: `POST /law/lawcase/todoInfo/customerList`
- **功能描述**: 获取客户相关的待办事项列表
- **权限要求**: `lawcase:todoInfo:list` 或 `user`
- **请求参数**:
  - `relevanceId` (string, 必填): 客户ID

#### 5.1.5 统计列表
- **接口路径**: `POST /law/lawcase/todoInfo/statisticList`
- **功能描述**: 获取待办事项统计列表
- **权限要求**: `lawcase:todoInfo:list` 或 `user`
- **请求参数**:
  - `relevanceType` (string, 可选): 关联类型
  - `status` (string, 可选): 状态

#### 5.1.6 查询待办详情
- **接口路径**: `POST /law/lawcase/todoInfo/queryById2`
- **功能描述**: 根据ID查询待办事项详情
- **权限要求**: `lawcase:todoInfo:view` 或 `user`
- **请求参数**:
  - `id` (string, 必填): 待办事项ID

#### 5.1.7 保存待办事项
- **接口路径**: `POST /law/lawcase/todoInfo/save`
- **功能描述**: 新增或编辑待办事项
- **权限要求**: `lawcase:todoInfo:add` 或 `lawcase:todoInfo:edit` 或 `user`
- **请求参数**:
  - `id` (string, 可选): 待办事项ID
  - `name` (string, 必填): 待办事项名称
  - `relevanceType` (string, 必填): 关联类型
  - `relevanceId` (string, 必填): 关联ID
  - `content` (string, 可选): 内容描述
  - `planStartDate` (string, 可选): 计划开始日期
  - `planEndDate` (string, 可选): 计划结束日期

#### 5.1.8 变更状态
- **接口路径**: `POST /law/lawcase/todoInfo/changeStatus`
- **功能描述**: 变更待办事项状态
- **权限要求**: `lawcase:todoInfo:edit` 或 `user`
- **请求参数**:
  - `id` (string, 必填): 待办事项ID
  - `status` (string, 必填): 新状态

#### 5.1.9 删除待办事项
- **接口路径**: `POST /law/lawcase/todoInfo/delete`
- **功能描述**: 删除待办事项
- **权限要求**: `lawcase:todoInfo:del` 或 `user`
- **请求参数**:
  - `ids` (string, 必填): 待办事项ID，多个用逗号分隔

#### 5.1.10 待办事项附件列表
- **接口路径**: `POST /law/lawcase/todoInfo/fileList`
- **功能描述**: 获取待办事项附件列表
- **权限要求**: `user`
- **请求参数**:
  - `todoId` (string, 必填): 待办事项ID

#### 5.1.11 上传附件
- **接口路径**: `POST /law/lawcase/todoInfo/fileUpload`
- **功能描述**: 上传待办事项附件
- **权限要求**: `lawcase:todoInfo:edit` 或 `user`
- **请求参数**:
  - `todoId` (string, 必填): 待办事项ID
  - `file` (file, 必填): 上传的文件

#### 5.1.12 删除附件
- **接口路径**: `POST /law/lawcase/todoInfo/deleteFile`
- **功能描述**: 删除待办事项附件
- **权限要求**: `lawcase:todoInfo:edit` 或 `user`
- **请求参数**:
  - `ids` (string, 必填): 附件ID，多个用逗号分隔

#### 5.1.13 使用待办文档模板
- **接口路径**: `POST /law/lawcase/todoInfo/templateCopy`
- **功能描述**: 使用文档模板创建待办事项文档
- **权限要求**: `lawcase:todoInfo:edit` 或 `user`
- **请求参数**:
  - `todoId` (string, 必填): 待办事项ID
  - `templateId` (string, 必填): 模板ID

## 6. WPS办公模块

### 6.1 文档模板管理

#### 6.1.1 模板列表
- **接口路径**: `POST /wps/docTemplate/list`
- **功能描述**: 分页查询文书模板列表
- **权限要求**: `wps:docTemplate:list` 或 `user`
- **请求参数**:
  - `pageNo` (int, 必填): 当前页码
  - `pageSize` (int, 必填): 每页数量
  - `name` (string, 可选): 模板名称
  - `category.id` (string, 可选): 所属分类ID

#### 6.1.2 查询模板详情
- **接口路径**: `POST /wps/docTemplate/queryById`
- **功能描述**: 根据ID查询文书模板详情
- **权限要求**: `wps:docTemplate:view` 或 `wps:docTemplate:add` 或 `wps:docTemplate:edit` 或 `user`
- **请求参数**:
  - `id` (string, 必填): 模板ID

#### 6.1.3 保存模板
- **接口路径**: `POST /wps/docTemplate/save`
- **功能描述**: 新增或编辑文书模板
- **权限要求**: `wps:docTemplate:add` 或 `wps:docTemplate:edit` 或 `user`
- **请求参数**:
  - `id` (string, 可选): 模板ID
  - `name` (string, 必填): 模板名称
  - `category.id` (string, 必填): 分类ID
  - `path` (string, 必填): 模板文件路径

#### 6.1.4 获取模板副本
- **接口路径**: `POST /wps/docTemplate/getCopyDoc`
- **功能描述**: 获取文书模板副本
- **权限要求**: `wps:docTemplate:add` 或 `user`
- **请求参数**:
  - `id` (string, 必填): 模板ID
  - `name` (string, 必填): 副本名称

### 6.2 WPS在线编辑

#### 6.2.1 获取编辑URL
- **接口路径**: `GET /law/wpsoffice/url`
- **功能描述**: 获取WPS在线编辑URL
- **权限要求**: `user`
- **请求参数**:
  - `_w_fname` (string, 必填): 文件名
  - `_w_fileid` (string, 必填): 文件ID
  - `operateType` (string, 必填): 操作类型（read/write）
  - `token` (string, 必填): 用户Token

#### 6.2.2 创建新文档URL
- **接口路径**: `GET /law/wpsoffice/createUrl`
- **功能描述**: 创建新文档的WPS编辑URL
- **权限要求**: `user`
- **请求参数**:
  - `type` (string, 必填): 文档类型（w:Word, s:Excel, p:PowerPoint）

#### 6.2.3 文件信息接口
- **接口路径**: `GET /v1/3rd/file/info`
- **功能描述**: WPS获取文件信息接口
- **权限要求**: 无需认证（WPS回调）
- **请求参数**:
  - `_w_fname` (string, 必填): 文件名
  - `_w_userid` (string, 必填): 用户ID
  - `_w_fileid` (string, 必填): 文件ID
  - `_w_operateType` (string, 必填): 操作类型

#### 6.2.4 文件保存接口
- **接口路径**: `POST /v1/3rd/file/save`
- **功能描述**: WPS文档保存回调接口
- **权限要求**: 无需认证（WPS回调）
- **请求参数**:
  - `file` (file, 必填): 保存的文件
  - `_w_userid` (string, 必填): 用户ID
  - `_w_fname` (string, 必填): 文件名

#### 6.2.5 文档上传接口
- **接口路径**: `POST /weboffice/upload`
- **功能描述**: 上传文档到WPS
- **权限要求**: `user`
- **请求参数**:
  - `file` (file, 必填): 上传的文件
  - `_w_userid` (string, 必填): 用户ID

## 7. 移动端专用接口

### 7.1 移动端登录

#### 7.1.1 移动端登录
- **接口路径**: `POST /app/sys/login`
- **功能描述**: 移动端用户登录
- **权限要求**: 无需认证
- **请求参数**:
  - `userName` (string, 必填): 用户名
  - `password` (string, 必填): 密码
  - `clientId` (string, 可选): 客户端ID

#### 7.1.2 移动端登出
- **接口路径**: `POST /app/sys/logout`
- **功能描述**: 移动端用户登出
- **权限要求**: `user`

### 7.2 移动端用户管理

#### 7.2.1 移动端用户列表
- **接口路径**: `GET /app/sys/user/list`
- **功能描述**: 获取用户列表（移动端）
- **权限要求**: `user`
- **请求参数**:
  - `pageNo` (int, 必填): 当前页码
  - `pageSize` (int, 必填): 每页数量

### 7.3 移动端机构管理

#### 7.3.1 移动端机构树形数据
- **接口路径**: `GET /app/sys/office/treeData`
- **功能描述**: 获取机构树形数据（移动端）
- **权限要求**: `user`
- **请求参数**:
  - `extId` (string, 可选): 排除的ID
  - `type` (string, 可选): 类型
  - `grade` (long, 可选): 级别
  - `isAll` (boolean, 可选): 是否显示所有

### 7.4 移动端区域管理

#### 7.4.1 移动端区域树形数据
- **接口路径**: `GET /app/sys/area/treeData`
- **功能描述**: 获取区域树形数据（移动端）
- **权限要求**: `user`
- **请求参数**:
  - `extId` (string, 可选): 排除的ID

### 7.5 移动端案件管理

#### 7.5.1 移动端案件列表
- **接口路径**: `POST /app/lawcase/case/list`
- **功能描述**: 分页查询案件列表（移动端）
- **权限要求**: `lawcase:case:list` 或 `user`
- **请求参数**:
  - `pageNo` (int, 必填): 当前页码
  - `pageSize` (int, 必填): 每页数量
  - `status` (string, 可选): 案件状态
  - `auditStatus` (string, 可选): 审核状态
  - `checkWord` (string, 可选): 检测字段
  - `name` (string, 可选): 案件名称

#### 7.5.2 移动端所有案件数据
- **接口路径**: `POST /app/lawcase/case/allData`
- **功能描述**: 获取所有案件数据（移动端，无分页）
- **权限要求**: `lawcase:case:list` 或 `user`

#### 7.5.3 移动端案件详情
- **接口路径**: `POST /app/lawcase/case/detailInfo`
- **功能描述**: 获取案件详情（移动端）
- **权限要求**: `lawcase:case:view` 或 `user`
- **请求参数**:
  - `id` (string, 必填): 案件ID

#### 7.5.4 移动端案件文档树形数据
- **接口路径**: `POST /app/lawcase/case/fileTreeData`
- **功能描述**: 获取案件文档树形数据（移动端）
- **权限要求**: `lawcase:case:view` 或 `user`
- **请求参数**:
  - `id` (string, 必填): 案件ID
  - `fileDirectoryId` (string, 可选): 文件目录ID


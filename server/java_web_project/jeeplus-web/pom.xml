<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>org.jeeplus</groupId>
        <artifactId>jeeplus</artifactId>
        <version>8.0</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <artifactId>jeeplus-web</artifactId>
    <packaging>war</packaging>

    <name>jeeplus-web</name>
    <description>starter</description>


    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <java.version>1.8</java.version>
    </properties>

    <dependencies>


        <dependency>
            <groupId>org.jeeplus</groupId>
            <artifactId>jeeplus-core</artifactId>
            <version>${project.parent.version}</version>
        </dependency>

        <dependency>
            <groupId>org.jeeplus</groupId>
            <artifactId>jeeplus-admin</artifactId>
            <version>${project.parent.version}</version>
        </dependency>
        <dependency>
            <groupId>org.jeeplus</groupId>
            <artifactId>jeeplus-datasource</artifactId>
            <version>${project.parent.version}</version>
        </dependency>


        <dependency>
            <groupId>org.jeeplus</groupId>
            <artifactId>jeeplus-quartz</artifactId>
            <version>${project.parent.version}</version>
        </dependency>
        <dependency>
            <groupId>org.jeeplus</groupId>
            <artifactId>jeeplus-tools</artifactId>
            <version>${project.parent.version}</version>
        </dependency>
        <dependency>
            <groupId>org.jeeplus</groupId>
            <artifactId>jeeplus-oa</artifactId>
            <version>${project.parent.version}</version>
        </dependency>

        <dependency>
            <groupId>org.jeeplus</groupId>
            <artifactId>jeeplus-monitor</artifactId>
            <version>${project.parent.version}</version>
        </dependency>

        <dependency>
            <groupId>org.jeeplus</groupId>
            <artifactId>jeeplus-ureport</artifactId>
            <version>${project.parent.version}</version>
        </dependency>

        <dependency>
            <groupId>org.jeeplus</groupId>
            <artifactId>jeeplus-wps</artifactId>
            <version>${project.parent.version}</version>
        </dependency>
        <dependency>
            <groupId>org.jeeplus</groupId>
            <artifactId>jeeplus-api</artifactId>
            <version>${project.parent.version}</version>
        </dependency>
        <dependency>
            <groupId>org.jeeplus</groupId>
            <artifactId>jeeplus-case</artifactId>
            <version>${project.parent.version}</version>
        </dependency>
    </dependencies>
    <build>
        <!--<finalName>jeeplus-vue</finalName>-->
        <finalName>law</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <!-- 指定该Main Class为全局的唯一入口 -->
                    <mainClass>com.jeeplus.JeeplusWebApplication</mainClass>
                </configuration>
            </plugin>
            <plugin>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                    <encoding>UTF-8</encoding>
                    <compilerArguments>
                        <extdirs>lib</extdirs>
                    </compilerArguments>
                </configuration>
            </plugin>

        </plugins>
        <resources>
            <resource>
                <directory>src/main/webapp</directory>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
            </resource>
            <resource>
                <directory>src/main/java</directory>
                <includes>
                    <include>**/*.xml</include>
                </includes>
            </resource>
        </resources>
    </build>

</project>

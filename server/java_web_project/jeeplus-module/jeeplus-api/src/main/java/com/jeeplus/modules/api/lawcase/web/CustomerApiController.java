/**
 * Copyright © 2015-2020 <a href="http://www.jeeplus.org/">JeePlus</a> All rights reserved.
 */
package com.jeeplus.modules.api.lawcase.web;

import com.jeeplus.common.json.AjaxJson;
import com.jeeplus.common.utils.StringUtils;
import com.jeeplus.core.persistence.Page;
import com.jeeplus.core.web.BaseController;
import com.jeeplus.modules.lawcase.entity.*;
import com.jeeplus.modules.lawcase.service.CaseCustomerRelationService;
import com.jeeplus.modules.lawcase.service.CaseService;
import com.jeeplus.modules.lawcase.service.CustomerService;
import com.jeeplus.modules.sys.entity.User;
import com.jeeplus.modules.sys.utils.UserUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;

/**
 *  客户信息Controller
 * <AUTHOR>
 * @version 2021-08-02
 */
@RestController
@Api(tags = "客户管理-移动端", description = "客户信息-移动端")
@RequestMapping(value = "/app/lawcase/customer")
public class CustomerApiController extends BaseController {

	@Autowired
	private CustomerService customerService;
	@Autowired
	private CaseService caseService;
	@Autowired
	private CaseCustomerRelationService caseCustomerRelationService;

	/**
	 * 保存客户信息
	 */
	@ApiOperation("新增保存")
	@RequiresPermissions(value={"lawcase:customer:add","lawcase:customer:edit", "user"},logical=Logical.OR)
	@PostMapping("save")
	public AjaxJson save(@RequestBody Customer customer) throws Exception{
		/**
		 * 后台hibernate-validation插件校验
		 */
		String errMsg = beanValidator(customer);
		if (StringUtils.isNotBlank(errMsg)){
			return AjaxJson.error(errMsg);
		}
		User user = UserUtils.getUser();
		if(user == null || StringUtils.isBlank(user.getId())){
			return AjaxJson.error("当前登录信息有误！");
		}

		CustomerFollowUp customerFollowUp = new CustomerFollowUp();
		customerFollowUp.setUser(user);
		try {
			customerService.save(customer, new ArrayList<CustomerFollowUp>(){{ this.add(customerFollowUp); }});
			return AjaxJson.success("保存成功");
		} catch (Exception e) {
			e.printStackTrace();
			return AjaxJson.error("保存失败");
		}
	}


	@RequiresPermissions(value = "user")
	@ApiOperation(value = "已关联案件信息列表", consumes = "application/form-data")
	@ApiImplicitParam(value = "客户id", name = "customer.id",required = true)
	@PostMapping("relationList")
	public AjaxJson list(CaseCustomerRelation customerRelation, HttpServletRequest request, HttpServletResponse response) {
		Page<CaseCustomerRelation> page = new Page<CaseCustomerRelation>(request, response, -1);
		if(customerRelation.getCustomer() != null && StringUtils.isNotBlank(customerRelation.getCustomer().getId())){
			page = caseCustomerRelationService.findPage(page, customerRelation);
		}
		return AjaxJson.success().put("page",page);
	}

	@RequiresPermissions(value = "user")
	@ApiOperation(value = "未关联案件信息列表", consumes = "application/form-data")
	@ApiImplicitParams({
			@ApiImplicitParam(value = "客户id", name = "customerId",required = true),
			@ApiImplicitParam(value = "案件名称", name = "name")
	})
	@PostMapping("caseList")
	public AjaxJson list(String customerId, String name, HttpServletRequest request, HttpServletResponse response) {
		return AjaxJson.success().put("data", caseCustomerRelationService.findNotRelationCaseList(customerId, name));
	}

	@RequiresPermissions(value={"case:customerRelation:add","case:customerRelation:edit", "user"},logical=Logical.OR)
	@ApiOperation(value = "保存案件关联", consumes = "application/form-data")
	@ApiImplicitParams({
			@ApiImplicitParam(value = "关联案件id", name = "lawCase.id", required = true),
			@ApiImplicitParam(value = "客户id", name = "customer.id", required = true)
	})
	@PostMapping("saveRelation")
	public AjaxJson save(CaseCustomerRelation caseCustomerRelation) throws Exception{
		/**
		 * 后台hibernate-validation插件校验
		 */
		String errMsg = beanValidator(caseCustomerRelation);
		if (StringUtils.isNotBlank(errMsg)){
			return AjaxJson.error(errMsg);
		}
		if(caseCustomerRelation.getLawCase() == null || StringUtils.isBlank(caseCustomerRelation.getLawCase().getId())){
			return AjaxJson.error("关联案件信息有误");
		}
		if(caseCustomerRelation.getCustomer() == null || StringUtils.isBlank(caseCustomerRelation.getCustomer().getId())){
			return AjaxJson.error("客户信息有误");
		}
		Case lawCase = caseService.get(caseCustomerRelation.getLawCase());
		if(lawCase == null || StringUtils.isBlank(lawCase.getId())){
			return AjaxJson.error("案件信息有误");
		}
		caseCustomerRelation.setLawCase(lawCase);

		/* 验证是否已关联 */
		Integer count = caseCustomerRelationService.getCount(caseCustomerRelation);
		if(count != null && count > 0){
			return AjaxJson.success("关联成功");
		}
		try {
			caseCustomerRelationService.save(caseCustomerRelation);
			return AjaxJson.success("关联成功");
		} catch (Exception e) {
			e.printStackTrace();
			return AjaxJson.error("关联失败");
		}
	}


	/**
	 * 批量删除阶段信息
	 */
	@RequiresPermissions(value = {"case:customerRelation:del", "user"}, logical = Logical.OR)
	@ApiOperation(value = "取消关联", consumes = "application/form-data")
	@ApiImplicitParam(value = "记录id 多个以 , 拼接", name = "ids", required = true)
	@PostMapping("deleteRelation")
	public AjaxJson delete(String ids) {
		String[] idArray =ids.split(",");
		for(String id : idArray){
			caseCustomerRelationService.delete(new CaseCustomerRelation(id));
		}
		return AjaxJson.success("解除成功");
	}

}
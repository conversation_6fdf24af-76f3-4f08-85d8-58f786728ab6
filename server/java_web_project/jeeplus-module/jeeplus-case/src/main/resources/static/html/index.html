<html>
<meta charset="UTF-8">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<html>
<head>
<title>WEB-socket 测试</title>
    <script type="text/javascript" src="http://code.jquery.com/jquery-1.4.4.js"></script>
    <!--<script type="text/javascript" src="https://cdn.socket.io/socket.io-1.0.5.js" ></script>-->
    <script type="text/javascript" src="http://localhost:8082/law/static/html/js/socket.io-1.0.5.js" ></script>
<style>

</style>

    <script>
        var url = "http://law.291w.com";
        // var url = "http://localhost";
        // 建立连接
        var socket = io.connect(url, {
            "query": 'userId=111'
        });
        socket.on("connect", function () {
            console.log("连接成功")
        });
        socket.on("disconnect", function () {
            console.log("断开连接")
        });
        // 监听 message 会话
        socket.on('message', function (data) {
            let html = document.createElement('p')
            html.innerHTML = `系统消息：<span>${data.hello}</span>`
            document.getElementById('content-module').appendChild(html)
            console.log(data);
        });
        // 按钮点击事件
        function sendFun() {
            let text = $("input[name='text']").val();
            if (!text) return;
            let html = document.createElement('p')
            html.innerHTML = `你细声说：<span>${text}</span>`
            document.getElementById('content-module').appendChild(html);
            socket.emit('clientMessage', { my: text});
        }
        // 监听 news 会话
        socket.on('news', function (data) {
            console.log(data);
            let html = document.createElement('p')
            html.innerHTML = `小皮咖说：<span>我知道了，你说“${data}”</span>`
            document.getElementById('content-module').appendChild(html)
        });



    </script>

</head>
<body style="padding: 30px">
<h3>socket测试</h3>
<div id="content-module">

</div>
<div>
    <input name="text" style="padding: 5px" />
    <button value="发送" onclick="sendFun()">发送</button>
</div>
</body>
</html>
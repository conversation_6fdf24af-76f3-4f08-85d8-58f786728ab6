/**
 * Copyright © 2015-2020 <a href="http://www.jeeplus.org/">JeePlus</a> All rights reserved.
 */
package com.jeeplus.modules.lawcase.entity;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.jeeplus.modules.sys.entity.User;

import com.jeeplus.core.persistence.DataEntity;
import com.jeeplus.common.utils.excel.annotation.ExcelField;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 案件信息Entity
 * <AUTHOR>
 * @version 2021-08-02
 */
@Data
public class Case extends DataEntity<Case> {
	
	private static final long serialVersionUID = 1L;
	/** 检测字段 1、共享他人  2、他人共享  */
	public static final String CHECK_WORD_SHARE = "1";
	public static final String CHECK_WORD_OTHER_SHARE = "2";
	/** 查询类型  99、其他案件（用于APP检索）  99、查询状态未结办案件 */
	public static final String QUERY_TYPE_OTHER = "99";

	@NotBlank(message = "案件类型不能为空")
	@ExcelField(title="案件类型", dictType = "case_type", align=2, sort=1)
	private String type;		// 案件类型
	@ExcelField(title="案件程序", align=2, sort=2)
	private CaseProgram caseProgram;		// 案件程序
	@NotBlank(message = "案件名称不能为空")
	@ExcelField(title="案件名称", align=2, sort=3)
	private String name;		// 案件名称
	@NotNull(message = "案由不能为空")
	@ExcelField(title="案由", align=2, sort=4)
	private CaseCause caseCause;	// 案由 	2021-09-04 新增
	@ExcelField(title="案号", align=2, sort=5)
	private String number;		// 案号
	@NotNull(message = "委托时间不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@ExcelField(title="委托时间", align=2, sort=6)
	private Date entrustDate;		// 委托时间
	@ExcelField(title="案件标的", align=2, sort=7)
	private Double subjectMatter;		// 案件标的
	@NotNull(message = "主办人不能为空")
	@ExcelField(title="主办人", fieldType=User.class, value="hostUser.name", align=2, sort=8)
	private User hostUser;		// 主办人
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@ExcelField(title="立案日期", align=2, sort=9)
	private Date recordDate;		// 立案日期
	@ExcelField(title="审理结果", dictType="trial_result", align=2, sort=10)
	private String trialResult;		// 审理结果
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@ExcelField(title="裁决日期", align=2, sort=11)
	private Date rulingDate;		// 裁决日期
	@ExcelField(title="结案状态", dictType="settle_case_status", align=2, sort=12)
	private String settleCaseStatus;		// 结案状态
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@ExcelField(title="结案日期", align=2, sort=13)
	private Date settleCaseDate;		// 结案日期
	@ExcelField(title="归档人", fieldType=User.class, value="archiveUser.name", align=2, sort=14)
	private User archiveUser;		// 归档人
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@ExcelField(title="归档日期", align=2, sort=15)
	private Date archiveDate;		// 归档日期
	@ExcelField(title="档案保管地", align=2, sort=16)
	private String custodyPlace;		// 档案保管地
	@ExcelField(title="收费方式", dictType="charge_mode", align=2, sort=17)
	private String chargeMode;		// 收费方式
	@NotNull(message = "合同金额不能为空")
	@ExcelField(title="合同金额", align=2, sort=18)
	private Double contractMoney;		// 合同金额
	@ExcelField(title="收费备注", align=2, sort=19)
	private String chargeRemarks;		// 收费备注
	@ExcelField(title="实际回款", align=2, sort=20)
	private Double actualBackMoney;		// 实际回款
	@ExcelField(title="胜诉金额", align=2, sort=21)
	private Double winMoney;		// 胜诉金额
	@ExcelField(title="是否共享", align=2, sort=22)
	private String isShare;		// 是否共享
	@ExcelField(title="受理单位地区", align=2, sort=23)
	private String acceptUnitArea;		// 受理单位地区
	@ExcelField(title="单位类型", dictType="accept_unit_type", align=2, sort=24)
	private String acceptUnitType;		// 单位类型
	@ExcelField(title="单位名称", align=2, sort=25)
	private String acceptUnitName;		// 单位名称
	@ExcelField(title="是否审核案件", dictType = "yes_no", align=2, sort=26)
	private String isAudit;			// 是否审核案件
	@ExcelField(title="案件审核状态", dictType = "case_audit_status", align=2, sort=27)
	private String auditStatus;		// 案件审核状态
	private String auditReason;		// 审核理由
	@ExcelField(title="案件状态", dictType = "case_status", align=2, sort=30)
	private String status;		// 案件状态
	private String mtypeId;		// 模板id
	private String mtypeName;		// 模板id
	// 虚拟
	private String checkWord;			// 检测字段
	private String entrustPersonNames;	// 委托人名称
	private String concernPersonNames;	// 其他当事人名称
	private String undertakePersonNames;// 承办人名称
	private String stageName;			// 当前阶段名称
	private String queryConcernPersonName;	// 查询当事人名称
	private String queryStageName;		// 查询阶段名称
	private Date queryStartEntrustDate;	// 查询委托开始日期
	private Date queryEndEntrustDate;	// 查询委托结束日期
	private Date queryStartSettleCaseDate;	// 查询结案开始日期
	private Date queryEndSettleCaseDate;	// 查询结案结束日期
	private String queryYear;				// 查询年


	public Case() {
		super();
	}
	
	public Case(String id){
		super(id);
	}
}
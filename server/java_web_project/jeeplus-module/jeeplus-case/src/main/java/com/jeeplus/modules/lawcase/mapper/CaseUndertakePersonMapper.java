/**
 * Copyright © 2015-2020 <a href="http://www.jeeplus.org/">JeePlus</a> All rights reserved.
 */
package com.jeeplus.modules.lawcase.mapper;

import com.jeeplus.modules.lawcase.entity.CaseUndertakePerson;
import org.springframework.stereotype.Repository;
import com.jeeplus.core.persistence.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 案件承办人员MAPPER接口
 * <AUTHOR>
 * @version 2021-08-02
 */
@Mapper
@Repository
public interface CaseUndertakePersonMapper extends BaseMapper<CaseUndertakePerson> {

    /**
     * 通过 案件id 删除案件承办人员信息
     * @param lawCaseId
     * @return
     */
    int deleteByCase(String lawCaseId);
}
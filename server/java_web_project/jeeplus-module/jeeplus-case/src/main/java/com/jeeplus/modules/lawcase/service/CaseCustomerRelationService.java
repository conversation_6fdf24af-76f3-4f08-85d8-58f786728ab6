/**
 * Copyright © 2015-2020 <a href="http://www.jeeplus.org/">JeePlus</a> All rights reserved.
 */
package com.jeeplus.modules.lawcase.service;

import com.google.common.collect.Maps;
import com.jeeplus.common.utils.StringUtils;
import com.jeeplus.core.persistence.Page;
import com.jeeplus.core.service.CrudService;
import com.jeeplus.modules.lawcase.entity.Case;
import com.jeeplus.modules.lawcase.entity.CaseCaseRelation;
import com.jeeplus.modules.lawcase.entity.CaseCustomerRelation;
import com.jeeplus.modules.lawcase.mapper.CaseCaseRelationMapper;
import com.jeeplus.modules.lawcase.mapper.CaseCustomerRelationMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

/**
 * 案件与客户 关联 Service
 * <AUTHOR>
 * @version 2021-08-02
 */
@Service
@Transactional(readOnly = true)
public class CaseCustomerRelationService extends CrudService<CaseCustomerRelationMapper, CaseCustomerRelation> {


	public CaseCustomerRelation get(String id) {
		return super.get(id);
	}
	
	public List<CaseCustomerRelation> findList(CaseCustomerRelation customerRelation) {
		return super.findList(customerRelation);
	}
	
	public Page<CaseCustomerRelation> findPage(Page<CaseCustomerRelation> page, CaseCustomerRelation customerRelation) {
		return super.findPage(page, customerRelation);
	}

	/**
	 * 根据关联案件id、客户id 查询关联记录数量
	 * @param customerRelation
	 * @return
	 */
	public Integer getCount(CaseCustomerRelation customerRelation){
		return mapper.getCount(customerRelation);
	}

	/**
	 * 获取未关联案件信息
	 * @param customerId
	 * @param name
	 * @return
	 */
	public List<Case> findNotRelationCaseList(String customerId, String name){
		Map<String, String> map = Maps.newHashMap();
		map.put("customerId", customerId);
		if (StringUtils.isNotBlank(name)) {
			map.put("name", name);
		}
		return mapper.findNotRelationCaseList(map);
	}

	@Transactional(readOnly = false)
	public void save(CaseCustomerRelation customerRelation) {
		super.save(customerRelation);
	}

	@Transactional(readOnly = false)
	public void delete(CaseCustomerRelation customerRelation) {
		super.delete(customerRelation);
	}

	/**
	 * 根据案件删除案件与案件关联
	 * @param caseId
	 */
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public void deleteByCase(String caseId) {
		mapper.deleteByCase(caseId);
	}
}
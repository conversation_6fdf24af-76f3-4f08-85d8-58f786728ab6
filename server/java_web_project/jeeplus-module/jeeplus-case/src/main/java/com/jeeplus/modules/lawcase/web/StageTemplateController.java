/**
 * Copyright © 2015-2020 <a href="http://www.jeeplus.org/">JeePlus</a> All rights reserved.
 */
package com.jeeplus.modules.lawcase.web;

import java.util.ArrayList;
import java.util.List;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.ConstraintViolationException;

import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.google.common.collect.Lists;
import com.jeeplus.common.json.AjaxJson;
import com.jeeplus.common.utils.DateUtils;
import com.jeeplus.common.utils.StringUtils;
import com.jeeplus.common.utils.excel.ExportExcel;
import com.jeeplus.common.utils.excel.ImportExcel;
import com.jeeplus.config.properties.JeePlusProperites;
import com.jeeplus.core.persistence.Page;
import com.jeeplus.core.web.BaseController;
import com.jeeplus.modules.lawcase.constant.CaseConstant;
import com.jeeplus.modules.lawcase.entity.CaseModelType;
import com.jeeplus.modules.lawcase.entity.Stage;
import com.jeeplus.modules.lawcase.entity.StageTemplate;
import com.jeeplus.modules.lawcase.service.CaseModelTypeService;
import com.jeeplus.modules.lawcase.service.StageService;
import com.jeeplus.modules.lawcase.service.StageTemplateService;
import com.jeeplus.modules.lawcase.vo.StageTemplateSortVO;
import com.jeeplus.modules.lawcase.vo.StageTemplateVO;
import com.jeeplus.modules.lawcase.vo.StageVO;
import com.jeeplus.modules.sys.utils.UserUtils;

import cn.hutool.json.JSONUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;

/**
 * 阶段模版Controller
 * <AUTHOR>
 * @version 2021-08-02
 */
@RestController
@Api(tags = "阶段模版")
@RequestMapping(value = "/lawcase/stageTemplate")
public class StageTemplateController extends BaseController {

	@Autowired
	private StageTemplateService stageTemplateService;
	@Autowired
	private StageService stageService;
	@Autowired
	private CaseModelTypeService caseModelTypeService;

	@ModelAttribute
	public StageTemplate get(@RequestParam(required=false) String id) {
		StageTemplate entity = null;
		if (StringUtils.isNotBlank(id)){
			entity = stageTemplateService.get(id);
		}
		if (entity == null){
			entity = new StageTemplate();
		}
		return entity;
	}

	/**
	 * 阶段模版列表数据
	 */
	@RequiresPermissions(value = {"lawlawcase:stageTemplate:list", "user"}, logical=Logical.OR)
	@ApiOperation(value = "模版信息列表", consumes = "application/form-data")
	@ApiImplicitParams({
			@ApiImplicitParam(value = "模版名称", name = "name"),
			@ApiImplicitParam(value = "类型: 1.自定义  2.系统", name = "type")
	})
	@PostMapping("list")
	public AjaxJson list(StageTemplate stageTemplate, HttpServletRequest request, HttpServletResponse response) {
		if(StringUtils.isBlank(stageTemplate.getType())){
			stageTemplate.setType(StageTemplate.TYPE_CUSTOM);
		}
		if(StageTemplate.TYPE_CUSTOM.equals(stageTemplate.getType())){
			stageTemplate.setCreateBy(UserUtils.getUser());
		}
		Page<StageTemplate> page = stageTemplateService.findPage(new Page<StageTemplate>(request, response), stageTemplate);
		return AjaxJson.success().put("page",page);
	}

	/**
	 * 根据Id获取阶段模版数据
	 */
//	@RequiresPermissions(value={"lawcase:stageTemplate:view","lawcase:stageTemplate:add","lawcase:stageTemplate:edit"},logical=Logical.OR)
	@ApiOperation(value = "模版信息详情", consumes = "application/form-data")
	@ApiImplicitParam(value = "模版id值", name = "id", required = true)
	@PostMapping("queryById")
	public AjaxJson queryById(StageTemplate stageTemplate) {
		StageTemplateVO stageTemplateVo = new StageTemplateVO();
		if(StringUtils.isNotBlank(stageTemplate.getName())){
			stageTemplateVo.setId(stageTemplate.getId());
			stageTemplateVo.setName(stageTemplate.getName());
			stageTemplateVo.setType(stageTemplate.getType());
			stageTemplateVo.setIsSystem(stageTemplate.getIsSystem());
			stageTemplateVo.setCaseModelType(stageTemplate.getCaseModelType());
			stageTemplateVo.setSort(stageTemplate.getSort());
			Stage stage = new Stage();
			stage.setStageTemplate(stageTemplate);
			// 获取阶段、阶段待办事项信息
			List<StageVO> stageList = stageService.findListByTemplate(stage);
			stageTemplateVo.setStageList(stageList);
		}
		/* 因使用TreeUtils 泛型的问题 导致 前端返值出现问题，现解决思路：将数据转为jsonStr 再将jsonStr转为 JSONObject 返回前端 */
		String jsonStr = JSONUtil.toJsonStr(stageTemplateVo);
		return AjaxJson.success().put("data", JSONUtil.parseObj(jsonStr));
	}

	/**
	 * 保存阶段模版
	 */
	@RequiresPermissions(value={"lawcase:stageTemplate:add","lawcase:stageTemplate:edit", "user"},logical=Logical.OR)
	@ApiOperation(value = "保存阶段模版")
	@PostMapping("save")
	public AjaxJson save(@RequestBody StageTemplateVO stageTemplateVo) throws Exception{
		/**
		 * 后台hibernate-validation插件校验
		 */
		StageTemplate stageTemplate = CaseConstant.voToStageTemplate(stageTemplateVo);
		String errMsg = beanValidator(stageTemplate);
		if (StringUtils.isNotBlank(errMsg)){
			return AjaxJson.error(errMsg);
		}
		
		if(org.apache.commons.lang3.StringUtils.isBlank(stageTemplateVo.getId())) {
			Integer tsort=stageTemplateService.getMaxSort();
			stageTemplateVo.setSort(tsort+1);
		}
	 
		List<StageVO> stageList = stageTemplateVo.getStageList();
		if(stageList == null || stageList.size() == 0){
			return AjaxJson.error("请填写阶段信息");
		}
		
		CaseModelType caseModelType=caseModelTypeService.get(stageTemplate.getCaseModelType());
        if(caseModelType.getFlag()!=1) {
        	return AjaxJson.error("类型必须是子节点");
        }
        stageTemplateVo.setCaseModelName(caseModelType.getPname());
		try {
			if(StringUtils.isBlank(stageTemplateVo.getType())){
				stageTemplateVo.setType(StageTemplate.TYPE_CUSTOM);
			}
			if(StringUtils.isBlank(stageTemplateVo.getIsSystem())){
				stageTemplateVo.setIsSystem(JeePlusProperites.NO);
			}
			stageTemplateService.save(stageTemplateVo);//保存
			return AjaxJson.success("保存模版成功");
		}catch (RuntimeException e){
			return AjaxJson.error("保存失败,请联系平台技术人员");
		}catch (Exception e) {
			e.printStackTrace();
			return AjaxJson.error("保存失败");
		}
	}


	/**
	 * 批量删除阶段模版
	 */
	@RequiresPermissions(value = {"lawcase:stageTemplate:del", "user"}, logical = Logical.OR)
	@ApiOperation(value = "信息删除", consumes = "application/form-data")
	@PostMapping("delete")
	public AjaxJson delete(String ids) {
		String idArray[] =ids.split(",");
		List<String> temNameList = new ArrayList<>();
		for(String id : idArray){
			StageTemplate stageTemplate = stageTemplateService.get(id);
			if(!JeePlusProperites.YES.equals(stageTemplate.getIsSystem())){
				stageTemplateService.delete(stageTemplate);
			}else {
				temNameList.add(stageTemplate.getName());
			}
		}
		String msg = "删除阶段模版成功";
		if(temNameList.size() > 0){
			msg += "。其中"+ StringUtils.join(temNameList, "，") +"不可以被删除。";
		}

		return AjaxJson.success(msg);
	}
	
	//修改模板排序
	@RequiresPermissions(value = {"lawcase:stageTemplate:del", "user"}, logical = Logical.OR)
	@RequestMapping("updateSort")
	public AjaxJson updateSort(@RequestBody StageTemplateSortVO vo) {
		stageTemplateService.updateSort(vo.getStartId(),vo.getStartSort());
		stageTemplateService.updateSort(vo.getEndId(),vo.getEndSort());
		return AjaxJson.success();
	}

	/**
	 * 导出excel文件
	 */
	@RequiresPermissions("lawcase:stageTemplate:export")
    @GetMapping("export")
    public AjaxJson exportFile(StageTemplate stageTemplate, HttpServletRequest request, HttpServletResponse response) {
		try {
            String fileName = "阶段模版"+DateUtils.getDate("yyyyMMddHHmmss")+".xlsx";
            Page<StageTemplate> page = stageTemplateService.findPage(new Page<StageTemplate>(request, response, -1), stageTemplate);
    		new ExportExcel("阶段模版", StageTemplate.class).setDataList(page.getList()).write(response, fileName).dispose();
    		return null;
		} catch (Exception e) {
			return AjaxJson.error("导出阶段模版记录失败！失败信息："+e.getMessage());
		}
    }

	/**
	 * 导入Excel数据

	 */
	@RequiresPermissions("lawcase:stageTemplate:import")
    @PostMapping("import")
   	public AjaxJson importFile(@RequestParam("file")MultipartFile file, HttpServletResponse response, HttpServletRequest request) {
		try {
			int successNum = 0;
			int failureNum = 0;
			StringBuilder failureMsg = new StringBuilder();
			ImportExcel ei = new ImportExcel(file, 1, 0);
			List<StageTemplate> list = ei.getDataList(StageTemplate.class);
			for (StageTemplate stageTemplate : list){
				try{
					stageTemplateService.save(stageTemplate);
					successNum++;
				}catch(ConstraintViolationException ex){
					failureNum++;
				}catch (Exception ex) {
					failureNum++;
				}
			}
			if (failureNum>0){
				failureMsg.insert(0, "，失败 "+failureNum+" 条阶段模版记录。");
			}
			return AjaxJson.success( "已成功导入 "+successNum+" 条阶段模版记录"+failureMsg);
		} catch (Exception e) {
			return AjaxJson.error("导入阶段模版失败！失败信息："+e.getMessage());
		}
    }

	/**
	 * 下载导入阶段模版数据模板
	 */
	@RequiresPermissions("lawcase:stageTemplate:import")
    @GetMapping("import/template")
     public AjaxJson importFileTemplate(HttpServletResponse response) {
		try {
            String fileName = "阶段模版数据导入模板.xlsx";
    		List<StageTemplate> list = Lists.newArrayList();
    		new ExportExcel("阶段模版数据", StageTemplate.class, 1).setDataList(list).write(response, fileName).dispose();
    		return null;
		} catch (Exception e) {
			return AjaxJson.error( "导入模板下载失败！失败信息："+e.getMessage());
		}
    }

	
	@ApiOperation(value = "模板树结构数据", consumes = "application/form-data")
	@RequiresPermissions("user")
	@RequestMapping("modelTypeTreeData")
	public AjaxJson modelTypeTreeData(@RequestParam(required = false) String extId) {
		List<CaseModelType> list = caseModelTypeService.findList(new CaseModelType());
		List<CaseModelType> list2=new ArrayList<>();
		for(CaseModelType info:list) {
			if(info.getFlag()==1) {
				//查询模板
				StageTemplate st=new StageTemplate();
				st.setCaseModelType(info.getId());
				List<StageTemplate> tlist=stageTemplateService.findList(st);
				if(tlist!=null && tlist.size()>0) {
					for(StageTemplate stinfo:tlist) {
						CaseModelType ctinfo=new CaseModelType();
						ctinfo.setId(stinfo.getId());
						ctinfo.setFlag(1);
						ctinfo.setName(stinfo.getName());
						CaseModelType pinfo=new CaseModelType();
						pinfo.setId(info.getId()); 
						ctinfo.setParent(pinfo);
						ctinfo.setParentIds(info.getParentIds()+info.getId()+",");
						ctinfo.setPname(info.getPname()+"/"+stinfo.getName());
						ctinfo.setSort(1);
						list2.add(ctinfo);
					}
				}
				info.setFlag(0);
			}
			info.setPname(info.getName());
		}
		list.addAll(list2);
		List rootTree =  caseModelTypeService.formatListToTree (new CaseModelType ("0"),list, extId );
		 return AjaxJson.success().put("treeData", rootTree);
	}

}
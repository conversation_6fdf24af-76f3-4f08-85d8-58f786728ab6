/**
 * Copyright © 2015-2020 <a href="http://www.jeeplus.org/">JeePlus</a> All rights reserved.
 */
package com.jeeplus.modules.lawcase.entity;

import com.jeeplus.common.utils.StringUtils;
import com.jeeplus.config.properties.JeePlusProperites;
import com.jeeplus.core.persistence.DataEntity;
import com.jeeplus.modules.sys.utils.FileKit;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 待办事项-附件 Entity
 * <AUTHOR>
 * @version 2021-08-12
 */
@Data 
public class TodoInfoFile extends DataEntity<TodoInfoFile> {

	private static final long serialVersionUID = 1L;
	/** 上传文件保存路径 */
	public static final String FILE_PATH = "/lawcase/todoFile/";

	public static final String TEMPLATE_FILE_PATH = "/lawcase/templateFile/";

	private TodoInfo todoInfo;		// 待办事项
	@NotNull
	private String name;			// 文件名称
	@NotNull
	private String path;			// 文件路径
	
	private Integer sort;
	
	private Integer editFlag;


	// 虚拟
	private String fullPath;	// 完整文件路径
	private String fileType;	// 文件类型
	
	//文件路径
	private String fullFilePath;
	
	public String getFullFilePath() {
		if(StringUtils.isNotBlank(this.path)){
			this.fullFilePath = (JeePlusProperites.getDefultUserfilesBaseDir() + this.path);
		}
		return fullFilePath;
	}

	public TodoInfoFile() {
		super();
	}

	public TodoInfoFile(String id){
		super(id);
	}

	public TodoInfoFile(TodoInfo todoInfo){
		this.todoInfo = todoInfo;
	}

	public String getFullPath() {
		if(StringUtils.isNotBlank(this.path)){
			this.fullPath = (JeePlusProperites.domainNameValue() + this.path);
		}
		return fullPath;
	}

	public String getFileType() {
		this.fileType = FileKit.getFileSuffix(this.path);
		return fileType;
	}
}
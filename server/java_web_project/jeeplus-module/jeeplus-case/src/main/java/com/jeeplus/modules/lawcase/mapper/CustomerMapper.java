/**
 * Copyright © 2015-2020 <a href="http://www.jeeplus.org/">JeePlus</a> All rights reserved.
 */
package com.jeeplus.modules.lawcase.mapper;

import com.jeeplus.modules.lawcase.entity.Customer;
import com.jeeplus.modules.lawcase.entity.CustomerFollowUp;
import org.springframework.stereotype.Repository;
import com.jeeplus.core.persistence.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 *  客户信息MAPPER接口
 * <AUTHOR>
 * @version 2021-08-02
 */
@Mapper
@Repository
public interface CustomerMapper extends BaseMapper<Customer> {
    /**
     * 获取完整信息 客户列表
     * @param customer
     * @return
     */
    List<Customer> findOverallList(Customer customer);

    /**
     * 查询 获取客户跟进人信息
     * @param customerFollowUp
     * @return
     */
    List<CustomerFollowUp> findFollowUpList(CustomerFollowUp customerFollowUp);

    /**
     * 批量插入客户跟进人信息
     * @param list
     * @return
     */
    int insertFollowUp(List<CustomerFollowUp> list);

    /**
     * 根据 客户id 删除客户跟进人信息
     * @param customerId
     * @return
     */
    int deleteFollowUpByCustomer(String customerId);
}
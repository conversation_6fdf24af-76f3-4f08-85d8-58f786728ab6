/**
 * Copyright © 2015-2020 <a href="http://www.jeeplus.org/">JeePlus</a> All rights reserved.
 */
package com.jeeplus.modules.lawcase.web;

import java.util.List;

import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.jeeplus.common.json.AjaxJson;
import com.jeeplus.common.utils.StringUtils;
import com.jeeplus.core.web.BaseController;
import com.jeeplus.modules.lawcase.entity.CaseCause;
import com.jeeplus.modules.lawcase.entity.CaseModelType;
import com.jeeplus.modules.lawcase.service.CaseCauseService;
import com.jeeplus.modules.lawcase.service.CaseModelTypeService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;

/**
 * 案由Controller
 * <AUTHOR>
 * @version 2021-09-04
 */
@Api(tags = "案由")
@RestController
@RequestMapping(value = "/lawcase/caseCause")
public class CaseCauseController extends BaseController {

	@Autowired
	private CaseCauseService caseCauseService;
	
	@Autowired
	private CaseModelTypeService caseModelTypeService;
	

	@ModelAttribute
	public CaseCause get(@RequestParam(required=false) String id) {
		CaseCause entity = null;
		if (StringUtils.isNotBlank(id)){
			entity = caseCauseService.get(id);
		}
		if (entity == null){
			entity = new CaseCause();
		}
		return entity;
	}


	/**
	 * 案由树表数据
	 */
	@RequiresPermissions("lawcase:caseCause:list")
	@GetMapping("list")
	public AjaxJson list(CaseCause caseCause) {
		return AjaxJson.success().put("list", caseCauseService.findList(caseCause));
	}

	/**
	 * 根据Id获取案由数据
	 */
	@ApiOperation(value = "根据id获取案由详情数据", consumes = "application/form-data")
	@ApiImplicitParam(value = "id值", name = "id", required = true)
	@RequiresPermissions(value={"lawcase:caseCause:view","lawcase:caseCause:add","lawcase:caseCause:edit", "user"},logical=Logical.OR)
	@PostMapping("queryById")
	public AjaxJson queryById(CaseCause caseCause) {
		return AjaxJson.success().put("caseCause", caseCause);
	}

	/**
	 * 保存案由
	 */
	@ApiOperation(value = "保存案由数据", consumes = "application/form-data")
	@ApiImplicitParams({
			@ApiImplicitParam(value = "id值", name = "id"),
			@ApiImplicitParam(value = "名称", name = "name", required = true),
			@ApiImplicitParam(value = "排序", name = "sort", required = true),
			@ApiImplicitParam(value = "上级id", name = "parent.id"),
			@ApiImplicitParam(value = "上级名称", name = "parent.name")
	})
	@RequiresPermissions(value={"lawcase:caseCause:add","lawcase:caseCause:edit", "user"},logical=Logical.OR)
	@PostMapping("save")
	public AjaxJson save(CaseCause caseCause) throws Exception{
		/**
		 * 后台hibernate-validation插件校验
		 */
		String errMsg = beanValidator(caseCause);
		if (StringUtils.isNotBlank(errMsg)){
			return AjaxJson.error(errMsg);
		}
		//新增或编辑表单保存
		caseCauseService.save(caseCause);//保存
		return AjaxJson.success("保存案由成功");
	}

	/**
	 * 删除案由
	 */
	@ApiOperation(value = "删除案由数据", consumes = "application/form-data")
	@ApiImplicitParam(value = "id值，多个以,拼接", name = "ids", required = true)
	@RequiresPermissions(value = {"lawcase:caseCause:del", "user"}, logical = Logical.OR)
	@PostMapping("delete")
	public AjaxJson delete(String ids) {
		String idArray[] =ids.split(",");
		for(String id : idArray){
			caseCauseService.delete(new CaseCause(id));
		}
		return AjaxJson.success("删除案由成功");
	}

	/**
     * 获取JSON树形数据。
     * @param extId 排除的ID
     * @return
	*/
	@ApiOperation(value = "案由树结构数据", consumes = "application/form-data")
	@RequiresPermissions("user")
	@PostMapping("treeData")
	public AjaxJson treeData(@RequestParam(required = false) String extId) {
		List<CaseCause> list = caseCauseService.findList(new CaseCause());
		List rootTree =  caseCauseService.formatListToTree (new CaseCause ("0"),list, extId );
		return AjaxJson.success().put("treeData", rootTree);
	}
	
	@ApiOperation(value = "模板树结构数据", consumes = "application/form-data")
	@RequiresPermissions("user")
	@RequestMapping("modelTypeTreeData")
	public AjaxJson modelTypeTreeData(@RequestParam(required = false) String extId) {
		List<CaseModelType> list = caseModelTypeService.findList(new CaseModelType());
		List rootTree =  caseModelTypeService.formatListToTree (new CaseModelType ("0"),list, extId );
		 return AjaxJson.success().put("treeData", rootTree);
	}
	

}
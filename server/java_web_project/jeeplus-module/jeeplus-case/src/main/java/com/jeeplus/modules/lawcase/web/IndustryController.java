/**
 * Copyright © 2015-2020 <a href="http://www.jeeplus.org/">JeePlus</a> All rights reserved.
 */
package com.jeeplus.modules.lawcase.web;

import java.util.List;

import com.jeeplus.modules.lawcase.entity.Industry;
import com.jeeplus.modules.lawcase.service.IndustryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.google.common.collect.Lists;
import com.jeeplus.common.json.AjaxJson;
import com.jeeplus.core.web.BaseController;
import com.jeeplus.common.utils.StringUtils;

/**
 * 行业信息Controller
 * <AUTHOR>
 * @version 2021-08-01
 */
@RestController
@Api(tags = "行业信息")
@RequestMapping(value = "/lawcase/industry")
public class IndustryController extends BaseController {

	@Autowired
	private IndustryService industryService;

	@ModelAttribute
	public Industry get(@RequestParam(required=false) String id) {
		Industry entity = null;
		if (StringUtils.isNotBlank(id)){
			entity = industryService.get(id);
		}
		if (entity == null){
			entity = new Industry();
		}
		return entity;
	}


	/**
	 * 行业信息树表数据
	 */
	@RequiresPermissions("lawcase:industry:list")
	@GetMapping("list")
	public AjaxJson list(Industry industry) {
		return AjaxJson.success().put("list", industryService.findList(industry));
	}

	/**
	 * 根据Id获取行业信息数据
	 */
	@ApiOperation(value = "根据id获取行业信息详情数据", consumes = "application/form-data")
	@ApiImplicitParam(value = "id值", name = "id", required = true)
	@RequiresPermissions(value={"lawcase:industry:view","lawcase:industry:add","lawcase:industry:edit", "user"},logical=Logical.OR)
	@PostMapping("queryById")
	public AjaxJson queryById(Industry industry) {
		return AjaxJson.success().put("industry", industry);
	}

	/**
	 * 保存行业信息
	 */
	@ApiOperation(value = "保存行业信息数据", consumes = "application/form-data")
	@ApiImplicitParams({
			@ApiImplicitParam(value = "id值", name = "id"),
			@ApiImplicitParam(value = "名称", name = "name", required = true),
			@ApiImplicitParam(value = "排序", name = "sort", required = true),
			@ApiImplicitParam(value = "上级id", name = "parent.id"),
			@ApiImplicitParam(value = "上级名称", name = "parent.name"),
	})
	@RequiresPermissions(value={"lawcase:industry:add","lawcase:industry:edit", "user"},logical=Logical.OR)
	@PostMapping("save")
	public AjaxJson save(Industry industry) throws Exception{
		/**
		 * 后台hibernate-validation插件校验
		 */
		String errMsg = beanValidator(industry);
		if (StringUtils.isNotBlank(errMsg)){
			return AjaxJson.error(errMsg);
		}
		//新增或编辑表单保存
		industryService.save(industry);//保存
		return AjaxJson.success("保存行业信息成功");
	}

	/**
	 * 删除行业信息
	 */
	@ApiOperation(value = "删除行业信息数据", consumes = "application/form-data")
	@ApiImplicitParam(value = "id值，多个以,拼接", name = "ids", required = true)
	@RequiresPermissions(value = {"lawcase:industry:del", "user"}, logical = Logical.OR)
	@PostMapping("delete")
	public AjaxJson delete(String ids) {
		String idArray[] =ids.split(",");
		for(String id : idArray){
			industryService.delete(new Industry(id));
		}
		return AjaxJson.success("删除行业信息成功");
	}

	/**
     * 获取JSON树形数据。
     * @param extId 排除的ID
     * @return
	*/
	@ApiOperation(value = "树结构数据")
	@RequiresPermissions("user")
	@GetMapping("treeData")
	public AjaxJson treeData(@RequestParam(required = false) String extId) {
		List<Industry> list = industryService.findList(new Industry());
		List rootTree =  industryService.formatListToTree (new Industry ("0"),list, extId );
		return AjaxJson.success().put("treeData", rootTree);
	}

}
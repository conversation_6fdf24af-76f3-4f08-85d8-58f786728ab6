<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jeeplus.modules.lawcase.mapper.FinanceFlowFileMapper">

	<sql id="financeFlowFileColumns">
		a.id AS "id",
		a.create_by AS "createBy.id",
		a.create_date AS "createDate",
		a.remarks AS "remarks",
		a.del_flag AS "delFlag",
		a.name AS "name",
		a.path AS "path",
		a.finance_flow_id AS "financeFlowRecord.id"
	</sql>




	<sql id="financeFlowFileJoins">

	</sql>

	<select id="get" resultType="TodoInfoFile">
		SELECT
			<include refid="financeFlowFileColumns"/>
		FROM law_finance_flow_file a
		<include refid="financeFlowFileJoins"/>
		WHERE a.id = #{id}
	</select>

	<select id="findList" resultType="TodoInfoFile">
		SELECT
			<include refid="financeFlowFileColumns"/>
		FROM law_finance_flow_file a
		<include refid="financeFlowFileJoins"/>
		<where>
			a.del_flag = #{DEL_FLAG_NORMAL}
			${dataScope}
			<if test="financeFlowRecord != null and financeFlowRecord.id != null and financeFlowRecord.id != ''">
				AND a.finance_flow_id = #{financeFlowRecord.id}
			</if>
		</where>
		ORDER BY a.create_date
	</select>

	<select id="findAllList" resultType="TodoInfoFile">
		SELECT
			<include refid="financeFlowFileColumns"/>
		FROM law_finance_flow_file a
		<include refid="financeFlowFileJoins"/>
		<where>
			a.del_flag = #{DEL_FLAG_NORMAL}
			${dataScope}
		</where>
		ORDER BY a.create_date ASC
	</select>



	<insert id="insert">
		INSERT INTO law_finance_flow_file(
			   id,
			   create_by,
			   create_date,
			   remarks,
			   del_flag,
			   `name`,
			   path,
			   finance_flow_id
		) VALUES (
			#{id},
			#{createBy.id},
			#{createDate},
			#{remarks},
			#{delFlag},
			#{name},
			#{path},
			#{financeFlowRecord.id}
		)
	</insert>

	<insert id="insertBatch">
		INSERT INTO law_finance_flow_file(
			   id,
			   create_by,
			   create_date,
			   remarks,
			   del_flag,
				`name`,
				path,
				finance_flow_id
		) VALUES
		<foreach collection="list" item="item" separator=" , ">
			(
			#{item.id},
			#{item.createBy.id},
			#{item.createDate},
			#{item.remarks},
			#{item.delFlag},
			#{item.name},
			#{item.path},
			#{item.financeFlowRecord.id}
			)
		</foreach>

	</insert>

	<update id="update">
		UPDATE law_finance_flow_file SET
			remarks = #{remarks},
			`name` = #{name},
			path = #{path},
			finance_flow_id = #{financeFlowRecord.id}
		WHERE id = #{id}
	</update>

	<!--物理删除-->
	<update id="delete">
		DELETE FROM law_finance_flow_file WHERE id = #{id}
	</update>

	<update id="deleteByFinanceFlow">
		DELETE FROM law_finance_flow_file WHERE finance_flow_id = #{financeFlowId}
	</update>

	<!--逻辑删除-->
	<update id="deleteByLogic">
		UPDATE law_finance_flow_file SET del_flag = #{DEL_FLAG_DELETE}
		WHERE id = #{id}
	</update>

</mapper>
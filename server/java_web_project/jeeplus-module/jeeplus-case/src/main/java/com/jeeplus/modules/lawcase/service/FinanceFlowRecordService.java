/**
 * Copyright © 2015-2020 <a href="http://www.jeeplus.org/">JeePlus</a> All rights reserved.
 */
package com.jeeplus.modules.lawcase.service;

import java.util.ArrayList;
import java.util.List;

import com.jeeplus.common.utils.StringUtils;
import com.jeeplus.modules.lawcase.entity.FinanceFlowFile;
import com.jeeplus.modules.lawcase.mapper.FinanceFlowFileMapper;
import com.jeeplus.modules.lawcase.vo.CaseFinanceVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.jeeplus.core.persistence.Page;
import com.jeeplus.core.service.CrudService;
import com.jeeplus.database.datasource.annotation.DS;
import com.jeeplus.modules.lawcase.entity.FinanceFlowRecord;
import com.jeeplus.modules.lawcase.mapper.FinanceFlowRecordMapper;

/**
 * 财务流水记录Service
 * <AUTHOR>
 * @version 2021-10-29
 */
@DS("law_case")
@Service
@Transactional(readOnly = true)
public class FinanceFlowRecordService extends CrudService<FinanceFlowRecordMapper, FinanceFlowRecord> {

	@Autowired
	private FinanceFlowFileMapper financeFlowFileMapper;

	public FinanceFlowRecord get(String id) {
		return super.get(id);
	}

	/**
	 * 获取流水记录、流水附件信息
	 * @param id
	 * @return
	 */
	public FinanceFlowRecord getInfo(String id) {
		FinanceFlowRecord financeFlowRecord = super.get(id);
		if(financeFlowRecord == null){
			financeFlowRecord = new FinanceFlowRecord();
		}
		if(StringUtils.isNotBlank(financeFlowRecord.getId())){
			List<FinanceFlowFile> fileList = financeFlowFileMapper.findList(new FinanceFlowFile(financeFlowRecord));
			if(fileList == null){
				fileList = new ArrayList<>();
			}
			financeFlowRecord.setFileList(fileList);
		}
		return financeFlowRecord;
	}

	public List<FinanceFlowRecord> findList(FinanceFlowRecord financeFlowRecord) {
		return super.findList(financeFlowRecord);
	}
	
	public Page<FinanceFlowRecord> findPage(Page<FinanceFlowRecord> page, FinanceFlowRecord financeFlowRecord) {
		return super.findPage(page, financeFlowRecord);
	}

	/**
	 * 查询流水记录、对应的附件信息
	 * @param page
	 * @param financeFlowRecord
	 * @return
	 */
	public Page<FinanceFlowRecord> findInfoPage(Page<FinanceFlowRecord> page, FinanceFlowRecord financeFlowRecord) {
		page = super.findPage(page, financeFlowRecord);
		if (page.getList() != null && page.getList().size() > 0){
			for (FinanceFlowRecord flowRecord : page.getList()) {
				List<FinanceFlowFile> fileList = financeFlowFileMapper.findList(new FinanceFlowFile(flowRecord));
				if(fileList == null){
					fileList = new ArrayList<>();
				}
				flowRecord.setFileList(fileList);
			}

		}
		return page;
	}

	/**
	 * 根据案件id 查询案件财务统计情况
	 * @param caseId
	 * @return
	 */
	public CaseFinanceVO getCaseFinance(String caseId){
		return  mapper.getCaseFinance(caseId);
	}

	/**
	 * 案件财务信息统计
	 * @param page
	 * @param caseFinance
	 * @return
	 */
	public Page<CaseFinanceVO> findCaseFinancePage(Page<CaseFinanceVO> page, CaseFinanceVO caseFinance) {
		dataRuleFilter(caseFinance);
		caseFinance.setPage(page);
		page.setList(mapper.findCaseFinanceList(caseFinance));
		return page;
	}

	@Transactional(readOnly = false)
	public void save(FinanceFlowRecord financeFlowRecord) {
		super.save(financeFlowRecord);
	}

	/**
	 * 批量保存 流水数据、流水记录对应附件
	 * @param financeFlowRecordList
	 */
	@Transactional(readOnly = false)
	public void batchSaveInfo(List<FinanceFlowRecord> financeFlowRecordList) {
		for (FinanceFlowRecord financeFlowRecord : financeFlowRecordList) {
			this.saveInfo(financeFlowRecord);
		}
	}

	/**
	 * 保存流水数据、流水记录对应附件
	 * @param financeFlowRecord
	 */
	@Transactional(readOnly = false)
	public void saveInfo(FinanceFlowRecord financeFlowRecord) {
		boolean isFirst = StringUtils.isBlank(financeFlowRecord.getId());
		super.save(financeFlowRecord);

		// 流水记录附件 先删除后添加
		if(!isFirst){
			financeFlowFileMapper.deleteByFinanceFlow(financeFlowRecord.getId());
		}
		List<FinanceFlowFile> fileList = financeFlowRecord.getFileList();
		if(fileList != null && fileList.size() > 0){
			for (FinanceFlowFile financeFlowFile : fileList) {
				financeFlowFile.setFinanceFlowRecord(financeFlowRecord);
				if(StringUtils.isBlank(financeFlowFile.getId())){
					financeFlowFile.preInsert();
				}else {
					financeFlowFile.preUpdate();
					financeFlowFile.setCreateBy(financeFlowFile.getUpdateBy());
					financeFlowFile.setCreateDate(financeFlowFile.getUpdateDate());
				}
			}
			financeFlowFileMapper.insertBatch(fileList);
		}
	}

	@Transactional(readOnly = false)
	public void delete(FinanceFlowRecord financeFlowRecord) {
		super.delete(financeFlowRecord);
		financeFlowFileMapper.deleteByFinanceFlow(financeFlowRecord.getId());
	}
	
}
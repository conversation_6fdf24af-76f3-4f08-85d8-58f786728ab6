/**
 * Copyright © 2015-2020 <a href="http://www.jeeplus.org/">JeePlus</a> All rights reserved.
 */
package com.jeeplus.modules.lawcase.entity;


import com.jeeplus.core.persistence.DataEntity;
import com.jeeplus.common.utils.excel.annotation.ExcelField;
import com.jeeplus.modules.sys.utils.FileKit;
import lombok.Data;

/**
 * 案件文档Entity
 * <AUTHOR>
 * @version 2021-08-13
 */
@Data
public class CaseFile extends DataEntity<CaseFile> {
	
	private static final long serialVersionUID = 1L;
	/** 上传文件保存路径 */
	public static final String FILE_PATH = "/lawcase/caseFile/";

	@ExcelField(title="文件名", align=2, sort=7)
	private String name;		// 文件名
	@ExcelField(title="所属目录", align=2, sort=8)
	private CaseFileDirectory fileDirectory;		// 所属目录
	@ExcelField(title="上传路径", align=2, sort=9)
	private String path;		// 上传路径
	private Case lawCase;		// 案件信息

	// 虚拟
	private String fileType;	// 文件类型
	private String queryFileDirectoryId;	// 查询文件目录id

	public CaseFile() {
		super();
	}
	
	public CaseFile(String id){
		super(id);
	}

	public String getFileType() {
		this.fileType = FileKit.getFileSuffix(this.path);
		return fileType;
	}
}
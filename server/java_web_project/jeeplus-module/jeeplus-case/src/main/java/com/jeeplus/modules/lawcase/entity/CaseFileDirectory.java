/**
 * Copyright © 2015-2020 <a href="http://www.jeeplus.org/">JeePlus</a> All rights reserved.
 */
package com.jeeplus.modules.lawcase.entity;

import com.fasterxml.jackson.annotation.JsonBackReference;
import javax.validation.constraints.NotNull;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import com.jeeplus.core.persistence.TreeEntity;
import lombok.Data;

/**
 * 案件文档目录Entity
 * <AUTHOR>
 * @version 2021-08-12
 */
@Data 
@JsonIgnoreProperties(value={"hibernateLazyInitializer","handler"})
public class CaseFileDirectory extends TreeEntity<CaseFileDirectory> {
	
	private static final long serialVersionUID = 1L;
	private Case lawCase;		// 案件id

	// 虚拟
	private String queryAccurateName;	// 查询 精确查找名称
	
	public CaseFileDirectory() {
		super();
	}

	public CaseFileDirectory(String id){
		super(id);
	}

	public  CaseFileDirectory getParent() {
			return parent;
	}
	
	@Override
	public void setParent(CaseFileDirectory parent) {
		this.parent = parent;
		
	}
	
	public String getParentId() {
		return parent != null && parent.getId() != null ? parent.getId() : "0";
	}
}
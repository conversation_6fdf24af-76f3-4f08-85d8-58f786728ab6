/**
 * Copyright © 2015-2020 <a href="http://www.jeeplus.org/">JeePlus</a> All rights reserved.
 */
package com.jeeplus.modules.lawcase.web;

import java.io.File;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.ConstraintViolationException;

import com.jeeplus.common.utils.FileUtils;
import com.jeeplus.modules.lawcase.constant.CaseConstant;
import com.jeeplus.modules.lawcase.entity.Case;
import com.jeeplus.modules.lawcase.entity.CaseFileDirectory;
import com.jeeplus.modules.lawcase.service.CaseFileDirectoryService;
import com.jeeplus.modules.lawcase.service.CaseService;
import com.jeeplus.modules.office.entity.DocTemplate;
import com.jeeplus.modules.office.service.DocTemplateService;
import com.jeeplus.modules.sys.utils.FileKit;
import com.jeeplus.modules.wps.model.ConstantModel;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import oracle.jdbc.proxy.annotation.Post;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.google.common.collect.Lists;
import com.jeeplus.common.utils.DateUtils;
import com.jeeplus.common.json.AjaxJson;
import com.jeeplus.core.persistence.Page;
import com.jeeplus.core.web.BaseController;
import com.jeeplus.common.utils.StringUtils;
import com.jeeplus.common.utils.excel.ExportExcel;
import com.jeeplus.common.utils.excel.ImportExcel;
import com.jeeplus.modules.lawcase.entity.CaseFile;
import com.jeeplus.modules.lawcase.service.CaseFileService;

/**
 * 案件文档Controller
 * <AUTHOR>
 * @version 2021-08-13
 */
@RestController
@Api(tags = "案件-文档")
@RequestMapping(value = "/lawcase/caseFile")
public class CaseFileController extends BaseController {

	@Autowired
	private CaseService caseService;
	@Autowired
	private CaseFileService caseFileService;
	@Autowired
	private CaseFileDirectoryService caseFileDirectoryService;
	@Autowired
	private DocTemplateService docTemplateService;

	@ModelAttribute
	public CaseFile get(@RequestParam(required=false) String id) {
		CaseFile entity = null;
		if (StringUtils.isNotBlank(id)){
			entity = caseFileService.get(id);
		}
		if (entity == null){
			entity = new CaseFile();
		}
		return entity;
	}

	/**
	 * 文档列表数据
	 */
	@RequiresPermissions(value = {"lawcase:caseFile:list", "user"}, logical = Logical.OR)
	@ApiOperation(value = "文件列表", consumes = "application/form-data")
	@ApiImplicitParams({
			@ApiImplicitParam(value = "案件id",name = "lawCase.id", required = true),
			@ApiImplicitParam(value = "文件目录id， 若目录为空 则默认查询案件根目录",name = "fileDirectory.id"),
			@ApiImplicitParam(value = "文件名",name = "name")
	})
	@PostMapping("list")
	public AjaxJson list(CaseFile caseFile, HttpServletRequest request, HttpServletResponse response) {
		Page<CaseFile> page = new Page<CaseFile>(request, response);
		if(caseFile.getLawCase() != null && StringUtils.isNotBlank(caseFile.getLawCase().getId())){
			// 若目录不存在 则查询根目录
			if(caseFile.getFileDirectory() == null || StringUtils.isBlank(caseFile.getFileDirectory().getId())){
				caseFile.setFileDirectory( caseFileDirectoryService.getRootByCase(caseFile.getLawCase().getId()) );
			}
			page = caseFileService.findPage(page, caseFile);
		}
		return AjaxJson.success().put("page",page);
	}

	/**
	 * 根据Id获取文档数据
	 */
	@RequiresPermissions(value={"lawcase:caseFile:view","lawcase:caseFile:add","lawcase:caseFile:edit"},logical=Logical.OR)
	@GetMapping("queryById")
	public AjaxJson queryById(CaseFile caseFile) {
		return AjaxJson.success().put("caseFile", caseFile);
	}


	@RequiresPermissions(value={"lawcase:caseFile:add","lawcase:caseFile:edit", "user"},logical=Logical.OR)
	@ApiOperation(value = "模版使用", consumes = "application/form-data")
	@ApiImplicitParams({
			@ApiImplicitParam(value = "案件id",name = "lawCaseId", required = true),
			@ApiImplicitParam(value = "文件目录id  若为空默认根目录",name = "fileDirectoryId"),
			@ApiImplicitParam(value = "模版id值", name = "templateId", required = true)
	})
	@PostMapping("templateCopy")
	public AjaxJson templateCopy(String lawCaseId, String fileDirectoryId, String templateId) throws Exception{
		if(StringUtils.isBlank(lawCaseId) || StringUtils.isBlank(templateId)){
			return AjaxJson.error("参数错误");
		}
		CaseFileDirectory fileDirectory = new CaseFileDirectory();
		if(StringUtils.isBlank(fileDirectoryId)){
			fileDirectory = caseFileDirectoryService.getRootByCase(lawCaseId);
		}
		if(fileDirectory == null || StringUtils.isBlank(fileDirectory.getId())){
			return AjaxJson.error("请选择上传目录");
		}
		DocTemplate docTemplate = docTemplateService.get(templateId);
		if(docTemplate == null || StringUtils.isBlank(docTemplate.getId())){
			return AjaxJson.error("模版信息有误");
		}
		if(StringUtils.isBlank(docTemplate.getPath())){
			return AjaxJson.error("模版文件不存在！");
		}
		try {
			AjaxJson j = FileKit.fileCopy(FileKit.getFileDir(docTemplate.getPath()), CaseFile.FILE_PATH);
			if(!j.isSuccess()){
				return j;
			}
			CaseFile caseFile = new CaseFile();
			caseFile.setFileDirectory(fileDirectory);
			caseFile.setLawCase(new Case(lawCaseId));
			caseFile.setPath( j.get("url").toString());
			caseFile.setName( String.valueOf(j.get("name")) );
			caseFileService.save(caseFile);//保存
			j.setMsg("使用成功");
			return j;
		} catch (Exception e) {
			e.printStackTrace();
			return AjaxJson.error("使用失败");
		}
	}

	@RequiresPermissions(value={"lawcase:caseFile:add","lawcase:caseFile:edit", "user"},logical=Logical.OR)
	@ApiOperation(value = "文件新建", consumes = "application/form-data")
	@ApiImplicitParams({
			@ApiImplicitParam(value = "案件id",name = "lawCaseId", required = true),
			@ApiImplicitParam(value = "文件目录id  若为空默认根目录",name = "fileDirectoryId"),
			@ApiImplicitParam(value = "文件类型 s:Excel文件  p:PPT文件  w:Word文件 ", name = "type", required = true)
	})
	@PostMapping("create")
	public AjaxJson fileNew(@RequestParam("type") String type, String lawCaseId, String fileDirectoryId) throws Exception{
		if(StringUtils.isBlank(lawCaseId) || StringUtils.isBlank(type)){
			return AjaxJson.error("参数错误");
		}
		List<String> typeList = Arrays.asList(ConstantModel.TYPE_EXCEL, ConstantModel.TYPE_WORD, ConstantModel.TYPE_PPT);
		if(!typeList.contains(type)){
			return AjaxJson.error("文件类型错误！");
		}
		CaseFileDirectory fileDirectory = new CaseFileDirectory();
		if(StringUtils.isBlank(fileDirectoryId)){
			fileDirectory = caseFileDirectoryService.getRootByCase(lawCaseId);
		}
		if(fileDirectory == null || StringUtils.isBlank(fileDirectory.getId())){
			return AjaxJson.error("请选择上传目录");
		}
		// 文件创建
		AjaxJson j = CaseConstant.fileNew(type, CaseFile.FILE_PATH, "");
		if(!j.isSuccess()){
			return  j;
		}
		try {
			CaseFile caseFile = new CaseFile();
			caseFile.setFileDirectory(fileDirectory);
			caseFile.setLawCase(new Case(lawCaseId));
			caseFile.setPath( j.get("url").toString());
			caseFile.setName( String.valueOf(j.get("name")) );
			caseFileService.save(caseFile);//保存
			j.setMsg("创建成功");
			return j;
		} catch (Exception e) {
			e.printStackTrace();
			return AjaxJson.error("创建失败");
		}
	}

	/**
	 * 保存文档
	 */
	@RequiresPermissions(value={"lawcase:caseFile:add","lawcase:caseFile:edit", "user"},logical=Logical.OR)
	@ApiOperation(value = "名称修改", consumes = "application/form-data")
	@ApiImplicitParams({
			@ApiImplicitParam(value = "文件记录id值", name = "id", required = true),
			@ApiImplicitParam(value = "名称", name = "name", required = true)
	})
	@PostMapping("save")
	public AjaxJson save(String id, String name) throws Exception{
		if(StringUtils.isBlank(id) || StringUtils.isBlank(name)){
			return AjaxJson.error("参数错误");
		}
		CaseFile caseFile = caseFileService.get(id);
		if(caseFile == null || StringUtils.isBlank(caseFile.getId())){
			return AjaxJson.error("文件信息有误");
		}
		caseFile.setName(name);

		try {
			caseFileService.save(caseFile);//保存
			return AjaxJson.success("操作成功");
		} catch (Exception e) {
			e.printStackTrace();
			return AjaxJson.error("操作失败");
		}
	}

	@RequiresPermissions(value={"lawcase:caseFile:add","lawcase:caseFile:edit", "user"},logical=Logical.OR)
	@ApiOperation(value = "文件移动", consumes = "application/form-data")
	@ApiImplicitParams({
			@ApiImplicitParam(value = "文件记录id值", name = "ids", required = true),
			@ApiImplicitParam(value = "案件id", name = "caseId", required = true),
			@ApiImplicitParam(value = "文件目录id，为空时默认根目录", name = "dirId")
	})
	@PostMapping("move")
	public AjaxJson move(String ids, String caseId, String dirId) throws Exception{
		if(StringUtils.isBlank(ids) || StringUtils.isBlank(caseId)){
			return AjaxJson.error("参数错误");
		}
		Case lawCase = caseService.get(caseId);
		if(lawCase == null || StringUtils.isBlank(lawCase.getId())){
			return AjaxJson.error("案件信息有误");
		}
		CaseFileDirectory fileDirectory = new CaseFileDirectory(dirId);
		if(StringUtils.isBlank(dirId)){
			fileDirectory = caseFileDirectoryService.getRootByCase(lawCase.getId());
		}

		for (String id : ids.split(",")) {
			CaseFile caseFile = caseFileService.get(id);
			if(caseFile != null && StringUtils.isNotBlank(caseFile.getId())){
				caseFile.setFileDirectory(fileDirectory);
				caseFileService.save(caseFile);//保存
			}
		}
		return AjaxJson.success("移动成功");
	}

	@RequiresPermissions(value={"lawcase:caseFile:add","lawcase:caseFile:edit", "user"},logical=Logical.OR)
	@ApiOperation(value = "文件上传", consumes = "application/form-data")
	@ApiImplicitParams({
			@ApiImplicitParam(value = "文件",name = "file", required = true),
			@ApiImplicitParam(value = "案件id",name = "lawCase.id", required = true),
			@ApiImplicitParam(value = "文件目录id",name = "fileDirectory.id")
	})
	@PostMapping("upload")
	public AjaxJson upload(CaseFile caseFile, MultipartFile file) throws Exception{
		if(caseFile.getLawCase() == null || StringUtils.isBlank(caseFile.getLawCase().getId())){
			return AjaxJson.error("案件信息有误");
		}
		if(caseFile.getFileDirectory() == null || StringUtils.isBlank(caseFile.getFileDirectory().getId())){
			caseFile.setFileDirectory( caseFileDirectoryService.getRootByCase(caseFile.getLawCase().getId()) );
		}
		if(caseFile.getFileDirectory() == null || StringUtils.isBlank(caseFile.getFileDirectory().getId())){
			return AjaxJson.error("请选择上传目录");
		}
		try {
			AjaxJson j = FileKit.fileUpload(file, CaseFile.FILE_PATH);
			if(!j.isSuccess()){
				return j;
			}
			caseFile.setPath( j.get("url").toString());
			caseFile.setName( String.valueOf(j.get("name")) );
			caseFileService.save(caseFile);//保存
			return AjaxJson.success("上传成功");
		} catch (Exception e) {
			e.printStackTrace();
			return AjaxJson.error("上传失败");
		}
	}

	@RequiresPermissions(value={"lawcase:caseFile:add","lawcase:caseFile:edit", "user"},logical=Logical.OR)
	@ApiOperation(value = "文件夹上传", consumes = "application/form-data")
	@ApiImplicitParams({
			@ApiImplicitParam(value = "文件数组",name = "file", required = true),
			@ApiImplicitParam(value = "案件id",name = "lawCaseId", required = true),
			@ApiImplicitParam(value = "文件目录id",name = "fileDirectoryId")
	})
	@PostMapping("uploadDir")
	public AjaxJson uploadDir(HttpServletRequest request, @RequestParam MultipartFile[] file) throws Exception{
		String lawCaseId = request.getParameter("lawCaseId");
		String fileDirectoryId = request.getParameter("fileDirectoryId");
		if(StringUtils.isBlank(lawCaseId) || file == null || file.length == 0){
			return AjaxJson.error("参数错误");
		}
		// 父级文件夹目录
		CaseFileDirectory parentDir = null;
		if(StringUtils.isNotBlank(fileDirectoryId)){
			parentDir = caseFileDirectoryService.get(fileDirectoryId);
		}else {
			parentDir = caseFileDirectoryService.getRootByCase(lawCaseId);
		}
		if(parentDir == null || StringUtils.isBlank(parentDir.getId())){
			return AjaxJson.error("请选择上传目录");
		}
		parentDir.setLawCase(new Case(lawCaseId));

		// 文件保存
		List<String> errFileNameList = new ArrayList<>();
		for (MultipartFile mf : file) {
			if(!mf.isEmpty()){
				String originalFilename = mf.getOriginalFilename();
				if(StringUtils.isNotBlank(originalFilename)){
					try {
						caseFileDirectoryService.saveInfo(mf, parentDir);
					} catch (Exception e) {
						// 失败信息
						e.printStackTrace();
						errFileNameList.add(originalFilename);
					}
				}
			}
		}
		String msg = "上传成功";
		if(errFileNameList.size() > 0){
			msg += ("</br>其中"+ StringUtils.join(errFileNameList, "，") +"文件上传失败");
		}
		return AjaxJson.success(msg);
	}

	/**
	 * 批量删除文档
	 */
	@RequiresPermissions(value = {"lawcase:caseFile:del", "user"}, logical = Logical.OR)
	@ApiOperation(value = "删除", consumes = "application/form-data")
	@ApiImplicitParam(value = "文件id 多个以,拼接",name = "ids", required = true)
	@PostMapping("delete")
	public AjaxJson delete(String ids) {
		String idArray[] =ids.split(",");
		for(String id : idArray){
			caseFileService.delete( caseFileService.get(id));
		}
		return AjaxJson.success("删除文档成功");
	}

	/**
	 * 导出excel文件
	 */
	@RequiresPermissions("lawcase:caseFile:export")
    @GetMapping("export")
    public AjaxJson exportFile(CaseFile caseFile, HttpServletRequest request, HttpServletResponse response) {
		try {
            String fileName = "文档"+DateUtils.getDate("yyyyMMddHHmmss")+".xlsx";
            Page<CaseFile> page = caseFileService.findPage(new Page<CaseFile>(request, response, -1), caseFile);
    		new ExportExcel("文档", CaseFile.class).setDataList(page.getList()).write(response, fileName).dispose();
    		return null;
		} catch (Exception e) {
			return AjaxJson.error("导出文档记录失败！失败信息："+e.getMessage());
		}
    }

	/**
	 * 导入Excel数据

	 */
	@RequiresPermissions("lawcase:caseFile:import")
    @PostMapping("import")
   	public AjaxJson importFile(@RequestParam("file")MultipartFile file, HttpServletResponse response, HttpServletRequest request) {
		try {
			int successNum = 0;
			int failureNum = 0;
			StringBuilder failureMsg = new StringBuilder();
			ImportExcel ei = new ImportExcel(file, 1, 0);
			List<CaseFile> list = ei.getDataList(CaseFile.class);
			for (CaseFile caseFile : list){
				try{
					caseFileService.save(caseFile);
					successNum++;
				}catch(ConstraintViolationException ex){
					failureNum++;
				}catch (Exception ex) {
					failureNum++;
				}
			}
			if (failureNum>0){
				failureMsg.insert(0, "，失败 "+failureNum+" 条文档记录。");
			}
			return AjaxJson.success( "已成功导入 "+successNum+" 条文档记录"+failureMsg);
		} catch (Exception e) {
			return AjaxJson.error("导入文档失败！失败信息："+e.getMessage());
		}
    }

	/**
	 * 下载导入文档数据模板
	 */
	@RequiresPermissions("lawcase:caseFile:import")
    @GetMapping("import/template")
     public AjaxJson importFileTemplate(HttpServletResponse response) {
		try {
            String fileName = "文档数据导入模板.xlsx";
    		List<CaseFile> list = Lists.newArrayList();
    		new ExportExcel("文档数据", CaseFile.class, 1).setDataList(list).write(response, fileName).dispose();
    		return null;
		} catch (Exception e) {
			return AjaxJson.error( "导入模板下载失败！失败信息："+e.getMessage());
		}
    }


}
/**
 * Copyright © 2015-2020 <a href="http://www.jeeplus.org/">JeePlus</a> All rights reserved.
 */
package com.jeeplus.modules.lawcase.web;

import java.util.List;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.ConstraintViolationException;

import com.jeeplus.modules.lawcase.entity.Stage;
import com.jeeplus.modules.lawcase.service.StageService;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.google.common.collect.Lists;
import com.jeeplus.common.utils.DateUtils;
import com.jeeplus.common.json.AjaxJson;
import com.jeeplus.core.persistence.Page;
import com.jeeplus.core.web.BaseController;
import com.jeeplus.common.utils.StringUtils;
import com.jeeplus.common.utils.excel.ExportExcel;
import com.jeeplus.common.utils.excel.ImportExcel;

/**
 * 阶段信息Controller
 * <AUTHOR>
 * @version 2021-08-02
 */
@RestController
@RequestMapping(value = "/case/stage")
public class StageController extends BaseController {

	@Autowired
	private StageService stageService;

	@ModelAttribute
	public Stage get(@RequestParam(required=false) String id) {
		Stage entity = null;
		if (StringUtils.isNotBlank(id)){
			entity = stageService.get(id);
		}
		if (entity == null){
			entity = new Stage();
		}
		return entity;
	}

	/**
	 * 阶段信息列表数据
	 */
	@RequiresPermissions("case:stage:list")
	@GetMapping("list")
	public AjaxJson list(Stage stage, HttpServletRequest request, HttpServletResponse response) {
		Page<Stage> page = stageService.findPage(new Page<Stage>(request, response), stage);
		return AjaxJson.success().put("page",page);
	}

	/**
	 * 根据Id获取阶段信息数据
	 */
	@RequiresPermissions(value={"case:stage:view","case:stage:add","case:stage:edit"},logical=Logical.OR)
	@GetMapping("queryById")
	public AjaxJson queryById(Stage stage) {
		return AjaxJson.success().put("stage", stage);
	}

	/**
	 * 保存阶段信息
	 */
	@RequiresPermissions(value={"case:stage:add","case:stage:edit"},logical=Logical.OR)
	@PostMapping("save")
	public AjaxJson save(Stage stage, Model model) throws Exception{
		/**
		 * 后台hibernate-validation插件校验
		 */
		String errMsg = beanValidator(stage);
		if (StringUtils.isNotBlank(errMsg)){
			return AjaxJson.error(errMsg);
		}
		//新增或编辑表单保存
		stageService.save(stage);//保存
		return AjaxJson.success("保存阶段信息成功");
	}


	/**
	 * 批量删除阶段信息
	 */
	@RequiresPermissions("case:stage:del")
	@DeleteMapping("delete")
	public AjaxJson delete(String ids) {
		String idArray[] =ids.split(",");
		for(String id : idArray){
			stageService.delete(stageService.get(id));
		}
		return AjaxJson.success("删除阶段信息成功");
	}

	/**
	 * 导出excel文件
	 */
	@RequiresPermissions("case:stage:export")
    @GetMapping("export")
    public AjaxJson exportFile(Stage stage, HttpServletRequest request, HttpServletResponse response) {
		try {
            String fileName = "阶段信息"+DateUtils.getDate("yyyyMMddHHmmss")+".xlsx";
            Page<Stage> page = stageService.findPage(new Page<Stage>(request, response, -1), stage);
    		new ExportExcel("阶段信息", Stage.class).setDataList(page.getList()).write(response, fileName).dispose();
    		return null;
		} catch (Exception e) {
			return AjaxJson.error("导出阶段信息记录失败！失败信息："+e.getMessage());
		}
    }

	/**
	 * 导入Excel数据

	 */
	@RequiresPermissions("case:stage:import")
    @PostMapping("import")
   	public AjaxJson importFile(@RequestParam("file")MultipartFile file, HttpServletResponse response, HttpServletRequest request) {
		try {
			int successNum = 0;
			int failureNum = 0;
			StringBuilder failureMsg = new StringBuilder();
			ImportExcel ei = new ImportExcel(file, 1, 0);
			List<Stage> list = ei.getDataList(Stage.class);
			for (Stage stage : list){
				try{
					stageService.save(stage);
					successNum++;
				}catch(ConstraintViolationException ex){
					failureNum++;
				}catch (Exception ex) {
					failureNum++;
				}
			}
			if (failureNum>0){
				failureMsg.insert(0, "，失败 "+failureNum+" 条阶段信息记录。");
			}
			return AjaxJson.success( "已成功导入 "+successNum+" 条阶段信息记录"+failureMsg);
		} catch (Exception e) {
			return AjaxJson.error("导入阶段信息失败！失败信息："+e.getMessage());
		}
    }

	/**
	 * 下载导入阶段信息数据模板
	 */
	@RequiresPermissions("case:stage:import")
    @GetMapping("import/template")
     public AjaxJson importFileTemplate(HttpServletResponse response) {
		try {
            String fileName = "阶段信息数据导入模板.xlsx";
    		List<Stage> list = Lists.newArrayList();
    		new ExportExcel("阶段信息数据", Stage.class, 1).setDataList(list).write(response, fileName).dispose();
    		return null;
		} catch (Exception e) {
			return AjaxJson.error( "导入模板下载失败！失败信息："+e.getMessage());
		}
    }


}
/**
 * Copyright © 2015-2020 <a href="http://www.jeeplus.org/">JeePlus</a> All rights reserved.
 */
package com.jeeplus.modules.lawcase.service;

import java.util.List;

import com.google.common.collect.Lists;
import com.jeeplus.common.utils.StringUtils;
import com.jeeplus.modules.lawcase.constant.CaseConstant;
import com.jeeplus.modules.lawcase.entity.Stage;
import com.jeeplus.modules.lawcase.entity.StageRecord;
import com.jeeplus.modules.lawcase.entity.StageTemplate;
import com.jeeplus.modules.lawcase.mapper.StageTemplateMapper;
import com.jeeplus.modules.lawcase.util.TreeUtils;
import com.jeeplus.modules.lawcase.vo.StageTemplateVO;
import com.jeeplus.modules.lawcase.vo.StageVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.jeeplus.core.persistence.Page;
import com.jeeplus.core.service.CrudService;

/**
 * 阶段模版Service
 * <AUTHOR>
 * @version 2021-08-02
 */
@Service
@Transactional(readOnly = true)
public class StageTemplateService extends CrudService<StageTemplateMapper, StageTemplate> {

	@Autowired
	private StageService stageService;
	@Autowired
	private StageRecordService stageRecordService;

	public StageTemplate get(String id) {
		return super.get(id);
	}
	
	public List<StageTemplate> findList(StageTemplate stageTemplate) {
		return super.findList(stageTemplate);
	}
	
	public Page<StageTemplate> findPage(Page<StageTemplate> page, StageTemplate stageTemplate) {
		return super.findPage(page, stageTemplate);
	}
	
	@Transactional(readOnly = false)
	public void save(StageTemplate stageTemplate) {
		super.save(stageTemplate);
	}

	/**
	 * 保存阶段模版、阶段信息、阶段待办事项信息
	 * 1、保存阶段模版信息
	 * 2、循环保存阶段信息、阶段下待办事项信息
	 *
	 * @param stageTemplateVo
	 */
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public void save(StageTemplateVO stageTemplateVo) {
		// 保存模版信息  自定义 字段数据转换
		StageTemplate stageTemplate = CaseConstant.voToStageTemplate(stageTemplateVo);;
		// 是否首次记录
		boolean isFirst = StringUtils.isBlank(stageTemplate.getId());
		
		//获取最大sort
		super.save(stageTemplate);

		// 保存模版阶段、阶段待办事项
		if(!isFirst){
			stageService.deleteByTemplate(stageTemplate.getId());
			stageRecordService.deleteByTemplate(stageTemplate.getId());
		}

		List<Stage> stageList = Lists.newArrayList();
		List<StageRecord> recordList = Lists.newArrayList();
		int sort = 0;
		for (StageVO stageVo : stageTemplateVo.getStageList()) {
			// 自定义 字段数据转换
			Stage stage = CaseConstant.voToStage(stageVo);
			stage.setStageTemplate(stageTemplate);
			stage.setSort(++sort);
			if(StringUtils.isBlank(stage.getId())){
				stage.preInsert();
			}else {
				stage.preUpdate();
				stage.setCreateBy(stage.getUpdateBy());
				stage.setCreateDate(stage.getUpdateDate());
			}
			stageList.add(stage);
			// 处理阶段待办事项数据
			List<StageRecord> list = TreeUtils.treeToList(new StageRecord(), stageVo.getStageRecordList()
					, recordVo -> {
						StageRecord stageRecord = CaseConstant.voToStageRecord(recordVo);
						// TODO 2021-09-08 变更 改为无限级多级结构
//						boolean hasChildren = (recordVo.getChildren() != null && recordVo.getChildren().size() > 0);
//						stageRecord.setType( hasChildren ? CaseConstant.TODO_TYPE_CATALOG : CaseConstant.TODO_TYPE_TASK );
						stageRecord.setStageTemplate(stageTemplate);
						stageRecord.setStage(stage);
						return stageRecord;
					});
			recordList.addAll(list);
		}
		// 判断待办事项级别 TODO 2021-09-08 去掉两级限制
//		for (StageRecord stageRecord : recordList) {
//			if(StringUtils.isNotBlank(stageRecord.getParentIds())){
//				// 需考虑 根级 0,
//				String[] idArr = (stageRecord.getParentIds() + stageRecord.getId()).split(",");
//				if(idArr.length > 3){
//					throw new RuntimeException("最多只能创建两级阶段记录信息");
//				}
//			}
//		}
		// 批量保存模版阶段
		if (stageList.size() > 0) {
			stageService.saveBatch(stageList);
		}
		// 批量保存模版阶段待办事项
		if (recordList.size() > 0) {
			stageRecordService.saveBatch(recordList);
		}
	}

 

	@Transactional(readOnly = false)
	public void delete(StageTemplate stageTemplate) {
		super.delete(stageTemplate);
		// 删除阶段、阶段事项记录
		stageService.deleteByTemplate(stageTemplate.getId());
		stageRecordService.deleteByTemplate(stageTemplate.getId());
	}

	public Integer getMaxSort() {
		// TODO Auto-generated method stub
		return this.mapper.getMaxSort();
	}
	@Transactional(readOnly = false)
	public void updateSort(String id, Integer sort) {
		  this.mapper.updateSort(id,sort);
	}
	 
	
}
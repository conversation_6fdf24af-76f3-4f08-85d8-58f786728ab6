<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jeeplus.modules.lawcase.mapper.CaseProgramMapper">

    <resultMap id="caseProgramResult" type="CaseProgram">
   		<result property="id" column="id" />
		<result property="name" column="name" />
		<result property="sort" column="sort" />
		<result property="parentIds" column="parentIds" />
		<result property="type" column="type" />
    </resultMap>

	<sql id="caseProgramColumns">
		a.id AS "id",
		a.create_by AS "createBy.id",
		a.create_date AS "createDate",
		a.update_by AS "updateBy.id",
		a.update_date AS "updateDate",
		a.remarks AS "remarks",
		a.del_flag AS "delFlag",
		a.type AS "type",
		a.name AS "name",
		a.sort AS "sort",
		a.parent_id AS "parent.id",
		a.parent_ids AS "parentIds"
	</sql>




	<sql id="caseProgramJoins">

	</sql>



	<select id="get" resultType="CaseProgram">
		SELECT
			<include refid="caseProgramColumns"/>
		FROM law_case_program a
		<include refid="caseProgramJoins"/>
		WHERE a.id = #{id}
	</select>

	<select id="findList" resultType="CaseProgram">
		SELECT
			<include refid="caseProgramColumns"/>
		FROM law_case_program a
		<include refid="caseProgramJoins"/>
		<where>
			a.del_flag = #{DEL_FLAG_NORMAL}
			${dataScope}
			<if test="type != null and type != ''">
				AND a.type = #{type}
			</if>
			<if test="name != null and name != ''">
				AND a.name LIKE
				    <if test="_databaseId == 'postgre'">'%'||#{name}||'%'</if>
					<if test="_databaseId == 'oracle'">'%'||#{name}||'%'</if>
					<if test="_databaseId == 'mssql'">'%'+#{name}+'%'</if>
					<if test="_databaseId == 'mysql'">concat('%',#{name},'%')</if>
			</if>
			<if test="parent != null and parent.id != null and parent.id != ''">
				AND a.parent_id = #{parent.id}
			</if>
			<if test="parentIds != null and parentIds != ''">
				AND a.parent_ids LIKE
				    <if test="_databaseId == 'postgre'">'%'||#{parentIds}||'%'</if>
					<if test="_databaseId == 'oracle'">'%'||#{parentIds}||'%'</if>
					<if test="_databaseId == 'mssql'">'%'+#{parentIds}+'%'</if>
					<if test="_databaseId == 'mysql'">concat('%',#{parentIds},'%')</if>
			</if>
		</where>
		ORDER BY a.sort ASC
	</select>

	<select id="findAllList" resultType="CaseProgram">
		SELECT
			<include refid="caseProgramColumns"/>
		FROM law_case_program a
		<include refid="caseProgramJoins"/>
		<where>
			a.del_flag = #{DEL_FLAG_NORMAL}
			${dataScope}
		</where>
		ORDER BY a.sort ASC
	</select>

	<select id="getChildren" parameterType="String" resultMap="caseProgramResult">
        select * from law_case_program where parent_id = #{id} ORDER BY sort
    </select>

	<select id="findByParentIdsLike" resultType="CaseProgram">
		SELECT
			a.id,
			a.parent_id AS "parent.id",
			a.parent_ids
		FROM law_case_program a
		<include refid="caseProgramJoins"/>
		<where>
			a.del_flag = #{DEL_FLAG_NORMAL}
			AND a.parent_ids LIKE #{parentIds}
		</where>
		ORDER BY a.sort ASC
	</select>

	<insert id="insert">
		INSERT INTO law_case_program(
			   id,
			   create_by,
			   create_date,
			   update_by,
			   update_date,
			   remarks,
			   del_flag,
			   type,
			   name,
			   sort,
			   parent_id,
			   parent_ids
		) VALUES (
			#{id},
			#{createBy.id},
			#{createDate},
			#{updateBy.id},
			#{updateDate},
			#{remarks},
			#{delFlag},
			#{type},
			#{name},
			#{sort},
			#{parent.id},
			#{parentIds}
		)
	</insert>

	<update id="update">
		UPDATE law_case_program SET
			update_by = #{updateBy.id},
			update_date = #{updateDate},
			remarks = #{remarks},
			type = #{type},
			name = #{name},
			sort = #{sort},
			parent_id = #{parent.id},
			parent_ids = #{parentIds}
		WHERE id = #{id}
	</update>

	<update id="updateParentIds">
		UPDATE law_case_program SET
			parent_id = #{parent.id},
			parent_ids = #{parentIds}
		WHERE id = #{id}
	</update>

	<!--物理删除-->
	<update id="delete">
		DELETE FROM law_case_program
		WHERE id = #{id} OR parent_ids LIKE
		<if test="_databaseId == 'postgre'">'%,'||#{id}||',%'</if>
		<if test="_databaseId == 'oracle'">'%,'||#{id}||',%'</if>
		<if test="_databaseId == 'mssql'">'%,'+#{id}+',%'</if>
        <if test="_databaseId == 'mysql'">CONCAT('%,', #{id}, ',%')</if>
	</update>

	<!--逻辑删除-->
	<update id="deleteByLogic">
		UPDATE law_case_program SET
			del_flag = #{DEL_FLAG_DELETE}
		WHERE id = #{id} OR parent_ids LIKE
		<if test="_databaseId == 'postgre'">'%,'||#{id}||',%'</if>
		<if test="_databaseId == 'oracle'">'%,'||#{id}||',%'</if>
		<if test="_databaseId == 'mssql'">'%,'+#{id}+',%'</if>
        <if test="_databaseId == 'mysql'">CONCAT('%,', #{id}, ',%')</if>
	</update>

</mapper>
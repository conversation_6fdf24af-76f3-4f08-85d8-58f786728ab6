<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jeeplus.modules.lawcase.mapper.CaseExecuteSituationMapper">

	<sql id="caseExecuteSituationColumns">
		a.id AS "id",
		a.case_id AS "lawCase.id",
		a.apply_date AS "applyDate",
		a.accept_unit AS "acceptUnit",
		a.number AS "number",
		a.status AS "status",
		a.measures AS "measures",
		a.execute_request AS "executeRequest",
		a.performance AS "performance",
		a.create_date AS "createDate",
		a.update_date AS "updateDate",
		a.remarks AS "remarks",
		a.del_flag AS "delFlag",

		ca.name AS "lawCase.name"
	</sql>

	<sql id="caseExecuteSituationJoins">
		LEFT JOIN law_case ca ON a.case_id = ca.id
	</sql>


	<select id="get" resultType="CaseExecuteSituation" >
		SELECT
			<include refid="caseExecuteSituationColumns"/>
		FROM law_case_execute_situation a
		<include refid="caseExecuteSituationJoins"/>
		WHERE a.id = #{id}
	</select>

	<select id="findList" resultType="CaseExecuteSituation" >
		SELECT
			<include refid="caseExecuteSituationColumns"/>
		FROM law_case_execute_situation a
		<include refid="caseExecuteSituationJoins"/>
		<where>
			a.del_flag = #{DEL_FLAG_NORMAL}
			${dataScope}
			<if test="lawCase != null and lawCase.id != null and lawCase.id != ''">
				AND a.case_id = #{lawCase.id}
			</if>
		</where>
		<choose>
			<when test="page !=null and page.orderBy != null and page.orderBy != ''">
				ORDER BY ${page.orderBy}
			</when>
			<otherwise>
				ORDER BY a.update_date DESC
			</otherwise>
		</choose>
	</select>

	<select id="findAllList" resultType="CaseExecuteSituation" >
		SELECT
			<include refid="caseExecuteSituationColumns"/>
		FROM law_case_execute_situation a
		<include refid="caseExecuteSituationJoins"/>
		<where>
			a.del_flag = #{DEL_FLAG_NORMAL}
			${dataScope}
		</where>
		<choose>
			<when test="page !=null and page.orderBy != null and page.orderBy != ''">
				ORDER BY ${page.orderBy}
			</when>
			<otherwise>
				ORDER BY a.update_date DESC
			</otherwise>
		</choose>
	</select>

	<insert id="insert">
		INSERT INTO law_case_execute_situation(
			id,
			case_id,
			apply_date,
			accept_unit,
			`number`,
			status,
			measures,
			execute_request,
			performance,
			create_by,
			create_date,
			update_by,
			update_date,
			remarks,
			del_flag
		) VALUES (
			#{id},
			#{lawCase.id},
			#{applyDate},
			#{acceptUnit},
			#{number},
			#{status},
			#{measures},
			#{executeRequest},
			#{performance},
			#{createBy.id},
			#{createDate},
			#{updateBy.id},
			#{updateDate},
			#{remarks},
			#{delFlag}
		)
	</insert>

	<update id="update">
		UPDATE law_case_execute_situation SET
			case_id = #{lawCase.id},
			apply_date = #{applyDate},
			accept_unit = #{acceptUnit},
			number = #{number},
			status = #{status},
			measures = #{measures},
			execute_request = #{executeRequest},
			performance = #{performance},
			update_by = #{updateBy.id},
			update_date = #{updateDate},
			remarks = #{remarks}
		WHERE id = #{id}
	</update>


	<!--物理删除-->
	<update id="delete">
		DELETE FROM law_case_execute_situation
		WHERE id = #{id}
	</update>

	<!--逻辑删除-->
	<update id="deleteByLogic">
		UPDATE law_case_execute_situation SET
			del_flag = #{DEL_FLAG_DELETE}
		WHERE id = #{id}
	</update>


	<!-- 根据实体名称和字段名称和字段值获取唯一记录 -->
	<select id="findUniqueByProperty" resultType="CaseExecuteSituation">
		select * FROM law_case_execute_situation  where ${propertyName} = #{value}
	</select>

</mapper>
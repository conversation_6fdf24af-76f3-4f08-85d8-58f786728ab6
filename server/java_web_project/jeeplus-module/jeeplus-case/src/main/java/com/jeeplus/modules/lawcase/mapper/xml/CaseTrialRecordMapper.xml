<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jeeplus.modules.lawcase.mapper.CaseTrialRecordMapper">

	<sql id="caseTrialRecordColumns">
		a.id AS "id",
		a.case_id AS "lawCase.id",
		a.open_court_date AS "openCourtDate",
		a.open_court_type AS "openCourtType",
		a.barrister_id AS "barrister.id",
		a.barrister_name AS "barrister.name",
		a.open_court_address AS "openCourtAddress",
		a.controversy_focus AS "controversyFocus",
		a.our_opinion AS "ourOpinion",
		a.other_opinion AS "otherOpinion",
		a.judge_attitude AS "judgeAttitude",
		a.trial_summary AS "trialSummary",
		a.create_date AS "createDate",
		a.update_date AS "updateDate",
		a.remarks AS "remarks",
		a.del_flag AS "delFlag"
	</sql>

	<sql id="caseTrialRecordJoins">

	</sql>


	<select id="get" resultType="CaseTrialRecord" >
		SELECT
			<include refid="caseTrialRecordColumns"/>
		FROM law_case_trial_record a
		<include refid="caseTrialRecordJoins"/>
		WHERE a.id = #{id}
	</select>

	<select id="findList" resultType="CaseTrialRecord" >
		SELECT
			<include refid="caseTrialRecordColumns"/>
		FROM law_case_trial_record a
		<include refid="caseTrialRecordJoins"/>
		<where>
			a.del_flag = #{DEL_FLAG_NORMAL}
			${dataScope}
			<if test="lawCase != null and lawCase.id != null and lawCase.id != ''">
				AND a.case_id = #{lawCase.id}
			</if>
		</where>
		<choose>
			<when test="page !=null and page.orderBy != null and page.orderBy != ''">
				ORDER BY ${page.orderBy}
			</when>
			<otherwise>
				ORDER BY a.update_date DESC
			</otherwise>
		</choose>
	</select>

	<select id="findAllList" resultType="CaseTrialRecord" >
		SELECT
			<include refid="caseTrialRecordColumns"/>
		FROM law_case_trial_record a
		<include refid="caseTrialRecordJoins"/>
		<where>
			a.del_flag = #{DEL_FLAG_NORMAL}
			${dataScope}
		</where>
		<choose>
			<when test="page !=null and page.orderBy != null and page.orderBy != ''">
				ORDER BY ${page.orderBy}
			</when>
			<otherwise>
				ORDER BY a.update_date DESC
			</otherwise>
		</choose>
	</select>

	<insert id="insert">
		INSERT INTO law_case_trial_record(
			id,
			case_id,
			open_court_date,
			open_court_type,
			barrister_id,
			barrister_name,
			open_court_address,
			controversy_focus,
			our_opinion,
			other_opinion,
			judge_attitude,
			trial_summary,
			create_by,
			create_date,
			update_by,
			update_date,
			remarks,
			del_flag
		) VALUES (
			#{id},
			#{lawCase.id},
			#{openCourtDate},
			#{openCourtType},
			#{barrister.id},
			#{barrister.name},
			#{openCourtAddress},
			#{controversyFocus},
			#{ourOpinion},
			#{otherOpinion},
			#{judgeAttitude},
			#{trialSummary},
			#{createBy.id},
			#{createDate},
			#{updateBy.id},
			#{updateDate},
			#{remarks},
			#{delFlag}
		)
	</insert>

	<update id="update">
		UPDATE law_case_trial_record SET
			case_id = #{lawCase.id},
			open_court_date = #{openCourtDate},
			open_court_type = #{openCourtType},
			barrister_id = #{barrister.id},
			barrister_name = #{barrister.name},
			open_court_address = #{openCourtAddress},
			controversy_focus = #{controversyFocus},
			our_opinion = #{ourOpinion},
			other_opinion = #{otherOpinion},
			judge_attitude = #{judgeAttitude},
			trial_summary = #{trialSummary},
			update_by = #{updateBy.id},
			update_date = #{updateDate},
			remarks = #{remarks}
		WHERE id = #{id}
	</update>


	<!--物理删除-->
	<update id="delete">
		DELETE FROM law_case_trial_record
		WHERE id = #{id}
	</update>

	<!--逻辑删除-->
	<update id="deleteByLogic">
		UPDATE law_case_trial_record SET
			del_flag = #{DEL_FLAG_DELETE}
		WHERE id = #{id}
	</update>


	<!-- 根据实体名称和字段名称和字段值获取唯一记录 -->
	<select id="findUniqueByProperty" resultType="CaseTrialRecord">
		select * FROM law_case_trial_record  where ${propertyName} = #{value}
	</select>

</mapper>
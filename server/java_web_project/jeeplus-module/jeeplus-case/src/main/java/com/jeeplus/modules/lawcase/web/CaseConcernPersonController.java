/**
 * Copyright © 2015-2020 <a href="http://www.jeeplus.org/">JeePlus</a> All rights reserved.
 */
package com.jeeplus.modules.lawcase.web;

import java.util.List;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.ConstraintViolationException;

import com.jeeplus.modules.lawcase.constant.CaseConstant;
import com.jeeplus.modules.lawcase.dto.CaseConcernPersonDto;
import com.jeeplus.modules.lawcase.entity.Case;
import com.jeeplus.modules.lawcase.entity.CaseConcernPerson;
import com.jeeplus.modules.lawcase.entity.CaseStage;
import com.jeeplus.modules.lawcase.service.CaseConcernPersonService;
import com.jeeplus.modules.sys.entity.User;
import com.jeeplus.modules.sys.utils.UserUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import com.google.common.collect.Lists;
import com.jeeplus.common.utils.DateUtils;
import com.jeeplus.common.json.AjaxJson;
import com.jeeplus.core.persistence.Page;
import com.jeeplus.core.web.BaseController;
import com.jeeplus.common.utils.StringUtils;
import com.jeeplus.common.utils.excel.ExportExcel;
import com.jeeplus.common.utils.excel.ImportExcel;

/**
 * 案件当事人Controller
 * <AUTHOR>
 * @version 2021-08-02
 */
@RestController
@Api(tags = "案件-当事人")
@RequestMapping(value = "/case/caseConcernPerson")
public class CaseConcernPersonController extends BaseController {

	@Autowired
	private CaseConcernPersonService caseConcernPersonService;

	@ModelAttribute
	public CaseConcernPerson get(@RequestParam(required=false) String id) {
		CaseConcernPerson entity = null;
		if (StringUtils.isNotBlank(id)){
			entity = caseConcernPersonService.get(id);
		}
		if (entity == null){
			entity = new CaseConcernPerson();
		}
		return entity;
	}

	/**
	 * 案件当事人列表数据
	 */
//	@RequiresPermissions("case:caseConcernPerson:list")
	@ApiOperation(value = "当事人列表", consumes = "application/form-data")
	@ApiImplicitParam(value = "案件id值", name = "lawCase.id", required = true)
	@PostMapping("list")
	public AjaxJson list(CaseConcernPerson caseConcernPerson, HttpServletRequest request, HttpServletResponse response) {
		Page<CaseConcernPerson> page = new Page<CaseConcernPerson>(request, response);
		if(caseConcernPerson.getLawCase() != null && StringUtils.isNotBlank(caseConcernPerson.getLawCase().getId())){
			page = caseConcernPersonService.findPage(page, caseConcernPerson);
		}
		return AjaxJson.success().put("page",page);
	}

	@ApiOperation(value = "当事人库信息列表", consumes = "application/form-data")
	@ApiImplicitParams({
			@ApiImplicitParam(value = "当事人名称", name = "name"),
			@ApiImplicitParam(value = "类型 字典：customer_type", name = "type"),
			@ApiImplicitParam(value = "案件名称", name = "lawCaseName"),
			@ApiImplicitParam(value = "是否委托方 字典：yes_no", name = "isEntrust")
	})
	@PostMapping("allList")
	public AjaxJson list1(CaseConcernPersonDto caseConcernPersonDto, HttpServletRequest request, HttpServletResponse response) {
		// 数据过滤验证 仅 律师权限的用户只能看到 主办人是自己 的数据
		caseConcernPersonDto = CaseConstant.dataFilterVerify(caseConcernPersonDto, (concernPersonDto, user) ->{
			concernPersonDto.setHostUser(user);
			return concernPersonDto;
		});

		Page<CaseConcernPersonDto> page = caseConcernPersonService.findOverallPage(new Page<CaseConcernPersonDto>(request, response), caseConcernPersonDto);
		return AjaxJson.success().put("page",page);
	}

	/**
	 * 根据Id获取案件当事人数据
	 */
//	@RequiresPermissions(value={"case:caseConcernPerson:view","case:caseConcernPerson:add","case:caseConcernPerson:edit"},logical=Logical.OR)
	@ApiOperation(value = "当事人详情", consumes = "application/form-data")
	@ApiImplicitParam(value = "id值", name = "id", required = true)
	@PostMapping("queryById")
	public AjaxJson queryById(CaseConcernPerson caseConcernPerson) {
		return AjaxJson.success().put("caseConcernPerson", caseConcernPerson);
	}

	/**
	 * 保存案件当事人
	 */
//	@RequiresPermissions(value={"case:caseConcernPerson:add","case:caseConcernPerson:edit"},logical=Logical.OR)
	@ApiOperation(value = "当事人信息保存")
	@PostMapping("save")
	public AjaxJson save(@RequestBody CaseConcernPerson caseConcernPerson) throws Exception{
		/**
		 * 后台hibernate-validation插件校验
		 */
		String errMsg = beanValidator(caseConcernPerson);
		if (StringUtils.isNotBlank(errMsg)){
			return AjaxJson.error(errMsg);
		}
		if(caseConcernPerson.getLawCase() == null || StringUtils.isBlank(caseConcernPerson.getLawCase().getId())){
			return AjaxJson.error("案件信息有误");
		}
		if(StringUtils.isBlank(caseConcernPerson.getName())){
			String msg = "请填写姓名";
			if(CaseConstant.CUSTOMER_TYPE_COMPANY.equals(caseConcernPerson.getType())){
				msg = "请填写单位名称";
			}
			return AjaxJson.error(msg);
		}
		if(StringUtils.isBlank(caseConcernPerson.getPhone())){
			return AjaxJson.error("请填写联系方式");
		}
		// 查询原有数据 进行替换
		if (StringUtils.isNotBlank(caseConcernPerson.getId())) {
			CaseConcernPerson oldCaseConcernPerson = caseConcernPersonService.get(caseConcernPerson.getId());
			caseConcernPerson = CaseConstant.classCopyProperties(caseConcernPerson, oldCaseConcernPerson);
		}

		//新增或编辑表单保存
		caseConcernPersonService.save(caseConcernPerson);//保存
		return AjaxJson.success("保存案件当事人成功");
	}


	/**
	 * 批量删除案件当事人
	 */
//	@RequiresPermissions("case:caseConcernPerson:del")
	@ApiOperation(value = "当事人信息删除", consumes = "application/form-data")
	@PostMapping("delete")
	public AjaxJson delete(String ids) {
		String idArray[] =ids.split(",");
		for(String id : idArray){
			caseConcernPersonService.delete(new CaseConcernPerson(id));
		}
		return AjaxJson.success("删除案件当事人成功");
	}

	/**
	 * 导出excel文件
	 */
	@RequiresPermissions(value = {"case:caseConcernPerson:export", "user"}, logical = Logical.OR)
	@ApiOperation(value = "当事人导出", consumes = "application/form-data")
    @GetMapping("export")
    public AjaxJson exportFile(CaseConcernPersonDto caseConcernPersonDto, HttpServletRequest request, HttpServletResponse response) {
		try {
			// 数据过滤验证 仅 律师权限的用户只能看到 主办人是自己 的数据
			caseConcernPersonDto = CaseConstant.dataFilterVerify(caseConcernPersonDto, (concernPersonDto, user) ->{
				concernPersonDto.setHostUser(user);
				return concernPersonDto;
			});
            String fileName = "案件当事人"+DateUtils.getDate("yyyyMMddHHmmss")+".xlsx";
//            Page<CaseConcernPerson> page = caseConcernPersonService.findPage(new Page<CaseConcernPerson>(request, response, -1), caseConcernPerson);
			Page<CaseConcernPersonDto> page = caseConcernPersonService.findOverallPage(new Page<CaseConcernPersonDto>(request, response, -1), caseConcernPersonDto);
    		new ExportExcel("案件当事人", CaseConcernPersonDto.class).setDataList(page.getList()).write(response, fileName).dispose();
    		return null;
		} catch (Exception e) {
			return AjaxJson.error("导出案件当事人记录失败！失败信息："+e.getMessage());
		}
    }

	/**
	 * 导入Excel数据

	 */
	@RequiresPermissions("case:caseConcernPerson:import")
    @PostMapping("import")
   	public AjaxJson importFile(@RequestParam("file")MultipartFile file, HttpServletResponse response, HttpServletRequest request) {
		try {
			int successNum = 0;
			int failureNum = 0;
			StringBuilder failureMsg = new StringBuilder();
			ImportExcel ei = new ImportExcel(file, 1, 0);
			List<CaseConcernPerson> list = ei.getDataList(CaseConcernPerson.class);
			for (CaseConcernPerson caseConcernPerson : list){
				try{
					caseConcernPersonService.save(caseConcernPerson);
					successNum++;
				}catch(ConstraintViolationException ex){
					failureNum++;
				}catch (Exception ex) {
					failureNum++;
				}
			}
			if (failureNum>0){
				failureMsg.insert(0, "，失败 "+failureNum+" 条案件当事人记录。");
			}
			return AjaxJson.success( "已成功导入 "+successNum+" 条案件当事人记录"+failureMsg);
		} catch (Exception e) {
			return AjaxJson.error("导入案件当事人失败！失败信息："+e.getMessage());
		}
    }

	/**
	 * 下载导入案件当事人数据模板
	 */
	@RequiresPermissions("case:caseConcernPerson:import")
    @GetMapping("import/template")
     public AjaxJson importFileTemplate(HttpServletResponse response) {
		try {
            String fileName = "案件当事人数据导入模板.xlsx";
    		List<CaseConcernPerson> list = Lists.newArrayList();
    		new ExportExcel("案件当事人数据", CaseConcernPerson.class, 1).setDataList(list).write(response, fileName).dispose();
    		return null;
		} catch (Exception e) {
			return AjaxJson.error( "导入模板下载失败！失败信息："+e.getMessage());
		}
    }


}
<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jeeplus.modules.lawcase.mapper.CaseFileDirectoryMapper">

    <resultMap id="caseFileDirectoryResult" type="com.jeeplus.modules.lawcase.entity.CaseFileDirectory">
   		<result property="id" column="id" />
		<result property="name" column="name" />
		<result property="sort" column="sort" />
		<result property="parentIds" column="parentIds" />
		<result property="lawCase.id" column="lawCase.id" />
    </resultMap>

	<sql id="caseFileDirectoryColumns">
		a.id AS "id",
		a.create_by AS "createBy.id",
		a.create_date AS "createDate",
		a.update_by AS "updateBy.id",
		a.update_date AS "updateDate",
		a.remarks AS "remarks",
		a.del_flag AS "delFlag",
		a.parent_id AS "parent.id",
		a.parent_ids AS "parentIds",
		a.name AS "name",
		a.sort AS "sort",
		a.case_id AS "lawCase.id"
	</sql>




	<sql id="caseFileDirectoryJoins">

	</sql>



	<select id="get" resultType="CaseFileDirectory">
		SELECT
			<include refid="caseFileDirectoryColumns"/>
		FROM law_case_file_directory a
		<include refid="caseFileDirectoryJoins"/>
		WHERE a.id = #{id}
	</select>

	<select id="getRootByCase" resultType="CaseFileDirectory">
		SELECT <include refid="caseFileDirectoryColumns"/>
		FROM law_case_file_directory a
		<include refid="caseFileDirectoryJoins"/>
		WHERE a.case_id = #{lawCase.id} AND a.parent_id = #{parentId}
		ORDER BY a.create_date
		LIMIT 1
	</select>

	<select id="findList" resultType="CaseFileDirectory">
		SELECT
			<include refid="caseFileDirectoryColumns"/>
		FROM law_case_file_directory a
		<include refid="caseFileDirectoryJoins"/>
		<where>
			a.del_flag = #{DEL_FLAG_NORMAL}
			${dataScope}
			<if test="parent != null and parent.id != null and parent.id != ''">
				AND a.parent_id = #{parent.id}
			</if>
			<if test="parentIds != null and parentIds != ''">
				AND a.parent_ids LIKE
				    <if test="_databaseId == 'postgre'">'%'||#{parentIds}||'%'</if>
					<if test="_databaseId == 'oracle'">'%'||#{parentIds}||'%'</if>
					<if test="_databaseId == 'mssql'">'%'+#{parentIds}+'%'</if>
					<if test="_databaseId == 'mysql'">concat('%',#{parentIds},'%')</if>
			</if>
			<if test="name != null and name != ''">
				AND a.name LIKE
				    <if test="_databaseId == 'postgre'">'%'||#{name}||'%'</if>
					<if test="_databaseId == 'oracle'">'%'||#{name}||'%'</if>
					<if test="_databaseId == 'mssql'">'%'+#{name}+'%'</if>
					<if test="_databaseId == 'mysql'">concat('%',#{name},'%')</if>
			</if>
			<if test="queryAccurateName != null and queryAccurateName != ''">
				AND a.name = #{queryAccurateName}
			</if>

			<if test="lawCase != null and lawCase.id != null and lawCase.id != ''">
				AND a.case_id = #{lawCase.id}
			</if>
		</where>
		ORDER BY a.sort ASC, a.create_date
	</select>

	<select id="findAllList" resultType="CaseFileDirectory">
		SELECT
			<include refid="caseFileDirectoryColumns"/>
		FROM law_case_file_directory a
		<include refid="caseFileDirectoryJoins"/>
		<where>
			a.del_flag = #{DEL_FLAG_NORMAL}
			${dataScope}
		</where>
		ORDER BY a.sort ASC
	</select>

	<select id="getChildren" parameterType="String" resultMap="caseFileDirectoryResult">
        select * from law_case_file_directory where parent_id = #{id} ORDER BY sort
    </select>

	<select id="findByParentIdsLike" resultType="CaseFileDirectory">
		SELECT
			a.id,
			a.parent_id AS "parent.id",
			a.parent_ids
		FROM law_case_file_directory a
		<include refid="caseFileDirectoryJoins"/>
		<where>
			a.del_flag = #{DEL_FLAG_NORMAL}
			AND a.parent_ids LIKE #{parentIds}
		</where>
		ORDER BY a.sort ASC
	</select>

	<insert id="insert">
		INSERT INTO law_case_file_directory(
			   id,
			   create_by,
			   create_date,
			   update_by,
			   update_date,
			   remarks,
			   del_flag,
			   parent_id,
			   parent_ids,
			   `name`,
			   sort,
			   case_id
		) VALUES (
			#{id},
			#{createBy.id},
			#{createDate},
			#{updateBy.id},
			#{updateDate},
			#{remarks},
			#{delFlag},
			#{parent.id},
			#{parentIds},
			#{name},
			#{sort},
			#{lawCase.id}
		)
	</insert>

	<update id="update">
		UPDATE law_case_file_directory SET
			update_by = #{updateBy.id},
			update_date = #{updateDate},
			remarks = #{remarks},
			parent_id = #{parent.id},
			parent_ids = #{parentIds},
			`name` = #{name},
			sort = #{sort},
			case_id = #{lawCase.id}
		WHERE id = #{id}
	</update>

	<update id="updateParentIds">
		UPDATE law_case_file_directory SET
			parent_id = #{parent.id},
			parent_ids = #{parentIds}
		WHERE id = #{id}
	</update>

	<!--物理删除-->
	<update id="delete">
		DELETE FROM law_case_file_directory
		WHERE id = #{id} OR parent_ids LIKE
		<if test="_databaseId == 'postgre'">'%,'||#{id}||',%'</if>
		<if test="_databaseId == 'oracle'">'%,'||#{id}||',%'</if>
		<if test="_databaseId == 'mssql'">'%,'+#{id}+',%'</if>
        <if test="_databaseId == 'mysql'">CONCAT('%,', #{id}, ',%')</if>
	</update>

	<!--逻辑删除-->
	<update id="deleteByLogic">
		UPDATE law_case_file_directory SET
			del_flag = #{DEL_FLAG_DELETE}
		WHERE id = #{id} OR parent_ids LIKE
		<if test="_databaseId == 'postgre'">'%,'||#{id}||',%'</if>
		<if test="_databaseId == 'oracle'">'%,'||#{id}||',%'</if>
		<if test="_databaseId == 'mssql'">'%,'+#{id}+',%'</if>
        <if test="_databaseId == 'mysql'">CONCAT('%,', #{id}, ',%')</if>
	</update>

</mapper>
/**
 * Copyright © 2015-2020 <a href="http://www.jeeplus.org/">JeePlus</a> All rights reserved.
 */
package com.jeeplus.modules.lawcase.service;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.jeeplus.core.service.TreeService;
import com.jeeplus.modules.lawcase.entity.CaseModelType;
import com.jeeplus.modules.lawcase.mapper.CaseModelTypeMapper;

 
@Service
@Transactional(readOnly = true)
public class CaseModelTypeService extends TreeService<CaseModelTypeMapper, CaseModelType> {

	public CaseModelType get(String id) {
		return super.get(id);
	}

}
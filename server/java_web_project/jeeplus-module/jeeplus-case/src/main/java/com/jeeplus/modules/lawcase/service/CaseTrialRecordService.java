/**
 * Copyright © 2015-2020 <a href="http://www.jeeplus.org/">JeePlus</a> All rights reserved.
 */
package com.jeeplus.modules.lawcase.service;

import java.util.List;

import com.jeeplus.modules.lawcase.entity.CaseTrialRecord;
import com.jeeplus.modules.lawcase.mapper.CaseTrialRecordMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.jeeplus.core.persistence.Page;
import com.jeeplus.core.service.CrudService;

/**
 * 庭审记录Service
 * <AUTHOR>
 * @version 2021-08-02
 */
@Service
@Transactional(readOnly = true)
public class CaseTrialRecordService extends CrudService<CaseTrialRecordMapper, CaseTrialRecord> {

	public CaseTrialRecord get(String id) {
		return super.get(id);
	}
	
	public List<CaseTrialRecord> findList(CaseTrialRecord caseTrialRecord) {
		return super.findList(caseTrialRecord);
	}
	
	public Page<CaseTrialRecord> findPage(Page<CaseTrialRecord> page, CaseTrialRecord caseTrialRecord) {
		return super.findPage(page, caseTrialRecord);
	}
	
	@Transactional(readOnly = false)
	public void save(CaseTrialRecord caseTrialRecord) {
		super.save(caseTrialRecord);
	}
	
	@Transactional(readOnly = false)
	public void delete(CaseTrialRecord caseTrialRecord) {
		super.delete(caseTrialRecord);
	}
	
}
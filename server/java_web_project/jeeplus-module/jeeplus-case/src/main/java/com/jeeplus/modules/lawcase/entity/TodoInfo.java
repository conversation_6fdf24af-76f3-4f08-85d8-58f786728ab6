/**
 * Copyright © 2015-2020 <a href="http://www.jeeplus.org/">JeePlus</a> All rights reserved.
 */
package com.jeeplus.modules.lawcase.entity;

import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.google.common.collect.Lists;
import com.jeeplus.common.utils.StringUtils;
import com.jeeplus.modules.sys.entity.User;
import com.fasterxml.jackson.annotation.JsonBackReference;
import javax.validation.constraints.NotNull;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import com.jeeplus.core.persistence.TreeEntity;
import lombok.Data;

/**
 * 待办事项Entity
 * <AUTHOR>
 * @version 2021-08-12
 */
@Data 
@JsonIgnoreProperties(value={"hibernateLazyInitializer","handler"})
public class TodoInfo extends TreeEntity<TodoInfo> {
	
	private static final long serialVersionUID = 1L;
	/** 检测字段 overdue 逾期   plan 待计划  checkFile 检测是否有附件 */
	public static final String CHECK_WORD_OVERDUE = "overdue";
	public static final String CHECK_WORD_PLAN = "plan";
	public static final String CHECK_WORD_FILE = "checkFile";

	private String type;		// 类型	字典：todo_type
	private String content;		// 内容详情
	private Double money;		// 金额
	private String status;		// 办理状态(已办、未办)
	@NotNull
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date startDate;		// 开始时间
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date endDate;		// 结束时间
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date remindDate;		// 提醒时间
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date completeDate;	// 完成时间
	private String consumeTime;		// 耗时（分）
	private User hostUser;		// 主办人员
	private String relevanceType;		// 关联类型
	private String relevanceId;		// 关联信息
	private CaseStage stage;		// 阶段信息
	private String isNotAuditProhibit; // 是否未审核期间禁止编辑 默认：否	2022-03-05 新增

	// 虚拟
	private String isHaveFile;			// 是否有附件
	private String isHaveDoc;			// 是否有文档
	private Integer isEditFile;//编辑新增文件 1编辑 2新增
	
	private List<TodoInfoFile> fileList = Lists.newArrayList();	// 附件列表
	private List<TodoInfoFile> fileList2 = Lists.newArrayList();	// 附件列表
	private String queryRelevanceIds;	// 查询关联ids 多个以,拼接
	private String checkWord;			// 检测字段		overdue 逾期   plan 待计划   checkFile 是否有附件
	private Date queryStartDate;		// 查询开始日期
	private Date queryEndDate;			// 查询结束日期
	private Case lawCase;				// 关联案件信息

	public TodoInfo() {
		super();
	}

	public TodoInfo(String id){
		super(id);
	}

	public  TodoInfo getParent() {
			return parent;
	}
	
	@Override
	public void setParent(TodoInfo parent) {
		this.parent = parent;
		
	}
	
	public String getParentId() {
		return parent != null && StringUtils.isNotBlank(parent.getId()) ? parent.getId() : "0";
	}
}
/**
 * Copyright © 2015-2020 <a href="http://www.jeeplus.org/">JeePlus</a> All rights reserved.
 */
package com.jeeplus.modules.lawcase.service;

import java.util.*;
import java.util.stream.Collectors;

import com.alibaba.fastjson.JSON;
import com.jeeplus.common.json.AjaxJson;
import com.jeeplus.common.utils.FileUtils;
import com.jeeplus.modules.lawcase.constant.CaseConstant;
import com.jeeplus.modules.lawcase.entity.CaseStage;
import com.jeeplus.modules.lawcase.entity.TodoInfoFile;
import com.jeeplus.modules.lawcase.mapper.TodoInfoFileMapper;
import com.jeeplus.modules.lawcase.util.PackToZipUtils;
import com.jeeplus.modules.lawcase.vo.TodoInfoRelevanceVO;
import com.jeeplus.modules.sys.utils.FileKit;
import com.jeeplus.modules.sys.utils.GeTuiPushUtils;
import com.jeeplus.modules.sys.utils.GeTuiPushUtils.PushParam;
import com.jeeplus.modules.sys.utils.UserUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.jeeplus.core.service.TreeService;
import com.jeeplus.common.utils.StringUtils;
import com.jeeplus.modules.lawcase.entity.TodoInfo;
import com.jeeplus.modules.lawcase.mapper.TodoInfoMapper;

/**
 * 待办事项Service
 * <AUTHOR>
 * @version 2021-08-12
 */
@Service
@Transactional(readOnly = true)
public class TodoInfoService extends TreeService<TodoInfoMapper, TodoInfo> {

	@Autowired
	private TodoInfoFileMapper todoInfoFileMapper;

	public TodoInfo get(String id) {
		return super.get(id);
	}

	public List<TodoInfo> findList(TodoInfo todoInfo) {
		if (StringUtils.isNotBlank(todoInfo.getParentIds())){
			todoInfo.setParentIds(","+todoInfo.getParentIds()+",");
		}
		return super.findList(todoInfo);
	}

	/**
	 * 检测待办事项级别 默认最多只有两级 TODO 暂时废弃 2021-09-08
	 * @param todoInfo
	 * @return
	 */
	public AjaxJson checkTodoLevel(TodoInfo todoInfo) {
		String parentId = todoInfo.getParentId();
		if("0".equals(parentId)){
			todoInfo.setType(CaseConstant.TODO_TYPE_CATALOG);
		}else {
			TodoInfo parentTodo = this.get(parentId);
			if(StringUtils.isNotBlank(parentTodo.getParentIds())){
				// 因考虑 父级0,不计入数量 可忽略
				String[] parentArr = (parentTodo.getParentIds() + parentTodo.getId()).split(",");
				if(parentArr.length > 2){
					return AjaxJson.error("最多只能创建两级待办事项");
				}
			}
			todoInfo.setType(CaseConstant.TODO_TYPE_TASK);
		}
		return AjaxJson.success("正常").put("todoInfo", todoInfo);
	}

	/**
	 * 根据 关联id、类型 获取待办事项
	 * @param todoInfo
	 * @return
	 */
	public List<TodoInfo> findTreeList(TodoInfo todoInfo) {
		List<TodoInfo> list = new ArrayList<>();
		if(StringUtils.isNotBlank(todoInfo.getRelevanceId())){
			List<TodoInfo> todoList = this.findList(todoInfo);
			list = formatListToTree(new TodoInfo("0") ,todoList, "");
		}
		return list;
	}

	/**
	 * 根据 管理类型、关联id 进行查询封装
	 * @param todoInfo
	 * @return
	 */
	public Map<String, List<TodoInfo>> findGroupMap(TodoInfo todoInfo){
		Map<String, List<TodoInfo>> map = new HashMap<String, List<TodoInfo>>(10);
		List<TodoInfoRelevanceVO> list = mapper.findGroupList(todoInfo);
		// 将查询 list 结果进行 Map封装
		if(list != null && list.size() > 0){
			map = list.stream().collect(
					/* toMap(p1, p2, p3)  p1: map key值   p2: map value值   p3：key值重复时处理方式 */
					Collectors.toMap(TodoInfoRelevanceVO::getRelevanceId, TodoInfoRelevanceVO::getTodoInfoList
							, (oldList, newList) ->{
								List<TodoInfo> todoInfoList = new ArrayList<TodoInfo>(oldList);
								todoInfoList.addAll(newList);
								return todoInfoList;
							}
					)
			);
		}
		return map;
	}

	/**
	 * 定时任务 - 统计主办人的 待办事项数量信息并发送 APP消息通知
	 * @param todoInfo
	 */
	public void hostUserMsgPush(TodoInfo todoInfo){
		List<Map<String, Object>> hostUserStatisticList = mapper.findHostUserStatisticList(todoInfo);
		if(hostUserStatisticList != null && hostUserStatisticList.size() > 0){
			Map<String, List<String>> userClientMap = GeTuiPushUtils.getUserClientMap();
			List<PushParam> pushParamList = new ArrayList<>();
			for (Map<String, Object> map : hostUserStatisticList) {
				String hostUserLoginName = String.valueOf(map.get("hostUserLoginName"));
				Integer amount = Integer.valueOf( String.valueOf(map.get("amount")) );
				// 若待办事项数量存在
				if(amount > 0){
					List<String> clientIdList = userClientMap.get(hostUserLoginName);
					if(clientIdList != null && clientIdList.size() > 0){
						String content = "您有"+ (amount > 1 ? (amount+"条"): "新的") +"待办事项需要处理";
						PushParam pushParam = new PushParam();
						pushParam.setClientIdArr(clientIdList);
						pushParam.setTitle("待办消息提醒");
						pushParam.setContent(content);
						pushParamList.add(pushParam);
					}
				}
			}
//			System.out.println("推送数据："+ JSON.toJSONString(pushParamList));
			// 推送消息
			if(pushParamList.size() > 0){
				GeTuiPushUtils.pushBatchByCid( pushParamList );
			}

		}
	}

	/**
	 * 根据 查询起始、查询结束日期 获取月信息数据
	 * @param todoInfo
	 * @return
	 */
	public List<TodoInfo> findListByMonth(TodoInfo todoInfo){
		return mapper.findListByMonth(todoInfo);
	}

	/**
	 * 查询获取 待办事项附件记录
	 * @param todoInfoFile
	 * @return
	 */
	public List<TodoInfoFile> findFileList(TodoInfoFile todoInfoFile){
		List<TodoInfoFile> fileList = new ArrayList<TodoInfoFile>();
		if(todoInfoFile.getTodoInfo() != null && StringUtils.isNotBlank(todoInfoFile.getTodoInfo().getId())){
			fileList = todoInfoFileMapper.findList(todoInfoFile);
		}
		return fileList;
	}

	public List<PackToZipUtils.PackDTO> getFilePackList(String caseId){
		List<PackToZipUtils.PackDTO> packList = new ArrayList<>();

		List<TodoInfoFile> fileList = todoInfoFileMapper.findFileListByCase(caseId);
		if(fileList != null && fileList.size() > 0){
			TodoInfo todoInfo = new TodoInfo();
			todoInfo.setRelevanceType(CaseConstant.TODO_RELEVANCE_TYPE_CASE);
			todoInfo.setRelevanceId(caseId);
			List<TodoInfo> todoInfoList = mapper.findTodoStageList(todoInfo);
			if(todoInfoList != null && todoInfoList.size() > 0){
				Map<String, String> todoMap = todoInfoList.stream().collect(
						Collectors.toMap(TodoInfo::getId, obj -> obj.getStage().getName(), (t1, t2) -> t2)
				);
				for (TodoInfoFile todoFile : fileList) {
					PackToZipUtils.PackDTO pack = new PackToZipUtils.PackDTO();
					String dirName = todoMap.get(todoFile.getTodoInfo().getId());
					if(StringUtils.isNotBlank(dirName)){
						pack.setDirName(dirName);
						pack.setPath(todoFile.getPath());
						pack.setFileName(todoFile.getName());

						packList.add(pack);
					}
				}
			}
		}

		return packList;
	}

	@Transactional(readOnly = false)
	public void save(TodoInfo todoInfo) {
		super.save(todoInfo);
	}

	/**
	 * 保存 待办事项、附件信息
	 * @param todoInfo
	 */
	@Transactional(readOnly = false)
	public void saveInfo(TodoInfo todoInfo) {
		Boolean isFirst = StringUtils.isBlank(todoInfo.getId());
		if(isFirst){
			todoInfo.setHostUser(UserUtils.getUser());
		}
		super.save(todoInfo);
		// 插入附件信息
		if(!isFirst){
			todoInfoFileMapper.deleteByTodo(todoInfo.getId());
		}
		if(todoInfo.getFileList() != null && todoInfo.getFileList().size() > 0){
			List<TodoInfoFile> todoFileList = todoInfo.getFileList().stream().filter(obj -> StringUtils.isNotBlank(obj.getPath())).collect(Collectors.toList());
			if(todoFileList.size() > 0){
				for (TodoInfoFile todoInfoFile : todoFileList) {
					todoInfoFile.setTodoInfo(todoInfo);
					if(StringUtils.isBlank(todoInfoFile.getId())){
						todoInfoFile.preInsert();
					}else {
						todoInfoFile.preUpdate();
						if(todoInfoFile.getCreateDate() == null){ todoInfoFile.setCreateDate( todoInfoFile.getUpdateDate() ); }
					}
				}
				todoInfoFileMapper.insertBatch(todoFileList);
			}
		}
		if(todoInfo.getFileList2() != null && todoInfo.getFileList2().size() > 0){
			List<TodoInfoFile> todoFileList = todoInfo.getFileList2().stream().filter(obj -> StringUtils.isNotBlank(obj.getPath())).collect(Collectors.toList());
			if(todoFileList.size() > 0){
				for (TodoInfoFile todoInfoFile : todoFileList) {
					todoInfoFile.setTodoInfo(todoInfo);
					if(StringUtils.isBlank(todoInfoFile.getId())){
						todoInfoFile.preInsert();
					}else {
						todoInfoFile.preUpdate();
						if(todoInfoFile.getCreateDate() == null){ todoInfoFile.setCreateDate( todoInfoFile.getUpdateDate() ); }
					}
				}
				todoInfoFileMapper.insertBatch(todoFileList);
			}
		}

		// 更新父级状态
		this.updateParentStatus(todoInfo);
	}
	/**
	 * 更新当前级别、子级、父级  状态
	 * @param todoInfo
	 */
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public void updateStatus(TodoInfo todoInfo){
		mapper.updateCurrentAndChildrenStatus(todoInfo);
		// 更新父级状态
		this.updateParentStatus(todoInfo);
	}

    /**
     * 更新父级状态
     * @param todoInfo
     */
    @Transactional(readOnly = false, rollbackFor = Exception.class)
    public void updateParentStatus(TodoInfo todoInfo){
        if(todoInfo.getParent() != null && !"0".equals(todoInfo.getParentId())){
            // 父级待办事项
            TodoInfo parentTodo =mapper.get(todoInfo.getParent());
            if (parentTodo != null && StringUtils.isNotBlank(parentTodo.getId()) && StringUtils.isNotBlank(parentTodo.getStatus())){
                TodoInfo queryTodo = todoInfo.getParent();
                queryTodo.setStatus(CaseConstant.TODO_STATUS_WAIT);
                Integer waitStatusCount = mapper.getCountByParentId(queryTodo);

                String status = (waitStatusCount > 0)? CaseConstant.TODO_STATUS_WAIT : CaseConstant.TODO_STATUS_COMPLETE;
                if(!status.equals(parentTodo.getStatus())){
					// 设置完成时间 与 状态
					if(CaseConstant.TODO_STATUS_COMPLETE.equals(status)){
						parentTodo.setCompleteDate(new Date());
					}else {
						parentTodo.setCompleteDate(null);
					}
                    parentTodo.setStatus(status);
                    // 更新当前级别状态
                    mapper.updateCurrentStatus(parentTodo);
                    // 更新当前级的父级状态
                    this.updateParentStatus(parentTodo);
                }
            }
        }
    }

	/**
	 * 根据关联关系删除 待办事项
	 * @param relevanceType
	 * @param relevanceId
	 * @param caseStage
	 */
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public void deleteByRelevance(String relevanceType, String relevanceId, CaseStage caseStage){
		TodoInfo todoInfo = new TodoInfo();
		todoInfo.setRelevanceType(relevanceType);
		todoInfo.setRelevanceId(relevanceId);
		todoInfo.setStage(caseStage);
		mapper.deleteByRelevance(todoInfo);
	}
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public void deleteByRelevance(TodoInfo todoInfo){
		mapper.deleteByRelevance(todoInfo);
	}

	@Transactional(readOnly = false)
	public void delete(TodoInfo todoInfo) {
		// 删除待办 附件
		List<TodoInfoFile> fileList = todoInfoFileMapper.findListByTodo(todoInfo.getId());
		for (TodoInfoFile todoInfoFile : fileList) {
			this.deleteFile(todoInfoFile);
		}
		// 删除待办事项
		super.delete(todoInfo);
        // 更新父级状态
        this.updateParentStatus(todoInfo);
	}

	/**
	 * 根据 关联类型、关联id 删除所有数据 待办记录、待办附件信息
	 * @param relevanceType
	 * @param relevanceId
	 */
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public void deleteAllDataByRelevance(String relevanceType, String relevanceId) {
		// 删除附件信息
		List<TodoInfoFile> fileList = todoInfoFileMapper.findListByTodoRelevance(relevanceType, relevanceId);
		for (TodoInfoFile todoInfoFile : fileList) {
			this.deleteFile(todoInfoFile);
		}
		// 删除 待办事项
		deleteByRelevance(relevanceType, relevanceId, null);
	}

	/**
	 * 保存附件信息
	 * @param todoInfoFile
	 */
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public void saveFile(TodoInfoFile todoInfoFile) {
		if (todoInfoFile.getIsNewRecord()){
			todoInfoFile.preInsert();
			todoInfoFileMapper.insert(todoInfoFile);
		}else{
			todoInfoFile.preUpdate();
			todoInfoFileMapper.update(todoInfoFile);
		}
	}

	/**
	 * 删除附件信息
	 * @param id
	 */
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public void deleteFile(String id) {
		this.deleteFile(todoInfoFileMapper.get(id));
	}
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public void deleteFile(TodoInfoFile todoInfoFile) {
		if(todoInfoFile != null && StringUtils.isNotBlank(todoInfoFile.getId())){
			todoInfoFileMapper.delete(todoInfoFile);
			// 删除文件
			FileKit.deleteFileByUrl(todoInfoFile.getPath());
		}
	}

	public void saveBatchFiles(List<TodoInfoFile> todoInfoFiles) {
		todoInfoFileMapper.insertBatch(todoInfoFiles);
	}

	public Integer getMaxSortByTid(String id) {
		return todoInfoFileMapper.getMaxSortByTid(id);
	}
}
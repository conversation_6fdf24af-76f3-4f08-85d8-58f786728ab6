/**
 * Copyright © 2015-2020 <a href="http://www.jeeplus.org/">JeePlus</a> All rights reserved.
 */
package com.jeeplus.modules.lawcase.entity;


import com.jeeplus.core.persistence.DataEntity;
import com.jeeplus.common.utils.excel.annotation.ExcelField;
import lombok.Data;

/**
 * 阶段信息Entity
 * <AUTHOR>
 * @version 2021-08-02
 */
@Data
public class Stage extends DataEntity<Stage> {
	
	private static final long serialVersionUID = 1L;
	@ExcelField(title="模版id", align=2, sort=7)
	private StageTemplate stageTemplate;		// 模版id
	@ExcelField(title="名称", align=2, sort=8)
	private String name;		// 名称
	@ExcelField(title="排序", align=2, sort=9)
	private Integer sort;		// 排序
	
	public Stage() {
		super();
	}
	
	public Stage(String id){
		super(id);
	}
}
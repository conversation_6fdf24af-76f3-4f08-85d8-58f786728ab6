/**
 * Copyright © 2015-2020 <a href="http://www.jeeplus.org/">JeePlus</a> All rights reserved.
 */
package com.jeeplus.modules.lawcase.web;

import java.util.ArrayList;
import java.util.List;

import com.jeeplus.modules.lawcase.entity.Case;
import com.jeeplus.modules.lawcase.service.CaseService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.google.common.collect.Lists;
import com.jeeplus.common.json.AjaxJson;
import com.jeeplus.core.web.BaseController;
import com.jeeplus.common.utils.StringUtils;
import com.jeeplus.modules.lawcase.entity.CaseFileDirectory;
import com.jeeplus.modules.lawcase.service.CaseFileDirectoryService;

/**
 * 案件文档目录Controller
 * <AUTHOR>
 * @version 2021-08-12
 */
@RestController
@Api(tags = "案件-文档目录")
@RequestMapping(value = "/lawcase/caseFileDirectory")
public class CaseFileDirectoryController extends BaseController {

	@Autowired
	private CaseFileDirectoryService caseFileDirectoryService;
	@Autowired
	private CaseService caseService;

	@ModelAttribute
	public CaseFileDirectory get(@RequestParam(required=false) String id) {
		CaseFileDirectory entity = null;
		if (StringUtils.isNotBlank(id)){
			entity = caseFileDirectoryService.get(id);
		}
		if (entity == null){
			entity = new CaseFileDirectory();
		}
		return entity;
	}


	/**
	 * 文档目录树表数据
	 */
	@RequiresPermissions("lawcase:caseFileDirectory:list")
	@GetMapping("list")
	public AjaxJson list(CaseFileDirectory caseFileDirectory) {
		return AjaxJson.success().put("list", caseFileDirectoryService.findList(caseFileDirectory));
	}

	/**
	 * 根据Id获取文档目录数据
	 */
	@RequiresPermissions(value={"lawcase:caseFileDirectory:view","lawcase:caseFileDirectory:add","lawcase:caseFileDirectory:edit"},logical=Logical.OR)
	@GetMapping("queryById")
	public AjaxJson queryById(CaseFileDirectory caseFileDirectory) {
		return AjaxJson.success().put("caseFileDirectory", caseFileDirectory);
	}

	/**
	 * 保存文档目录
	 */
	@RequiresPermissions(value={"lawcase:caseFileDirectory:add","lawcase:caseFileDirectory:edit", "user"},logical=Logical.OR)
	@ApiOperation(value = "创建目录", consumes = "application/form-data")
	@ApiImplicitParams({
			@ApiImplicitParam(value = "id值", name = "id"),
			@ApiImplicitParam(value = "名称", name = "name", required = true),
			@ApiImplicitParam(value = "案件id", name = "lawCase.id", required = true),
			@ApiImplicitParam(value = "上级目录id", name = "parent.id")
	})
	@PostMapping("save")
	public AjaxJson save(CaseFileDirectory caseFileDirectory) throws Exception{
		/**
		 * 后台hibernate-validation插件校验
		 */
		String errMsg = beanValidator(caseFileDirectory);
		if (StringUtils.isNotBlank(errMsg)){
			return AjaxJson.error(errMsg);
		}
		if(StringUtils.isBlank(caseFileDirectory.getName())){
			return AjaxJson.error("名称不能为空");
		}
		if(caseFileDirectory.getLawCase() == null || StringUtils.isBlank(caseFileDirectory.getLawCase().getId())){
			return AjaxJson.error("案件信息有误");
		}
		if(caseFileDirectory.getParent() == null || StringUtils.isBlank(caseFileDirectory.getParent().getId())){
			CaseFileDirectory rootDir = caseFileDirectoryService.getRootByCase(caseFileDirectory.getLawCase().getId());
			caseFileDirectory.setParent(rootDir);
		}
		// 查询判断 该名称是否存在，若为修改时 需根据id查询旧数据进行名称比较
		boolean isCheck = true;
		if(StringUtils.isNotBlank(caseFileDirectory.getId())){
			CaseFileDirectory oldFileDir = caseFileDirectoryService.get(caseFileDirectory.getId());
			if(caseFileDirectory.getName().equals(oldFileDir.getName())){
				isCheck = false;
			}
		}
		if(isCheck){
			CaseFileDirectory queryDir = new CaseFileDirectory();
			queryDir.setParent(caseFileDirectory.getParent());
			queryDir.setQueryAccurateName(caseFileDirectory.getName());
			List<CaseFileDirectory> dirList = caseFileDirectoryService.findList(queryDir);
			if(dirList != null && dirList.size() > 0){
				return AjaxJson.error("该名称已存在");
			}
		}

		String msg = (StringUtils.isBlank(caseFileDirectory.getId()) ? "创建" : "修改");
		try {
			caseFileDirectoryService.save(caseFileDirectory);//保存
			return AjaxJson.success(msg +"成功");
		}catch (Exception e){
			return AjaxJson.error(msg +"失败");
		}
	}

	/**
	 * 删除文档目录
	 */
	@RequiresPermissions(value = {"lawcase:caseFileDirectory:del", "user"}, logical = Logical.OR)
	@ApiOperation(value = "目录删除", consumes = "application/form-data")
	@ApiImplicitParam(value = "目录id 多个以,拼接", name = "ids", required = true)
	@PostMapping("delete")
	public AjaxJson delete(String ids) {
		String idArray[] =ids.split(",");
		for(String id : idArray){
			CaseFileDirectory caseFileDirectory = caseFileDirectoryService.get(id);
			if(StringUtils.isNotBlank(caseFileDirectory.getId()) && !"0".equals(caseFileDirectory.getParentId())){
				caseFileDirectoryService.delete(caseFileDirectory);
			}
		}
		return AjaxJson.success("删除成功");
	}

	/**
     * 获取JSON树形数据。
     * @param lawCaseId 案件id
     * @return
	*/
	@RequiresPermissions("user")
	@ApiOperation(value = "文档目录", consumes = "application/form-data")
	@ApiImplicitParams({
			@ApiImplicitParam(value = "案件id", name = "lawCaseId", required = true),
			@ApiImplicitParam(value = "目录id，若为空则获取根目录信息", name = "directoryId")
	})
	@PostMapping("treeData")
	public AjaxJson treeData(@RequestParam(required = true) String lawCaseId, String directoryId) {
		List<CaseFileDirectory> rootTree = new ArrayList<CaseFileDirectory>();
		if(StringUtils.isNotBlank(lawCaseId)){
			Case lawCase = caseService.get(lawCaseId);
			if(lawCase != null && StringUtils.isNotBlank(lawCase.getId())){
				// 父级目录信息
				CaseFileDirectory parentDir = null;
				if(StringUtils.isNotBlank(directoryId)){
					parentDir = caseFileDirectoryService.get(directoryId);
				}else {
					parentDir = caseFileDirectoryService.getRootByCase(lawCaseId);
				}
				if(parentDir != null && StringUtils.isNotBlank(parentDir.getId())){
					// 是否根目录
					boolean isRoot = ("0".equals(parentDir.getParentId()));
					CaseFileDirectory caseFileDirectory = new CaseFileDirectory();
					caseFileDirectory.setLawCase(new Case(lawCaseId));
					// 若不是根目录
					if(!isRoot){
						caseFileDirectory.setParentIds(parentDir.getId());
					}
					List<CaseFileDirectory> list = caseFileDirectoryService.findList(caseFileDirectory);
					// 将list 转为树结构格式
					List<CaseFileDirectory> rootTreeList =  caseFileDirectoryService.formatListToTree (parentDir, list, "" );
					// 处理数据 只展示 子级信息
					for (CaseFileDirectory fileDirectory : rootTreeList) {
						rootTree.addAll(fileDirectory.getChildren());
					}
				}
			}
		}
		return AjaxJson.success().put("treeData", rootTree);
	}
}
/**
 * Copyright © 2015-2020 <a href="http://www.jeeplus.org/">JeePlus</a> All rights reserved.
 */
package com.jeeplus.modules.lawcase.web;

import java.util.List;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.ConstraintViolationException;

import com.jeeplus.modules.lawcase.entity.CaseHandleStrategy;
import com.jeeplus.modules.lawcase.service.CaseHandleStrategyService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import com.google.common.collect.Lists;
import com.jeeplus.common.utils.DateUtils;
import com.jeeplus.common.json.AjaxJson;
import com.jeeplus.core.persistence.Page;
import com.jeeplus.core.web.BaseController;
import com.jeeplus.common.utils.StringUtils;
import com.jeeplus.common.utils.excel.ExportExcel;
import com.jeeplus.common.utils.excel.ImportExcel;

/**
 * 案件办案策略Controller
 * <AUTHOR>
 * @version 2021-08-02
 */
@RestController
@Api(tags = "案件-办案策略")
@RequestMapping(value = "/case/caseHandleStrategy")
public class CaseHandleStrategyController extends BaseController {

	@Autowired
	private CaseHandleStrategyService caseHandleStrategyService;

	@ModelAttribute
	public CaseHandleStrategy get(@RequestParam(required=false) String id) {
		CaseHandleStrategy entity = null;
		if (StringUtils.isNotBlank(id)){
			entity = caseHandleStrategyService.get(id);
		}
		if (entity == null){
			entity = new CaseHandleStrategy();
		}
		return entity;
	}

	/**
	 * 办案策略列表数据
	 */
//	@RequiresPermissions("case:caseHandleStrategy:list")
	@ApiOperation(value = "案件办案策略数据列表", consumes = "application/form-data")
	@ApiImplicitParam(value = "案件id值", name = "lawCase.id", required = true)
	@PostMapping("list")
	public AjaxJson list(CaseHandleStrategy caseHandleStrategy, HttpServletRequest request, HttpServletResponse response) {
		Page<CaseHandleStrategy> page = new Page<CaseHandleStrategy>(request, response, -1);
		if(caseHandleStrategy.getLawCase() != null && StringUtils.isNotBlank(caseHandleStrategy.getLawCase().getId())){
			page = caseHandleStrategyService.findPage(page, caseHandleStrategy);
		}
		return AjaxJson.success().put("page",page);
	}

	/**
	 * 根据Id获取办案策略数据
	 */
//	@RequiresPermissions(value={"case:caseHandleStrategy:view","case:caseHandleStrategy:add","case:caseHandleStrategy:edit"},logical=Logical.OR)
	@ApiOperation(value = "案件办案策略详情", consumes = "application/form-data")
	@ApiImplicitParam(value = "id值", name = "id", required = true)
	@PostMapping("queryById")
	public AjaxJson queryById(CaseHandleStrategy caseHandleStrategy) {
		return AjaxJson.success().put("caseHandleStrategy", caseHandleStrategy);
	}

	/**
	 * 保存办案策略
	 */
	@RequiresPermissions(value={"case:caseHandleStrategy:add","case:caseHandleStrategy:edit", "user"},logical=Logical.OR)
	@ApiOperation(value = "案件办案策略保存")
	@ApiImplicitParams({
			@ApiImplicitParam(value = "id值", name = "id", required = false),
			@ApiImplicitParam(value = "案件id值", name = "lawCase.id", required = true),
			@ApiImplicitParam(value = "问题描述", name = "question", required = true),
			@ApiImplicitParam(value = "解决方案", name = "solution", required = true),
			@ApiImplicitParam(value = "结果", name = "result", required = true)
	})
	@PostMapping("save")
	public AjaxJson save(@RequestBody CaseHandleStrategy caseHandleStrategy) throws Exception{
		/**
		 * 后台hibernate-validation插件校验
		 */
		String errMsg = beanValidator(caseHandleStrategy);
		if (StringUtils.isNotBlank(errMsg)){
			return AjaxJson.error(errMsg);
		}
		if(caseHandleStrategy.getLawCase() == null || StringUtils.isBlank(caseHandleStrategy.getLawCase().getId())){
			return AjaxJson.error("案件信息有误");
		}
		//新增或编辑表单保存
		caseHandleStrategyService.save(caseHandleStrategy);//保存
		return AjaxJson.success("保存办案策略成功");
	}


	/**
	 * 批量删除办案策略
	 */
	@RequiresPermissions(value = {"case:caseHandleStrategy:del", "user"}, logical = Logical.OR)
	@ApiOperation(value = "案件办案策略删除", consumes = "application/form-data")
	@ApiImplicitParam(value = "id值拼接以,拼接字符串", name = "ids", required = true)
	@PostMapping("delete")
	public AjaxJson delete(String ids) {
		String idArray[] =ids.split(",");
		for(String id : idArray){
			caseHandleStrategyService.delete(new CaseHandleStrategy(id));
		}
		return AjaxJson.success("删除办案策略成功");
	}

	/**
	 * 导出excel文件
	 */
	@RequiresPermissions("case:caseHandleStrategy:export")
    @GetMapping("export")
    public AjaxJson exportFile(CaseHandleStrategy caseHandleStrategy, HttpServletRequest request, HttpServletResponse response) {
		try {
            String fileName = "办案策略"+DateUtils.getDate("yyyyMMddHHmmss")+".xlsx";
            Page<CaseHandleStrategy> page = caseHandleStrategyService.findPage(new Page<CaseHandleStrategy>(request, response, -1), caseHandleStrategy);
    		new ExportExcel("办案策略", CaseHandleStrategy.class).setDataList(page.getList()).write(response, fileName).dispose();
    		return null;
		} catch (Exception e) {
			return AjaxJson.error("导出办案策略记录失败！失败信息："+e.getMessage());
		}
    }

	/**
	 * 导入Excel数据

	 */
	@RequiresPermissions("case:caseHandleStrategy:import")
    @PostMapping("import")
   	public AjaxJson importFile(@RequestParam("file")MultipartFile file, HttpServletResponse response, HttpServletRequest request) {
		try {
			int successNum = 0;
			int failureNum = 0;
			StringBuilder failureMsg = new StringBuilder();
			ImportExcel ei = new ImportExcel(file, 1, 0);
			List<CaseHandleStrategy> list = ei.getDataList(CaseHandleStrategy.class);
			for (CaseHandleStrategy caseHandleStrategy : list){
				try{
					caseHandleStrategyService.save(caseHandleStrategy);
					successNum++;
				}catch(ConstraintViolationException ex){
					failureNum++;
				}catch (Exception ex) {
					failureNum++;
				}
			}
			if (failureNum>0){
				failureMsg.insert(0, "，失败 "+failureNum+" 条办案策略记录。");
			}
			return AjaxJson.success( "已成功导入 "+successNum+" 条办案策略记录"+failureMsg);
		} catch (Exception e) {
			return AjaxJson.error("导入办案策略失败！失败信息："+e.getMessage());
		}
    }

	/**
	 * 下载导入办案策略数据模板
	 */
	@RequiresPermissions("case:caseHandleStrategy:import")
    @GetMapping("import/template")
     public AjaxJson importFileTemplate(HttpServletResponse response) {
		try {
            String fileName = "办案策略数据导入模板.xlsx";
    		List<CaseHandleStrategy> list = Lists.newArrayList();
    		new ExportExcel("办案策略数据", CaseHandleStrategy.class, 1).setDataList(list).write(response, fileName).dispose();
    		return null;
		} catch (Exception e) {
			return AjaxJson.error( "导入模板下载失败！失败信息："+e.getMessage());
		}
    }


}
/**
 * Copyright © 2015-2020 <a href="http://www.jeeplus.org/">JeePlus</a> All rights reserved.
 */
package com.jeeplus.modules.lawcase.entity;


import com.jeeplus.core.persistence.DataEntity;
import com.jeeplus.common.utils.excel.annotation.ExcelField;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 客户联系人Entity
 * <AUTHOR>
 * @version 2021-08-02
 */
@Data
public class CustomerContacts extends DataEntity<CustomerContacts> {
	
	private static final long serialVersionUID = 1L;
	@ExcelField(title="客户信息", value = "customer.name", align=2, sort=7)
	private Customer customer;		// 客户信息id
	@NotBlank(message = "姓名不能为空")
	@ExcelField(title="姓名", align=2, sort=8)
	private String name;		// 姓名
	@ExcelField(title="性别", dictType="sex", align=2, sort=9)
	private String sex;		// 性别
	@ExcelField(title="邮箱", align=2, sort=10)
	private String email;		// 邮箱
	@ExcelField(title="联系电话", align=2, sort=11)
	private String phone;		// 联系电话
	@ExcelField(title="职务", align=2, sort=12)
	private String post;		// 职务
	@ExcelField(title="地址", align=2, sort=13)
	private String address;		// 地址
	
	public CustomerContacts() {
		super();
	}
	
	public CustomerContacts(String id){
		super(id);
	}
}
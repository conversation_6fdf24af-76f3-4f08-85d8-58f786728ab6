/**
 * Copyright © 2015-2020 <a href="http://www.jeeplus.org/">JeePlus</a> All rights reserved.
 */
package com.jeeplus.modules.lawcase.service;

import java.util.List;

import com.jeeplus.common.utils.StringUtils;
import com.jeeplus.modules.sys.utils.FileKit;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.jeeplus.core.persistence.Page;
import com.jeeplus.core.service.CrudService;
import com.jeeplus.modules.lawcase.entity.CaseFile;
import com.jeeplus.modules.lawcase.mapper.CaseFileMapper;

/**
 * 案件文档Service
 * <AUTHOR>
 * @version 2021-08-13
 */
@Service
@Transactional(readOnly = true)
public class CaseFileService extends CrudService<CaseFileMapper, CaseFile> {

	public CaseFile get(String id) {
		return super.get(id);
	}
	
	public List<CaseFile> findList(CaseFile caseFile) {
		return super.findList(caseFile);
	}
	
	public Page<CaseFile> findPage(Page<CaseFile> page, CaseFile caseFile) {
		return super.findPage(page, caseFile);
	}

	/**
	 * 根据案件id 查询案件文档信息
	 * @param caseId
	 * @return
	 */
	public List<CaseFile> findFileListByCase(String caseId){
		return mapper.findFileListByCase(caseId);
	}

	@Transactional(readOnly = false)
	public void save(CaseFile caseFile) {
		super.save(caseFile);
	}
	
	@Transactional(readOnly = false)
	public void delete(CaseFile caseFile) {
		super.delete(caseFile);
		if(StringUtils.isNotBlank(caseFile.getPath())){
			FileKit.deleteFileByUrl(caseFile.getPath());
		}
	}
	
}
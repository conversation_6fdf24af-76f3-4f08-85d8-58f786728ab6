/**
 * Copyright &copy; 2015-2020 <a href="http://www.jeesharp.org/">jeesharp</a> All rights reserved.
 */
package com.jeeplus.modules.lawcase.mapper;

import com.jeeplus.core.persistence.BaseMapper;
import com.jeeplus.modules.lawcase.entity.Version;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

/**
 * 版本信息MAPPER接口
 * <AUTHOR>
 * @version 2021-10-20
 */
@Mapper
@Repository
public interface VersionMapper extends BaseMapper<Version> {
    /**
     * 根据ids 更新版本使用状态
     * @param isEnable
     * @param ids
     * @return
     */
    int updateEnable(@Param("isEnable")String isEnable, @Param("ids") String ids);
}
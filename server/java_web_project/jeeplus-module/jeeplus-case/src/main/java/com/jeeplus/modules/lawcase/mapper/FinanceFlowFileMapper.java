/**
 * Copyright © 2015-2020 <a href="http://www.jeeplus.org/">JeePlus</a> All rights reserved.
 */
package com.jeeplus.modules.lawcase.mapper;

import com.jeeplus.core.persistence.BaseMapper;
import com.jeeplus.modules.lawcase.entity.FinanceFlowFile;
import com.jeeplus.modules.lawcase.entity.TodoInfoFile;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 财务流水记录- 附件 MAPPER接口
 * <AUTHOR>
 * @version 2021-08-12
 */
@Mapper
@Repository
public interface FinanceFlowFileMapper extends BaseMapper<FinanceFlowFile> {
    /**
     * 根据 财务流水记录id 删除附件信息
     * @param financeFlowId
     * @return
     */
    int deleteByFinanceFlow(String financeFlowId);

}
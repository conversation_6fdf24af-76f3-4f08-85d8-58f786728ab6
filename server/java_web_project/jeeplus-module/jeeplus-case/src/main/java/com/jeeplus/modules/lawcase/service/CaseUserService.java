/**
 * Copyright © 2015-2020 <a href="http://www.jeeplus.org/">JeePlus</a> All rights reserved.
 */
package com.jeeplus.modules.lawcase.service;

import com.jeeplus.common.utils.StringUtils;
import com.jeeplus.config.properties.JeePlusProperites;
import com.jeeplus.core.persistence.Page;
import com.jeeplus.core.service.CrudService;
import com.jeeplus.modules.lawcase.entity.Case;
import com.jeeplus.modules.lawcase.entity.CaseUser;
import com.jeeplus.modules.lawcase.entity.Stage;
import com.jeeplus.modules.lawcase.mapper.CaseUserMapper;
import com.jeeplus.modules.lawcase.mapper.StageMapper;
import com.jeeplus.modules.lawcase.util.TreeUtils;
import com.jeeplus.modules.lawcase.vo.StageRecordVO;
import com.jeeplus.modules.lawcase.vo.StageVO;
import com.jeeplus.modules.sys.entity.User;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 案件成员 Service
 * <AUTHOR>
 * @version 2021-08-02
 */
@Service
@Transactional(readOnly = true)
public class CaseUserService extends CrudService<CaseUserMapper, CaseUser> {

	@Autowired
	private CaseService caseService;

	public CaseUser get(String id) {
		return super.get(id);
	}
	
	public List<CaseUser> findList(CaseUser caseUser) {
		return super.findList(caseUser);
	}
	
	public Page<CaseUser> findPage(Page<CaseUser> page, CaseUser caseUser) {
		return super.findPage(page, caseUser);
	}

	/**
	 * 获取用户信息
	 * @param caseId
	 * @param name
	 * @return
	 */
	public List<User> findUserList(String caseId, String name){
		return mapper.findUserList(caseId, name);
	}

	@Transactional(readOnly = false)
	public void save(CaseUser caseUser) {
		super.save(caseUser);
		Case lawCase = caseUser.getLawCase();
		if(!JeePlusProperites.YES.equals(lawCase.getIsShare())){
			lawCase.setIsShare(JeePlusProperites.YES);
			caseService.updateIsShare(lawCase);
		}
	}

	@Transactional(readOnly = false)
	public void delete(CaseUser caseUser) {
		super.delete(caseUser);
		// 判断案件成员数量 若为0 则判断修改案件共享状态
		if (caseUser.getLawCase() != null && StringUtils.isNotBlank(caseUser.getLawCase().getId())){
			Integer count = mapper.getCountByCase(caseUser.getLawCase().getId());
			if(count == 0){
				Case lawCase = caseService.get(caseUser.getLawCase());
				if(!JeePlusProperites.NO.equals(lawCase.getIsShare())){
					lawCase.setIsShare(JeePlusProperites.NO);
					caseService.updateIsShare(lawCase);
				}
			}
		}
	}

	/**
	 * 根据案件删除案件成员
	 * @param caseId
	 */
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public void deleteByCase(String caseId) {
		mapper.deleteByCase(caseId);
	}
}
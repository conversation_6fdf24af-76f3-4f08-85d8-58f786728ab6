/**
 * Copyright © 2015-2020 <a href="http://www.jeeplus.org/">JeePlus</a> All rights reserved.
 */
package com.jeeplus.modules.lawcase.entity;


import com.jeeplus.core.persistence.DataEntity;
import com.jeeplus.modules.sys.entity.User;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 案件成员 Entity
 * <AUTHOR>
 * @version 2021-08-02
 */
@Data
public class CaseUser extends DataEntity<CaseUser> {

	private static final long serialVersionUID = 1L;
	@NotNull(message = "案件信息不能为空")
	private Case lawCase;			// 案件信息
	@NotNull(message = "成员信息不能为空")
	private User user;		// 成员信息

	public CaseUser() {
		super();
	}

	public CaseUser(String id){
		super(id);
	}
}
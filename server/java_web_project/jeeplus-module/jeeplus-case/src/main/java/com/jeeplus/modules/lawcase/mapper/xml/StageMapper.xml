<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jeeplus.modules.lawcase.mapper.StageMapper">

	<resultMap id="stageVoResult" type="com.jeeplus.modules.lawcase.vo.StageVO">
		<result property="id" column="id" />
		<result property="stageTemplateId" column="stageTemplateId" />
		<result property="name" column="name" />
		<result property="sort" column="sort" />
		<collection property="stageRecordList" ofType="com.jeeplus.modules.lawcase.vo.StageRecordVO">
			<id property="id" column="record.id" />
			<result property="name" column="record.name" />
			<result property="content" column="record.content" />
			<result property="sort" column="record.sort" />
			<result property="type" column="record.type" />
				<result property="nums" column="record.nums" />
			<result property="isNotAuditProhibit" column="record.isNotAuditProhibit" />
			<result property="parentId" column="record.parentId" />
		</collection>
	</resultMap>

	<sql id="stageColumns">
		a.id AS "id",
		a.create_by AS "createBy.id",
		a.create_date AS "createDate",
		a.update_by AS "updateBy.id",
		a.update_date AS "updateDate",
		a.remarks AS "remarks",
		a.del_flag AS "delFlag",
		a.stage_template_id AS "stageTemplate.id",
		a.name AS "name",
		a.sort AS "sort"
	</sql>

	<sql id="stageJoins">

	</sql>


	<select id="get" resultType="Stage" >
		SELECT
			<include refid="stageColumns"/>
		FROM law_stage a
		<include refid="stageJoins"/>
		WHERE a.id = #{id}
	</select>

	<select id="findList" resultType="Stage" >
		SELECT
			<include refid="stageColumns"/>
		FROM law_stage a
		<include refid="stageJoins"/>
		<where>
			a.del_flag = #{DEL_FLAG_NORMAL}
			${dataScope}
			<if test="stageTemplate != null and stageTemplate.id != null and stageTemplate.id != ''">
				AND a.stage_template_id = #{stageTemplate.id}
			</if>
		</where>
		<choose>
			<when test="page !=null and page.orderBy != null and page.orderBy != ''">
				ORDER BY ${page.orderBy}
			</when>
			<otherwise>
				ORDER BY a.update_date DESC
			</otherwise>
		</choose>
	</select>

	<select id="findListByTemplate" resultMap="stageVoResult" >
		SELECT a.*
			, re.id AS "record.id", re.type AS "record.type", re.name AS "record.name", re.content AS "record.content", re.nums AS "record.nums"
			, re.sort AS "record.sort", re.parentId AS "record.parentId", re.isNotAuditProhibit AS "record.isNotAuditProhibit"
		FROM(
			SELECT
				a.id AS "id", a.remarks AS "remarks",
				a.stage_template_id AS "stageTemplateId",
				a.name AS "name", a.sort AS "sort"
			FROM law_stage a
			WHERE a.del_flag = #{DEL_FLAG_NORMAL} AND a.stage_template_id = #{stageTemplate.id}
		) a
		LEFT JOIN(
			SELECT  a.id AS "id", a.remarks AS "remarks", a.stage_id AS "stageId", a.is_not_audit_prohibit AS "isNotAuditProhibit",
				a.type AS "type", a.name AS "name", a.content AS "content", a.sort AS "sort", a.parent_id AS "parentId", a.nums AS "nums"
			FROM law_stage_record a
			WHERE a.del_flag = #{DEL_FLAG_NORMAL} AND a.stage_template_id = #{stageTemplate.id}
		) re ON a.id = re.stageId
		ORDER BY a.sort, re.stageId, re.sort
	</select>

	<select id="findAllList" resultType="Stage" >
		SELECT
			<include refid="stageColumns"/>
		FROM law_stage a
		<include refid="stageJoins"/>
		<where>
			a.del_flag = #{DEL_FLAG_NORMAL}
			${dataScope}
		</where>
		<choose>
			<when test="page !=null and page.orderBy != null and page.orderBy != ''">
				ORDER BY ${page.orderBy}
			</when>
			<otherwise>
				ORDER BY a.update_date DESC
			</otherwise>
		</choose>
	</select>

	<insert id="insert">
		INSERT INTO law_stage(
			id,
			create_by,
			create_date,
			update_by,
			update_date,
			remarks,
			del_flag,
			stage_template_id,
			`name`,
			sort
		) VALUES (
			#{id},
			#{createBy.id},
			#{createDate},
			#{updateBy.id},
			#{updateDate},
			#{remarks},
			#{delFlag},
			#{stageTemplate.id},
			#{name},
			#{sort}
		)
	</insert>

	<insert id="insertBatch">
		INSERT INTO law_stage(
			id,
			create_by,
			create_date,
			update_by,
			update_date,
			remarks,
			del_flag,
			stage_template_id,
			`name`,
			sort
		) VALUES
		<foreach collection="list" item="item" separator=" , ">
			(
			#{item.id},
			#{item.createBy.id},
			#{item.createDate},
			#{item.updateBy.id},
			#{item.updateDate},
			#{item.remarks},
			#{item.delFlag},
			#{item.stageTemplate.id},
			#{item.name},
			#{item.sort}
			)
		</foreach>

	</insert>

	<update id="update">
		UPDATE law_stage SET
			update_by = #{updateBy.id},
			update_date = #{updateDate},
			remarks = #{remarks},
			stage_template_id = #{stageTemplate.id},
			`name` = #{name},
			sort = #{sort}
		WHERE id = #{id}
	</update>


	<!--物理删除-->
	<update id="delete">
		DELETE FROM law_stage
		WHERE id = #{id}
	</update>

	<update id="deleteByTemplate">
		DELETE FROM law_stage WHERE stage_template_id = #{templateId}
	</update>

	<!--逻辑删除-->
	<update id="deleteByLogic">
		UPDATE law_stage SET
			del_flag = #{DEL_FLAG_DELETE}
		WHERE id = #{id}
	</update>


	<!-- 根据实体名称和字段名称和字段值获取唯一记录 -->
	<select id="findUniqueByProperty" resultType="Stage">
		select * FROM law_stage  where ${propertyName} = #{value}
	</select>

</mapper>
/**
 * Copyright © 2015-2020 <a href="http://www.jeeplus.org/">JeePlus</a> All rights reserved.
 */
package com.jeeplus.modules.lawcase.web;

import java.util.List;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.ConstraintViolationException;

import com.jeeplus.modules.lawcase.entity.CaseTrialRecord;
import com.jeeplus.modules.lawcase.service.CaseTrialRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import com.google.common.collect.Lists;
import com.jeeplus.common.utils.DateUtils;
import com.jeeplus.common.json.AjaxJson;
import com.jeeplus.core.persistence.Page;
import com.jeeplus.core.web.BaseController;
import com.jeeplus.common.utils.StringUtils;
import com.jeeplus.common.utils.excel.ExportExcel;
import com.jeeplus.common.utils.excel.ImportExcel;

/**
 * 庭审记录Controller
 * <AUTHOR>
 * @version 2021-08-02
 */
@RestController
@Api(tags = "案件-庭审记录")
@RequestMapping(value = "/case/caseTrialRecord")
public class CaseTrialRecordController extends BaseController {

	@Autowired
	private CaseTrialRecordService caseTrialRecordService;

	@ModelAttribute
	public CaseTrialRecord get(@RequestParam(required=false) String id) {
		CaseTrialRecord entity = null;
		if (StringUtils.isNotBlank(id)){
			entity = caseTrialRecordService.get(id);
		}
		if (entity == null){
			entity = new CaseTrialRecord();
		}
		return entity;
	}

	/**
	 * 庭审记录列表数据
	 */
	@RequiresPermissions(value = {"case:caseTrialRecord:list", "user"}, logical = Logical.OR)
	@ApiOperation(value = "列表数据", consumes = "application/form-data")
	@ApiImplicitParams({
			@ApiImplicitParam(value = "案件id值", name = "lawCase.id", required = true)
	})
	@PostMapping("list")
	public AjaxJson list(CaseTrialRecord caseTrialRecord, HttpServletRequest request, HttpServletResponse response) {
		Page<CaseTrialRecord> page = new Page<CaseTrialRecord>(request, response, -1);
		if(caseTrialRecord.getLawCase() != null && StringUtils.isNotBlank(caseTrialRecord.getLawCase().getId())){
			page = caseTrialRecordService.findPage(page, caseTrialRecord);
		}
		return AjaxJson.success().put("page",page);
	}

	/**
	 * 根据Id获取庭审记录数据
	 */
	@RequiresPermissions(value={"case:caseTrialRecord:view","case:caseTrialRecord:add","case:caseTrialRecord:edit", "user"},logical=Logical.OR)
	@ApiOperation(value = "详情", consumes = "application/form-data")
	@ApiImplicitParam(value = "id值", name = "id", required = true)
	@PostMapping("queryById")
	public AjaxJson queryById(CaseTrialRecord caseTrialRecord) {
		return AjaxJson.success().put("caseTrialRecord", caseTrialRecord);
	}

	/**
	 * 保存庭审记录
	 */
	@RequiresPermissions(value={"case:caseTrialRecord:add","case:caseTrialRecord:edit", "user"},logical=Logical.OR)
	@ApiOperation(value = "保存")
	@PostMapping("save")
	public AjaxJson save(@RequestBody CaseTrialRecord caseTrialRecord) throws Exception{
		/**
		 * 后台hibernate-validation插件校验
		 */
		String errMsg = beanValidator(caseTrialRecord);
		if (StringUtils.isNotBlank(errMsg)){
			return AjaxJson.error(errMsg);
		}
		if(caseTrialRecord.getBarrister() == null || StringUtils.isBlank(caseTrialRecord.getBarrister().getId())){
			return AjaxJson.error("出庭律师不能为空");
		}
		if(caseTrialRecord.getLawCase() == null || StringUtils.isBlank(caseTrialRecord.getLawCase().getId())){
			return AjaxJson.error("案件信息有误");
		}
		//新增或编辑表单保存
		caseTrialRecordService.save(caseTrialRecord);//保存
		return AjaxJson.success("保存成功");
	}


	/**
	 * 批量删除庭审记录
	 */
	@RequiresPermissions(value = {"case:caseTrialRecord:del", "user"}, logical = Logical.OR)
	@ApiOperation(value = "删除", consumes = "application/form-data")
	@ApiImplicitParam(value = "id值以,拼接", name = "ids", required = true)
	@PostMapping("delete")
	public AjaxJson delete(String ids) {
		String idArray[] =ids.split(",");
		for(String id : idArray){
			caseTrialRecordService.delete(new CaseTrialRecord(id));
		}
		return AjaxJson.success("删除成功");
	}

	/**
	 * 导出excel文件
	 */
	@RequiresPermissions("case:caseTrialRecord:export")
    @GetMapping("export")
    public AjaxJson exportFile(CaseTrialRecord caseTrialRecord, HttpServletRequest request, HttpServletResponse response) {
		try {
            String fileName = "庭审记录"+DateUtils.getDate("yyyyMMddHHmmss")+".xlsx";
            Page<CaseTrialRecord> page = caseTrialRecordService.findPage(new Page<CaseTrialRecord>(request, response, -1), caseTrialRecord);
    		new ExportExcel("庭审记录", CaseTrialRecord.class).setDataList(page.getList()).write(response, fileName).dispose();
    		return null;
		} catch (Exception e) {
			return AjaxJson.error("导出庭审记录记录失败！失败信息："+e.getMessage());
		}
    }

	/**
	 * 导入Excel数据

	 */
	@RequiresPermissions("case:caseTrialRecord:import")
    @PostMapping("import")
   	public AjaxJson importFile(@RequestParam("file")MultipartFile file, HttpServletResponse response, HttpServletRequest request) {
		try {
			int successNum = 0;
			int failureNum = 0;
			StringBuilder failureMsg = new StringBuilder();
			ImportExcel ei = new ImportExcel(file, 1, 0);
			List<CaseTrialRecord> list = ei.getDataList(CaseTrialRecord.class);
			for (CaseTrialRecord caseTrialRecord : list){
				try{
					caseTrialRecordService.save(caseTrialRecord);
					successNum++;
				}catch(ConstraintViolationException ex){
					failureNum++;
				}catch (Exception ex) {
					failureNum++;
				}
			}
			if (failureNum>0){
				failureMsg.insert(0, "，失败 "+failureNum+" 条庭审记录记录。");
			}
			return AjaxJson.success( "已成功导入 "+successNum+" 条庭审记录记录"+failureMsg);
		} catch (Exception e) {
			return AjaxJson.error("导入庭审记录失败！失败信息："+e.getMessage());
		}
    }

	/**
	 * 下载导入庭审记录数据模板
	 */
	@RequiresPermissions("case:caseTrialRecord:import")
    @GetMapping("import/template")
     public AjaxJson importFileTemplate(HttpServletResponse response) {
		try {
            String fileName = "庭审记录数据导入模板.xlsx";
    		List<CaseTrialRecord> list = Lists.newArrayList();
    		new ExportExcel("庭审记录数据", CaseTrialRecord.class, 1).setDataList(list).write(response, fileName).dispose();
    		return null;
		} catch (Exception e) {
			return AjaxJson.error( "导入模板下载失败！失败信息："+e.getMessage());
		}
    }


}
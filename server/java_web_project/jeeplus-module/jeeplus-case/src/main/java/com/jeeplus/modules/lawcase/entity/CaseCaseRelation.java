/**
 * Copyright © 2015-2020 <a href="http://www.jeeplus.org/">JeePlus</a> All rights reserved.
 */
package com.jeeplus.modules.lawcase.entity;


import com.jeeplus.common.utils.excel.annotation.ExcelField;
import com.jeeplus.core.persistence.DataEntity;
import lombok.Data;

/**
 * 案件与案件关联 Entity
 * <AUTHOR>
 * @version 2021-08-02
 */
@Data
public class CaseCaseRelation extends DataEntity<CaseCaseRelation> {

	private static final long serialVersionUID = 1L;
	private Case lawCase;			// 案件信息
	private Case relationCase;		// 关联案件

	public CaseCaseRelation() {
		super();
	}

	public CaseCaseRelation(Case lawCase) {
		this.lawCase = lawCase;
	}

	public CaseCaseRelation(String id){
		super(id);
	}
}
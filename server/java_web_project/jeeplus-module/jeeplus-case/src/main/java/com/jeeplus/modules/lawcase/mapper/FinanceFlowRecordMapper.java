/**
 * Copyright © 2015-2020 <a href="http://www.jeeplus.org/">JeePlus</a> All rights reserved.
 */
package com.jeeplus.modules.lawcase.mapper;

import com.jeeplus.modules.lawcase.vo.CaseFinanceVO;
import org.springframework.stereotype.Repository;
import com.jeeplus.core.persistence.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import com.jeeplus.modules.lawcase.entity.FinanceFlowRecord;

import java.util.List;

/**
 * 财务流水记录MAPPER接口
 * <AUTHOR>
 * @version 2021-10-29
 */
@Mapper
@Repository
public interface FinanceFlowRecordMapper extends BaseMapper<FinanceFlowRecord> {
    /**
     * 根据案件id 查询案件财务统计情况
     * @param caseId
     * @return
     */
    CaseFinanceVO getCaseFinance (String caseId);

    /**
     * 案件财务信息统计  数据列表
     * @param caseFinance
     * @return
     */
    List<CaseFinanceVO> findCaseFinanceList(CaseFinanceVO caseFinance);
}
/**
 * Copyright © 2015-2020 <a href="http://www.jeeplus.org/">JeePlus</a> All rights reserved.
 */
package com.jeeplus.modules.lawcase.web;

import java.util.Date;
import java.util.List;

import javax.annotation.Resource;

import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.jeeplus.common.json.AjaxJson;
import com.jeeplus.common.utils.IdGen;
import com.jeeplus.common.utils.StringUtils;
import com.jeeplus.core.web.BaseController;
import com.jeeplus.modules.lawcase.entity.StageRecord;
import com.jeeplus.modules.lawcase.entity.StageRecordFile;
import com.jeeplus.modules.lawcase.entity.TodoInfoFile;
import com.jeeplus.modules.lawcase.mapper.StageRecordFileMapper;
import com.jeeplus.modules.lawcase.service.StageRecordService;
import com.jeeplus.modules.lawcase.vo.StageTemplateSortVO;
import com.jeeplus.modules.sys.utils.FileKit;
import com.jeeplus.modules.sys.utils.UserUtils;

import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;

/**
 * 阶段记录Controller
 * <AUTHOR>
 * @version 2021-08-12
 */
@RestController
@RequestMapping(value = "/lawcase/stageRecord")
public class StageRecordController extends BaseController {

	@Autowired
	private StageRecordService stageRecordService;

	@Resource
	private StageRecordFileMapper stageRecordFileMapper;

	@ModelAttribute
	public StageRecord get(@RequestParam(required=false) String id) {
		StageRecord entity = null;
		if (StringUtils.isNotBlank(id)){
			entity = stageRecordService.get(id);
		}
		if (entity == null){
			entity = new StageRecord();
		}
		return entity;
	}


	/**
	 * 阶段记录树表数据
	 */
	@RequiresPermissions("lawcase:stageRecord:list")
	@GetMapping("list")
	public AjaxJson list(StageRecord stageRecord) {
		return AjaxJson.success().put("list", stageRecordService.findList(stageRecord));
	}

	/**
	 * 根据Id获取阶段记录数据
	 */
	@RequiresPermissions(value={"lawcase:stageRecord:view","lawcase:stageRecord:add","lawcase:stageRecord:edit"},logical=Logical.OR)
	@GetMapping("queryById")
	public AjaxJson queryById(StageRecord stageRecord) {
		return AjaxJson.success().put("stageRecord", stageRecord);
	}

	/**
	 * 保存阶段记录
	 */
	@RequiresPermissions(value={"lawcase:stageRecord:add","lawcase:stageRecord:edit"},logical=Logical.OR)
	@PostMapping("save")
	public AjaxJson save(StageRecord stageRecord, Model model) throws Exception{
		/**
		 * 后台hibernate-validation插件校验
		 */
		String errMsg = beanValidator(stageRecord);
		if (StringUtils.isNotBlank(errMsg)){
			return AjaxJson.error(errMsg);
		}
		//新增或编辑表单保存
		stageRecordService.save(stageRecord);//保存
		return AjaxJson.success("保存阶段记录成功");
	}

	/**
	 * 删除阶段记录
	 */
	@RequiresPermissions("lawcase:stageRecord:del")
	@DeleteMapping("delete")
	public AjaxJson delete(String ids) {
		String idArray[] =ids.split(",");
		for(String id : idArray){
			stageRecordService.delete(new StageRecord(id));
		}
		return AjaxJson.success("删除阶段记录成功");
	}

	/**
     * 获取JSON树形数据。
     * @param extId 排除的ID
     * @return
	*/
	@RequiresPermissions("user")
	@GetMapping("treeData")
	public AjaxJson treeData(@RequestParam(required = false) String extId) {
		List<StageRecord> list = stageRecordService.findList(new StageRecord());
		List rootTree =  stageRecordService.formatListToTree (new StageRecord ("0"),list, extId );
		return AjaxJson.success().put("treeData", rootTree);
	}

	@RequiresPermissions(value = "user")
	@ApiOperation(value = "模板-附件上传", consumes = "application/form-data")
	@ApiImplicitParams({
			@ApiImplicitParam(value = "模板记录id", name = "recordId", required = true),
			@ApiImplicitParam(value = "文件", name = "file", required = true)
	})
	@PostMapping("fileUpload")
	public AjaxJson fileUpload(String recordId, MultipartFile file) {
		if (StringUtils.isBlank(recordId) || file == null){
			return AjaxJson.error("上传失败");
		}
		StageRecord stageRecord = stageRecordService.get(recordId);
		if (stageRecord == null){
			return AjaxJson.error("上传失败");
		}
		try {
			AjaxJson j = FileKit.fileUpload(file, TodoInfoFile.TEMPLATE_FILE_PATH);
			if(!j.isSuccess()){
				return j;
			}
			StageRecordFile stageRecordFile = new StageRecordFile();
			stageRecordFile.setId(IdGen.uuid());
			stageRecordFile.setDelFlag("0");
			stageRecordFile.setPath(String.valueOf(j.get("url")));
			stageRecordFile.setName(String.valueOf(j.get("name")));
			stageRecordFile.setCreateBy(UserUtils.getUser().getName());
			stageRecordFile.setCreateDate(new Date());
			stageRecordFile.setUpdateBy(UserUtils.getUser().getName());
			stageRecordFile.setUpdateDate(new Date());
			stageRecordFile.setRecordId(stageRecord.getId());
			Integer tsort=stageRecordFileMapper.getMaxSortByRid(stageRecord.getId());
			if(tsort==null) {
				tsort=0;
			}
			stageRecordFile.setSort(tsort+1);
			stageRecordFileMapper.insertOrUpdate(stageRecordFile);
			
			
			stageRecord.setNums(stageRecord.getNums()+1);
			stageRecordService.update(stageRecord);
			return AjaxJson.success("上传成功");
		} catch (Exception e) {
			e.printStackTrace();
			return AjaxJson.error("上传失败");
		}
	}
	
	@RequiresPermissions(value = "user")
	@ApiOperation(value = "附件列表记录", consumes = "application/form-data")
	@ApiImplicitParams({
			@ApiImplicitParam(value = "id值", name = "id", required = true)
	})
	@PostMapping("fileList")
	public AjaxJson fileList(String id) {
		List<StageRecordFile>  todoFileList = 
				stageRecordFileMapper.selectListByRecordId(id);
		return AjaxJson.success().put("list", todoFileList);
	}
	
	@ApiOperation(value = "删除附件记录", consumes = "application/form-data")
	@PostMapping("deleteFile")
	public AjaxJson deleteFile(String id) {
		if(StringUtils.isBlank(id)){
			return AjaxJson.error("参数错误");
		}
		deleteFileById(id);

		
		
		return AjaxJson.success("删除成功");
	}
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public void deleteFileById(String id) {
		this.deleteFile(stageRecordFileMapper.get(id));
	}
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public void deleteFile(StageRecordFile todoInfoFile) {
		if(todoInfoFile != null && StringUtils.isNotBlank(todoInfoFile.getId())){
			StageRecordFile selInfo=stageRecordFileMapper.get(todoInfoFile.getId());
			stageRecordFileMapper.delete(todoInfoFile);
			// 删除文件
			FileKit.deleteFileByUrl(todoInfoFile.getPath());
			
			
			StageRecord stageRecord = stageRecordService.get(selInfo.getRecordId());
			Integer num=stageRecord.getNums()-1;
			if(num<0) {
				num=0;
			}
			stageRecord.setNums(num);
			stageRecordService.update(stageRecord);
		}
	}

	//修改模板排序
	@RequiresPermissions(value = "user")
	@RequestMapping("updateSort")
	public AjaxJson updateSort(@RequestBody StageTemplateSortVO vo) {
		stageRecordFileMapper.updateSort(vo.getStartId(),vo.getStartSort());
		stageRecordFileMapper.updateSort(vo.getEndId(),vo.getEndSort());
		return AjaxJson.success();
	}
}
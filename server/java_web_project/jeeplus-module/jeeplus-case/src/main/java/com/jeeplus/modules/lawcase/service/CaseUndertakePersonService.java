/**
 * Copyright © 2015-2020 <a href="http://www.jeeplus.org/">JeePlus</a> All rights reserved.
 */
package com.jeeplus.modules.lawcase.service;

import java.util.List;

import com.jeeplus.modules.lawcase.entity.CaseUndertakePerson;
import com.jeeplus.modules.lawcase.mapper.CaseUndertakePersonMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.jeeplus.core.persistence.Page;
import com.jeeplus.core.service.CrudService;

/**
 * 案件承办人员Service
 * <AUTHOR>
 * @version 2021-08-02
 */
@Service
@Transactional(readOnly = true)
public class CaseUndertakePersonService extends CrudService<CaseUndertakePersonMapper, CaseUndertakePerson> {

	public CaseUndertakePerson get(String id) {
		return super.get(id);
	}
	
	public List<CaseUndertakePerson> findList(CaseUndertakePerson caseUndertakePerson) {
		return super.findList(caseUndertakePerson);
	}
	
	public Page<CaseUndertakePerson> findPage(Page<CaseUndertakePerson> page, CaseUndertakePerson caseUndertakePerson) {
		return super.findPage(page, caseUndertakePerson);
	}
	
	@Transactional(readOnly = false)
	public void save(CaseUndertakePerson caseUndertakePerson) {
		super.save(caseUndertakePerson);
	}

	@Transactional(readOnly = false)
	public void delete(CaseUndertakePerson caseUndertakePerson) {
		super.delete(caseUndertakePerson);
	}

	/**
	 * 通过 案件id 删除案件承办人员信息
	 * @param lawCaseId
	 */
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public void deleteByCase(String lawCaseId){
		mapper.deleteByCase(lawCaseId);
	}
}
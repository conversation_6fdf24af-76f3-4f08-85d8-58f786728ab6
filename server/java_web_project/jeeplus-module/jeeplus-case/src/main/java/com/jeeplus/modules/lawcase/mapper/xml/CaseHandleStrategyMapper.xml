<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jeeplus.modules.lawcase.mapper.CaseHandleStrategyMapper">

	<sql id="caseHandleStrategyColumns">
		a.id AS "id",
		a.create_by AS "createBy.id",
		a.create_date AS "createDate",
		a.update_by AS "updateBy.id",
		a.update_date AS "updateDate",
		a.remarks AS "remarks",
		a.del_flag AS "delFlag",
		a.case_id AS "lawCase.id",
		a.question AS "question",
		a.solution AS "solution",
		a.result AS "result"
	</sql>

	<sql id="caseHandleStrategyJoins">

	</sql>


	<select id="get" resultType="CaseHandleStrategy" >
		SELECT
			<include refid="caseHandleStrategyColumns"/>
		FROM law_case_handle_strategy a
		<include refid="caseHandleStrategyJoins"/>
		WHERE a.id = #{id}
	</select>

	<select id="findList" resultType="CaseHandleStrategy" >
		SELECT
			<include refid="caseHandleStrategyColumns"/>
		FROM law_case_handle_strategy a
		<include refid="caseHandleStrategyJoins"/>
		<where>
			a.del_flag = #{DEL_FLAG_NORMAL}
			${dataScope}
			<if test="lawCase != null and lawCase.id != null and lawCase.id != ''">
				AND a.case_id = #{lawCase.id}
			</if>
		</where>
		<choose>
			<when test="page !=null and page.orderBy != null and page.orderBy != ''">
				ORDER BY ${page.orderBy}
			</when>
			<otherwise>
				ORDER BY a.update_date DESC
			</otherwise>
		</choose>
	</select>

	<select id="findAllList" resultType="CaseHandleStrategy" >
		SELECT
			<include refid="caseHandleStrategyColumns"/>
		FROM law_case_handle_strategy a
		<include refid="caseHandleStrategyJoins"/>
		<where>
			a.del_flag = #{DEL_FLAG_NORMAL}
			${dataScope}
		</where>
		<choose>
			<when test="page !=null and page.orderBy != null and page.orderBy != ''">
				ORDER BY ${page.orderBy}
			</when>
			<otherwise>
				ORDER BY a.update_date DESC
			</otherwise>
		</choose>
	</select>

	<insert id="insert">
		INSERT INTO law_case_handle_strategy(
			id,
			create_by,
			create_date,
			update_by,
			update_date,
			remarks,
			del_flag,
			case_id,
			question,
			solution,
			result
		) VALUES (
			#{id},
			#{createBy.id},
			#{createDate},
			#{updateBy.id},
			#{updateDate},
			#{remarks},
			#{delFlag},
			#{lawCase.id},
			#{question},
			#{solution},
			#{result}
		)
	</insert>

	<update id="update">
		UPDATE law_case_handle_strategy SET
			update_by = #{updateBy.id},
			update_date = #{updateDate},
			remarks = #{remarks},
			case_id = #{lawCase.id},
			question = #{question},
			solution = #{solution},
			result = #{result}
		WHERE id = #{id}
	</update>


	<!--物理删除-->
	<update id="delete">
		DELETE FROM law_case_handle_strategy
		WHERE id = #{id}
	</update>

	<!--逻辑删除-->
	<update id="deleteByLogic">
		UPDATE law_case_handle_strategy SET
			del_flag = #{DEL_FLAG_DELETE}
		WHERE id = #{id}
	</update>


	<!-- 根据实体名称和字段名称和字段值获取唯一记录 -->
	<select id="findUniqueByProperty" resultType="CaseHandleStrategy">
		select * FROM law_case_handle_strategy  where ${propertyName} = #{value}
	</select>

</mapper>
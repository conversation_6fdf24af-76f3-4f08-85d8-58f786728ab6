<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jeeplus.modules.lawcase.mapper.CasePropertyPreservationMapper">

	<sql id="casePropertyPreservationColumns">
		a.id AS "id",
		a.case_id AS "lawCase.id",
		a.applicant AS "applicant",
		a.respondent AS "respondent",
		a.property_type AS "propertyType",
		a.ruling_number AS "rulingNumber",
		a.seizure_date AS "seizureDate",
		a.seizure_expiration_date AS "seizureExpirationDate",
		a.continue_remind_date AS "continueRemindDate",
		a.remind_mode AS "remindMode",
		a.accept_unit AS "acceptUnit",
		a.undertake_person AS "undertakePerson",
		a.preservation_money AS "preservationMoney",
		a.execute_status AS "executeStatus",
		a.create_date AS "createDate",
		a.update_date AS "updateDate",
		a.remarks AS "remarks",
		a.del_flag AS "delFlag",

		ca.name AS "lawCase.name"
	</sql>

	<sql id="casePropertyPreservationJoins">
		LEFT JOIN law_case ca ON a.case_id = ca.id
	</sql>


	<select id="get" resultType="CasePropertyPreservation" >
		SELECT
			<include refid="casePropertyPreservationColumns"/>
		FROM law_case_property_preservation a
		<include refid="casePropertyPreservationJoins"/>
		WHERE a.id = #{id}
	</select>

	<select id="findList" resultType="CasePropertyPreservation" >
		SELECT
			<include refid="casePropertyPreservationColumns"/>
		FROM law_case_property_preservation a
		<include refid="casePropertyPreservationJoins"/>
		<where>
			a.del_flag = #{DEL_FLAG_NORMAL}
			${dataScope}
			<if test="lawCase != null and lawCase.id != null and lawCase.id != ''">
				AND a.case_id = #{lawCase.id}
			</if>
		</where>
		<choose>
			<when test="page !=null and page.orderBy != null and page.orderBy != ''">
				ORDER BY ${page.orderBy}
			</when>
			<otherwise>
				ORDER BY a.update_date DESC
			</otherwise>
		</choose>
	</select>

	<select id="findAllList" resultType="CasePropertyPreservation" >
		SELECT
			<include refid="casePropertyPreservationColumns"/>
		FROM law_case_property_preservation a
		<include refid="casePropertyPreservationJoins"/>
		<where>
			a.del_flag = #{DEL_FLAG_NORMAL}
			${dataScope}
		</where>
		<choose>
			<when test="page !=null and page.orderBy != null and page.orderBy != ''">
				ORDER BY ${page.orderBy}
			</when>
			<otherwise>
				ORDER BY a.update_date DESC
			</otherwise>
		</choose>
	</select>

	<insert id="insert">
		INSERT INTO law_case_property_preservation(
			id,
			case_id,
			applicant,
			respondent,
			property_type,
			ruling_number,
			seizure_date,
			seizure_expiration_date,
			continue_remind_date,
			remind_mode,
			accept_unit,
			undertake_person,
			preservation_money,
			execute_status,
			create_by,
			create_date,
			update_by,
			update_date,
			remarks,
			del_flag
		) VALUES (
			#{id},
			#{lawCase.id},
			#{applicant},
			#{respondent},
			#{propertyType},
			#{rulingNumber},
			#{seizureDate},
			#{seizureExpirationDate},
			#{continueRemindDate},
			#{remindMode},
			#{acceptUnit},
			#{undertakePerson},
			#{preservationMoney},
			#{executeStatus},
			#{createBy.id},
			#{createDate},
			#{updateBy.id},
			#{updateDate},
			#{remarks},
			#{delFlag}
		)
	</insert>

	<update id="update">
		UPDATE law_case_property_preservation SET
			case_id = #{lawCase.id},
			applicant = #{applicant},
			respondent = #{respondent},
			property_type = #{propertyType},
			ruling_number = #{rulingNumber},
			seizure_date = #{seizureDate},
			seizure_expiration_date = #{seizureExpirationDate},
			continue_remind_date = #{continueRemindDate},
			remind_mode = #{remindMode},
			accept_unit = #{acceptUnit},
			undertake_person = #{undertakePerson},
			preservation_money = #{preservationMoney},
			execute_status = #{executeStatus},
			update_by = #{updateBy.id},
			update_date = #{updateDate},
			remarks = #{remarks}
		WHERE id = #{id}
	</update>


	<!--物理删除-->
	<update id="delete">
		DELETE FROM law_case_property_preservation
		WHERE id = #{id}
	</update>

	<!--逻辑删除-->
	<update id="deleteByLogic">
		UPDATE law_case_property_preservation SET
			del_flag = #{DEL_FLAG_DELETE}
		WHERE id = #{id}
	</update>


	<!-- 根据实体名称和字段名称和字段值获取唯一记录 -->
	<select id="findUniqueByProperty" resultType="CasePropertyPreservation">
		select * FROM law_case_property_preservation  where ${propertyName} = #{value}
	</select>

</mapper>
/**
 * Copyright © 2015-2020 <a href="http://www.jeeplus.org/">JeePlus</a> All rights reserved.
 */
package com.jeeplus.modules.lawcase.entity;


import javax.validation.constraints.NotNull;

import com.jeeplus.common.utils.excel.annotation.ExcelField;
import com.jeeplus.core.persistence.DataEntity;

import lombok.Data;

/**
 * 阶段模版Entity
 * <AUTHOR>
 * @version 2021-08-02
 */
@Data
public class StageTemplate extends DataEntity<StageTemplate> {
	
	private static final long serialVersionUID = 1L;
	/* 类型  1、自定义模版   2、系统定义（公共模版）  */
	public static final String TYPE_CUSTOM = "1";
	public static final String TYPE_SYSTEM = "2";

	@NotNull
	@ExcelField(title="名称", align=2, sort=7)
	private String name;		// 名称
	private String type;		// 模版类型   公共、自定义
	private String isSystem;	// 是否系统模版


	// 虚拟
	private String stageNames;	// 模版阶段名称
	
	@NotNull(message = "类型不能为空")
	@ExcelField(title="类型", align=2, sort=4)
	private String caseModelType;	 
	
	private String caseModelName;	
	
	private Integer sort;
	

	public StageTemplate() {
		super();
	}
	
	public StageTemplate(String id){
		super(id);
	}
}
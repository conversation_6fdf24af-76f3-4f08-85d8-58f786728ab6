<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jeeplus.modules.lawcase.mapper.CaseMapper">

	<sql id="caseColumns">
		a.id AS "id",
		a.type AS "type",
		a.case_program_id AS "caseProgram.id",
		a.case_program_name AS "caseProgram.name",
		a.name AS "name",
		a.mtype_id AS "mtypeId",
		a.mtype_name AS "mtypeName",
		a.case_cause_id AS "caseCause.id",
		a.case_cause_name AS "caseCause.name",
		a.number AS "number",
		a.entrust_date AS "entrustDate",
		a.subject_matter AS "subjectMatter",
		a.host_user_id AS "hostUser.id",
		a.record_date AS "recordDate",
		a.trial_result AS "trialResult",
		a.ruling_date AS "rulingDate",
		a.settle_case_status AS "settleCaseStatus",
		a.settle_case_date AS "settleCaseDate",
		a.archive_user_id AS "archiveUser.id",
		a.archive_date AS "archiveDate",
		a.custody_place AS "custodyPlace",
		a.charge_mode AS "chargeMode",
		a.contract_money AS "contractMoney",
		a.charge_remarks AS "chargeRemarks",
		a.actual_back_money AS "actualBackMoney",
		a.win_money AS "winMoney",
		a.is_share AS "isShare",
		a.accept_unit_area AS "acceptUnitArea",
		a.accept_unit_type AS "acceptUnitType",
		a.accept_unit_name AS "acceptUnitName",
		a.status AS "status",
		a.is_audit AS "isAudit",
		a.audit_status AS "auditStatus",
		a.audit_reason AS "auditReason",
		a.create_by AS "createBy.id",
		a.create_date AS "createDate",
		a.update_by AS "updateBy.id",
		a.update_date AS "updateDate",
		a.remarks AS "remarks",
		a.del_flag AS "delFlag",

		hostUser.name AS "hostUser.name",
		archiveUser.name AS "archiveUser.name",
		program.parent_ids AS "caseProgram.parentIds",
		cc.parent_ids AS "caseCause.parentIds"
	</sql>

	<sql id="caseListColumns">
		a.id AS "id",
		a.type AS "type",
		a.case_program_id AS "caseProgram.id",
		a.case_program_name AS "caseProgram.name",
		a.name AS "name",
		a.case_cause_id AS "caseCause.id",
		a.case_cause_name AS "caseCause.name",
		a.number AS "number",
		a.mtype_id AS "mtypeId",
		a.mtype_name AS "mtypeName",
		a.entrust_date AS "entrustDate",
		a.subject_matter AS "subjectMatter",
		a.host_user_id AS "hostUser.id",
		a.settle_case_status AS "settleCaseStatus",
		a.archive_user_id AS "archiveUser.id",
		a.accept_unit_area AS "acceptUnitArea",
		a.accept_unit_type AS "acceptUnitType",
		a.accept_unit_name AS "acceptUnitName",
		a.status AS "status",
		a.audit_status AS "auditStatus",
		a.audit_reason AS "auditReason",
		a.remarks AS "remarks",

		hostUser.name AS "hostUser.name",
		archiveUser.name AS "archiveUser.name"
	</sql>

	<sql id="caseJoins">

		LEFT JOIN sys_user hostUser ON hostUser.id = a.host_user_id
		LEFT JOIN sys_user archiveUser ON archiveUser.id = a.archive_user_id
		LEFT JOIN law_case_program program ON a.case_program_id = program.id
		LEFT JOIN law_case_cause cc ON a.case_cause_id = cc.id
	</sql>


	<select id="get" resultType="Case" >
		SELECT
			<include refid="caseColumns"/>
		FROM law_case a
		<include refid="caseJoins"/>
		WHERE a.id = #{id}
	</select>

	<select id="getMaxNumber" resultType="String" >
		SELECT MAX(a.number)
		FROM(
			SELECT SUBSTRING(a.number, 3) AS "number"
			FROM law_case  a
			WHERE a.number IS NOT NULL AND a.number != '' AND LENGTH(a.number) > 10
		) a
	</select>

	<select id="findList" resultType="Case" >
		SELECT
			<include refid="caseColumns"/>
		FROM law_case a
		<include refid="caseJoins"/>
		<where>
			a.del_flag = #{DEL_FLAG_NORMAL}
			${dataScope}
			<if test="type != null and type != ''">
				AND a.type = #{type}
			</if>
			<if test="caseProgram != null and caseProgram.id != null and caseProgram.id != ''">
				AND a.case_program_id = #{caseProgram.id}
			</if>
			<if test="caseProgram != null and caseProgram.name != null and caseProgram.name != ''">
				AND a.case_program_name LIKE
				<if test="_databaseId == 'postgre'">'%'||#{caseProgram.name}||'%'</if>
				<if test="_databaseId == 'oracle'">'%'||#{caseProgram.name}||'%'</if>
				<if test="_databaseId == 'mssql'">'%'+#{caseProgram.name}+'%'</if>
				<if test="_databaseId == 'mysql'">concat('%',#{caseProgram.name},'%')</if>
			</if>
			<if test="name != null and name != ''">
				AND a.name LIKE
				    <if test="_databaseId == 'postgre'">'%'||#{name}||'%'</if>
					<if test="_databaseId == 'oracle'">'%'||#{name}||'%'</if>
					<if test="_databaseId == 'mssql'">'%'+#{name}+'%'</if>
					<if test="_databaseId == 'mysql'">concat('%',#{name},'%')</if>
			</if>
			<if test="caseCause != null and caseCause.name != null and caseCause.name != ''">
				AND a.case_cause_name LIKE concat('%',#{caseCause.name},'%')
			</if>
			<if test="number != null and number != ''">
				AND a.number LIKE concat('%',#{number},'%')
			</if>
			<if test="isShare != null and isShare != ''">
				AND a.is_share = #{isShare}
			</if>
			<if test="status != null and status != ''">
				AND a.status = #{status}
			</if>
			<if test="hostUser != null and hostUser.id != null and hostUser.id != ''">
				AND a.host_user_id = #{hostUser.id}
			</if>
		</where>
		<choose>
			<when test="page !=null and page.orderBy != null and page.orderBy != ''">
				ORDER BY ${page.orderBy}
			</when>
			<otherwise>
				ORDER BY a.update_date DESC
			</otherwise>
		</choose>
	</select>

	<select id="findOverallList" resultType="Case" >
		SELECT <include refid="caseColumns"/>
			,(SELECT GROUP_CONCAT(`name`) FROM law_case_concern_person WHERE case_id = a.id AND is_entrust = '1' GROUP BY case_id) AS "entrustPersonNames"
			,(SELECT GROUP_CONCAT(`name`) FROM law_case_concern_person WHERE case_id = a.id AND is_entrust = '0' GROUP BY case_id) AS "concernPersonNames"
			,(SELECT GROUP_CONCAT(`name`) FROM law_case_undertake_person WHERE case_id = a.id GROUP BY case_id) AS "undertakePersonNames"
			,(SELECT GROUP_CONCAT(`name`) FROM law_case_stage WHERE case_id = a.id AND is_current = '1' GROUP BY case_id) AS "stageName"
		FROM law_case a
		<include refid="caseJoins"/>
		<if test="queryConcernPersonName != null and queryConcernPersonName != ''">
			LEFT JOIN law_case_concern_person cp ON a.id = cp.case_id
		</if>
		<if test="queryStageName != null and queryStageName != ''">
			LEFT JOIN law_case_stage cs ON a.id = cs.case_id
		</if>
		<if test="checkWord != null and checkWord != '' and checkWord eq '2'.toString() ">
			INNER JOIN law_case_user cu ON a.id = cu.case_id
		</if>

		<where>
			a.del_flag = #{DEL_FLAG_NORMAL}
			${dataScope}
			<if test="type != null and type != ''">
				<if test="type neq '99'.toString()">
					AND a.type = #{type}
				</if>
				<if test="type eq '99'.toString()">
					AND !FIND_IN_SET(a.type, '1,3,4')
				</if>
			</if>
			<if test="status != null and status != ''">
				<if test="status neq '99'.toString()">
					AND a.status = #{status}
				</if>
				<if test="status eq '99'.toString()">
					AND a.status != '2'
				</if>
			</if>
			<if test="caseProgram != null and caseProgram.id != null and caseProgram.id != ''">
				AND a.case_program_id = #{caseProgram.id}
			</if>
			<if test="caseProgram != null and caseProgram.name != null and caseProgram.name != ''">
				AND a.case_program_name LIKE
				<if test="_databaseId == 'postgre'">'%'||#{caseProgram.name}||'%'</if>
				<if test="_databaseId == 'oracle'">'%'||#{caseProgram.name}||'%'</if>
				<if test="_databaseId == 'mssql'">'%'+#{caseProgram.name}+'%'</if>
				<if test="_databaseId == 'mysql'">concat('%',#{caseProgram.name},'%')</if>
			</if>
			<if test="name != null and name != ''">
				AND a.name LIKE
				<if test="_databaseId == 'postgre'">'%'||#{name}||'%'</if>
				<if test="_databaseId == 'oracle'">'%'||#{name}||'%'</if>
				<if test="_databaseId == 'mssql'">'%'+#{name}+'%'</if>
				<if test="_databaseId == 'mysql'">concat('%',#{name},'%')</if>
			</if>
			<if test="caseCause != null and caseCause.id != null and caseCause.id != ''">
				AND (a.case_cause_id = #{caseCause.id} OR CONCAT('%,',cc.parent_ids,',%') LIKE CONCAT('%,',#{caseCause.id},',%'))
			</if>
			<if test="acceptUnitName != null and acceptUnitName != ''">
				AND a.accept_unit_name LIKE concat('%',#{acceptUnitName},'%')
			</if>
			<if test="number != null and number != ''">
				AND a.number LIKE concat('%',#{number},'%')
			</if>
			<if test="isShare != null and isShare != ''">
				AND a.is_share = #{isShare}
			</if>
			<if test="auditStatus != null and auditStatus != ''">
				AND a.audit_status = #{auditStatus}
			</if>
			<if test="hostUser != null and hostUser.id != null and hostUser.id != ''">
				AND a.host_user_id = #{hostUser.id}
			</if>
			<if test="queryConcernPersonName != null and queryConcernPersonName != ''">
				AND cp.name LIKE concat('%',#{queryConcernPersonName},'%')
			</if>
			<if test="queryStageName != null and queryStageName != ''">
				AND cs.name LIKE concat('%',#{queryStageName},'%')
			</if>
			<if test="checkWord != null and checkWord != '' and checkWord eq '2'.toString() ">
				AND cu.user_id = #{currentUser.id}
			</if>
			<if test="queryStartEntrustDate != null">
				AND  DATE(a.entrust_date) <![CDATA[  >=  ]]>  DATE(#{queryStartEntrustDate})
			</if>
			<if test="queryEndEntrustDate != null">
				AND DATE(a.entrust_date)  <![CDATA[  <=  ]]>  DATE(#{queryEndEntrustDate})
			</if>
			<if test="queryStartSettleCaseDate != null">
				AND  DATE(a.settle_case_date) <![CDATA[  >=  ]]>  DATE(#{queryStartSettleCaseDate})
			</if>
			<if test="queryEndSettleCaseDate != null">
				AND DATE(a.settle_case_date)  <![CDATA[  <=  ]]>  DATE(#{queryEndSettleCaseDate})
			</if>
			<if test="queryYear != null and queryYear != ''">
				AND DATE_FORMAT(a.entrust_date, '%Y') = #{queryYear}
			</if>
		</where>
		<choose>
			<when test="page !=null and page.orderBy != null and page.orderBy != ''">
				ORDER BY ${page.orderBy}
			</when>
			<otherwise>
				ORDER BY a.update_date DESC
			</otherwise>
		</choose>
	</select>

	<select id="findAllList" resultType="Case" >
		SELECT
			<include refid="caseColumns"/>
		FROM law_case a
		<include refid="caseJoins"/>
		<where>
			a.del_flag = #{DEL_FLAG_NORMAL}
			${dataScope}
		</where>
		<choose>
			<when test="page !=null and page.orderBy != null and page.orderBy != ''">
				ORDER BY ${page.orderBy}
			</when>
			<otherwise>
				ORDER BY a.update_date DESC
			</otherwise>
		</choose>
	</select>

	<insert id="insert">
		INSERT INTO law_case(
			id,
			`type`,
			case_program_id,
			case_program_name,
			`name`,
			case_cause_id,
			case_cause_name,
			`number`,
			entrust_date,
			subject_matter,
			host_user_id,
			record_date,
			trial_result,
			ruling_date,
			settle_case_status,
			settle_case_date,
			archive_user_id,
			archive_date,
			custody_place,
			charge_mode,
			contract_money,
			charge_remarks,
			actual_back_money,
			win_money,
			is_share,
			accept_unit_area,
			accept_unit_type,
			accept_unit_name,
			status,
			is_audit,
			audit_status,
			audit_reason,
			create_by,
			create_date,
			update_by,
			update_date,
			remarks,
			mtype_id,
			mtype_name,
			del_flag
		) VALUES (
			#{id},
			#{type},
			#{caseProgram.id},
			#{caseProgram.name},
			#{name},
			#{caseCause.id},
			#{caseCause.name},
			#{number},
			#{entrustDate},
			#{subjectMatter},
			#{hostUser.id},
			#{recordDate},
			#{trialResult},
			#{rulingDate},
			#{settleCaseStatus},
			#{settleCaseDate},
			#{archiveUser.id},
			#{archiveDate},
			#{custodyPlace},
			#{chargeMode},
			#{contractMoney},
			#{chargeRemarks},
			#{actualBackMoney},
			#{winMoney},
			#{isShare},
			#{acceptUnitArea},
			#{acceptUnitType},
			#{acceptUnitName},
			#{status},
			#{isAudit},
			#{auditStatus},
			#{auditReason},
			#{createBy.id},
			#{createDate},
			#{updateBy.id},
			#{updateDate},
			#{remarks},
			#{mtypeId},
			#{mtypeName},
			#{delFlag}
		)
	</insert>

	<update id="update">
		UPDATE law_case SET
			`type` = #{type},
			case_program_id = #{caseProgram.id},
			case_program_name = #{caseProgram.name},
			`name` = #{name},
			`mtype_id` = #{mtypeId},
			`mtype_name` = #{mtypeName},
			case_cause_id = #{caseCause.id},
			case_cause_name = #{caseCause.name},
			`number` = #{number},
			entrust_date = #{entrustDate},
			subject_matter = #{subjectMatter},
			host_user_id = #{hostUser.id},
			record_date = #{recordDate},
			trial_result = #{trialResult},
			ruling_date = #{rulingDate},
			settle_case_status = #{settleCaseStatus},
			settle_case_date = #{settleCaseDate},
			archive_user_id = #{archiveUser.id},
			archive_date = #{archiveDate},
			custody_place = #{custodyPlace},
			charge_mode = #{chargeMode},
			contract_money = #{contractMoney},
			charge_remarks = #{chargeRemarks},
			actual_back_money = #{actualBackMoney},
			win_money = #{winMoney},
			is_share = #{isShare},
			accept_unit_area = #{acceptUnitArea},
			accept_unit_type = #{acceptUnitType},
			accept_unit_name = #{acceptUnitName},
			 <if test="status != null and status != ''">
			  status = #{status},
			</if>
			is_audit = #{isAudit},
			audit_status = #{auditStatus},
			update_by = #{updateBy.id},
			update_date = #{updateDate},
			remarks = #{remarks}
		WHERE id = #{id}
	</update>

	<update id="updateSettle">
		UPDATE law_case SET
			settle_case_status = #{settleCaseStatus},
			settle_case_date = #{settleCaseDate},
			trial_result = #{trialResult},
			actual_back_money = #{actualBackMoney},
		   <if test="status != null and status != ''">
			  status = #{status},
			</if>
			remarks = #{remarks}
		WHERE id = #{id}
	</update>

	<update id="updateAccept">
		UPDATE law_case SET
			accept_unit_area = #{acceptUnitArea},
			accept_unit_type = #{acceptUnitType},
			accept_unit_name = #{acceptUnitName}
		WHERE id = #{id}
	</update>

	<update id="updateAcceptAndResult">
		UPDATE law_case SET
			accept_unit_area = #{acceptUnitArea},
			accept_unit_type = #{acceptUnitType},
			accept_unit_name = #{acceptUnitName},
			record_date = #{recordDate},
			trial_result = #{trialResult},
			ruling_date = #{rulingDate}
		WHERE id = #{id}
	</update>

	<update id="updateArchive">
		UPDATE law_case SET
			archive_user_id = #{archiveUser.id},
			archive_date = #{archiveDate},
			custody_place = #{custodyPlace},
			 <if test="status != null and status != ''">
			  status = #{status},
			</if>
			remarks = #{remarks}
		WHERE id = #{id}
	</update>

	<update id="updateStatus">
		UPDATE law_case SET    
		  <if test="status != null and status != ''">
			  status = #{status},
			</if> 
			update_date=now() 
			 WHERE id = #{id}
	</update>

	<update id="updateAuditStatus">
		UPDATE law_case SET audit_status = #{auditStatus}
			,audit_reason = #{auditReason}
		<if test="isAudit != null and isAudit != ''">
			,is_audit = #{isAudit}
		</if>
		WHERE id = #{id}
	</update>
	
		<update id="updateMtypeInfo">
		UPDATE law_case SET mtype_id = #{mtypeId}
			,mtype_name = #{mtypeName}
		WHERE id = #{id}
	</update>
	

	<update id="updateIsShare">
		UPDATE law_case SET is_share = #{isShare} WHERE id = #{id}
	</update>

	<!--物理删除-->
	<update id="delete">
		DELETE FROM law_case
		WHERE id = #{id}
	</update>

	<update id="deleteAllRelation">
		{call deleteCaseRelation(#{caseId,mode=IN})}
	</update>

	<!--逻辑删除-->
	<update id="deleteByLogic">
		UPDATE law_case SET
			del_flag = #{DEL_FLAG_DELETE}
		WHERE id = #{id}
	</update>


	<!-- 根据实体名称和字段名称和字段值获取唯一记录 -->
	<select id="findUniqueByProperty" resultType="Case">
		select * FROM law_case  where ${propertyName} = #{value}
	</select>

</mapper>
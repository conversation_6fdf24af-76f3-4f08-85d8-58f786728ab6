/**
 * Copyright © 2015-2020 <a href="http://www.jeeplus.org/">JeePlus</a> All rights reserved.
 */
package com.jeeplus.modules.lawcase.service;

import java.util.List;

import com.jeeplus.modules.lawcase.entity.CustomerContacts;
import com.jeeplus.modules.lawcase.mapper.CustomerContactsMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.jeeplus.core.persistence.Page;
import com.jeeplus.core.service.CrudService;
import com.jeeplus.database.datasource.annotation.DS;

/**
 * 客户联系人Service
 * <AUTHOR>
 * @version 2021-08-02
 */
@DS("lawCase")
@Service
@Transactional(readOnly = true)
public class CustomerContactsService extends CrudService<CustomerContactsMapper, CustomerContacts> {

	public CustomerContacts get(String id) {
		return super.get(id);
	}
	
	public List<CustomerContacts> findList(CustomerContacts customerContacts) {
		return super.findList(customerContacts);
	}
	
	public Page<CustomerContacts> findPage(Page<CustomerContacts> page, CustomerContacts customerContacts) {
		return super.findPage(page, customerContacts);
	}
	
	@Transactional(readOnly = false)
	public void save(CustomerContacts customerContacts) {
		super.save(customerContacts);
	}

	@Transactional(readOnly = false)
	public void delete(CustomerContacts customerContacts) {
		super.delete(customerContacts);
	}

	/**
	 * 通过客户id  删除联系人信息
	 * @param customerId
	 */
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public void deleteByCustomer(String customerId) {
		mapper.deleteByCustomer(customerId);
	}

}
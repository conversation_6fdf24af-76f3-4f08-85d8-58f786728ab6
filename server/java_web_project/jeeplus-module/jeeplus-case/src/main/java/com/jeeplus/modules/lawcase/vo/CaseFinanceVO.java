/**
 * Copyright © 2015-2020 <a href="http://www.jeeplus.org/">JeePlus</a> All rights reserved.
 */
package com.jeeplus.modules.lawcase.vo;

import com.jeeplus.core.persistence.DataEntity;
import com.jeeplus.modules.lawcase.entity.*;
import lombok.Data;

import java.util.List;

/**
 * 案件财务统计信息 VO
 * <AUTHOR>
 * @version 2021-10-29
 */
@Data
public class CaseFinanceVO extends DataEntity<CaseFinanceVO> {

	private static final long serialVersionUID = 1L;

	private Case lawCase;	// 案件信息

	private Double receiveMoney;		// 已收费用
	private Double expenditureMoney;	// 支出费用

	public CaseFinanceVO() {
		super();
	}

	public CaseFinanceVO(Case lawCase) {
		this.lawCase = lawCase;
	}

	public CaseFinanceVO(String id){
		super(id);
	}
}
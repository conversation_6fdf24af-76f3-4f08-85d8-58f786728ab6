/**
 * Copyright © 2015-2020 <a href="http://www.jeeplus.org/">JeePlus</a> All rights reserved.
 */
package com.jeeplus.modules.lawcase.mapper;

import com.jeeplus.core.persistence.BaseMapper;
import com.jeeplus.modules.lawcase.entity.Case;
import com.jeeplus.modules.lawcase.entity.CaseCaseRelation;
import com.jeeplus.modules.lawcase.entity.CaseCustomerRelation;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 * 案件-客户关联 MAPPER接口
 * <AUTHOR>
 * @version 2021-08-02
 */
@Mapper
@Repository
public interface CaseCustomerRelationMapper extends BaseMapper<CaseCustomerRelation> {
    /**
     * 根据客户id、关联案件id 查询数量
     * @param customerRelation
     * @return
     */
    int getCount(CaseCustomerRelation customerRelation);

    /**
     * 获取案件信息  可根据客户id 排除已关联的 案件
     * @param map
     * @return
     */
    List<Case> findNotRelationCaseList(Map<String, String> map);

    /**
     * 根据案件id 删除案件-客户关联信息
     * @param lawCaseId
     * @return
     */
    int deleteByCase(String lawCaseId);
}
package com.jeeplus.modules.lawcase.vo;

import com.google.common.collect.Lists;
import lombok.Data;

import java.io.Serializable;
import java.util.List;



/**
 * 树结构 VO 抽象父类
 * <AUTHOR>
 * @date 2021-08-12
 */
@Data
public abstract class BaseTreeVO<T> implements Serializable {

    private static final long serialVersionUID = 5L;

    protected String id;        // id 值
    protected String parentId;  // 父级id
    protected String name;      // 名称
    protected Integer sort;     // 排序
    protected List<T> children = Lists.newArrayList();    // 子级信息

    public BaseTreeVO(){}

}

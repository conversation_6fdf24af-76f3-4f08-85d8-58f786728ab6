/**
 * Copyright © 2015-2020 <a href="http://www.jeeplus.org/">JeePlus</a> All rights reserved.
 */
package com.jeeplus.modules.lawcase.web;

import java.util.List;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.ConstraintViolationException;

import com.jeeplus.modules.lawcase.entity.CaseExecuteSituation;
import com.jeeplus.modules.lawcase.service.CaseExecuteSituationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import com.google.common.collect.Lists;
import com.jeeplus.common.utils.DateUtils;
import com.jeeplus.common.json.AjaxJson;
import com.jeeplus.core.persistence.Page;
import com.jeeplus.core.web.BaseController;
import com.jeeplus.common.utils.StringUtils;
import com.jeeplus.common.utils.excel.ExportExcel;
import com.jeeplus.common.utils.excel.ImportExcel;

/**
 * 执行情况Controller
 * <AUTHOR>
 * @version 2021-08-02
 */
@RestController
@Api(tags = "案件-执行情况")
@RequestMapping(value = "/case/caseExecuteSituation")
public class CaseExecuteSituationController extends BaseController {

	@Autowired
	private CaseExecuteSituationService caseExecuteSituationService;

	@ModelAttribute
	public CaseExecuteSituation get(@RequestParam(required=false) String id) {
		CaseExecuteSituation entity = null;
		if (StringUtils.isNotBlank(id)){
			entity = caseExecuteSituationService.get(id);
		}
		if (entity == null){
			entity = new CaseExecuteSituation();
		}
		return entity;
	}

	/**
	 * 执行情况列表数据
	 */
//	@RequiresPermissions("case:caseExecuteSituation:list")
	@ApiOperation(value = "执行情况数据", consumes = "application/form-data")
	@ApiImplicitParam(value = "案件id值", name = "lawCase.id", required = true)
	@PostMapping("list")
	public AjaxJson list(CaseExecuteSituation caseExecuteSituation, HttpServletRequest request, HttpServletResponse response) {
		Page<CaseExecuteSituation> page = new Page<CaseExecuteSituation>(request, response, -1);
		if(caseExecuteSituation.getLawCase() != null && StringUtils.isNotBlank(caseExecuteSituation.getLawCase().getId())){
			page = caseExecuteSituationService.findPage(page, caseExecuteSituation);
		}
		return AjaxJson.success().put("page",page);
	}

	/**
	 * 根据Id获取执行情况数据
	 */
//	@RequiresPermissions(value={"case:caseExecuteSituation:view","case:caseExecuteSituation:add","case:caseExecuteSituation:edit"},logical=Logical.OR)
	@ApiOperation(value = "详情", consumes = "application/form-data")
	@ApiImplicitParam(value = "id值", name = "id", required = true)
	@PostMapping("queryById")
	public AjaxJson queryById(CaseExecuteSituation caseExecuteSituation) {
		return AjaxJson.success().put("caseExecuteSituation", caseExecuteSituation);
	}

	/**
	 * 保存执行情况
	 */
	@RequiresPermissions(value={"case:caseExecuteSituation:add","case:caseExecuteSituation:edit", "user"},logical=Logical.OR)
	@ApiOperation("保存")
	@ApiImplicitParams({
			@ApiImplicitParam(value = "id值", name = "id", required = false),
			@ApiImplicitParam(value = "案件id值", name = "lawCase.id", required = false),
			@ApiImplicitParam(value = "申请执行日", name = "applyDate", required = false),
			@ApiImplicitParam(value = "受理单位", name = "acceptUnit", required = false),
			@ApiImplicitParam(value = "执行案号", name = "number", required = false),
			@ApiImplicitParam(value = "执行状态", name = "status", required = true),
			@ApiImplicitParam(value = "执行措施", name = "measures", required = true),
			@ApiImplicitParam(value = "执行请求", name = "executeRequest", required = false),
			@ApiImplicitParam(value = "履行情况", name = "performance", required = false)
	})
	@PostMapping("save")
	public AjaxJson save(@RequestBody CaseExecuteSituation caseExecuteSituation) throws Exception{
		/**
		 * 后台hibernate-validation插件校验
		 */
		String errMsg = beanValidator(caseExecuteSituation);
		if (StringUtils.isNotBlank(errMsg)){
			return AjaxJson.error(errMsg);
		}
		if(caseExecuteSituation.getLawCase() == null || StringUtils.isBlank(caseExecuteSituation.getLawCase().getId())){
			return AjaxJson.error("案件信息有误");
		}
		//新增或编辑表单保存
		try {
			caseExecuteSituationService.save(caseExecuteSituation);
			return AjaxJson.success("保存成功");
		} catch (Exception e) {
			e.printStackTrace();
			return AjaxJson.error("保存失败");
		}
	}


	/**
	 * 批量删除执行情况
	 */
	@RequiresPermissions(value = {"case:caseExecuteSituation:del", "user"}, logical = Logical.OR)
	@ApiOperation(value = "删除", consumes = "application/form-data")
	@ApiImplicitParam(value = "id值拼接以,拼接字符串", name = "ids", required = true)
	@PostMapping("delete")
	public AjaxJson delete(String ids) {
		String idArray[] =ids.split(",");
		for(String id : idArray){
			caseExecuteSituationService.delete(new CaseExecuteSituation(id));
		}
		return AjaxJson.success("删除成功");
	}

	/**
	 * 导出excel文件
	 */
//	@RequiresPermissions("case:caseExecuteSituation:export")
    @GetMapping("export")
    public AjaxJson exportFile(CaseExecuteSituation caseExecuteSituation, HttpServletRequest request, HttpServletResponse response) {
		try {
            String fileName = "执行情况"+DateUtils.getDate("yyyyMMddHHmmss")+".xlsx";
            Page<CaseExecuteSituation> page = caseExecuteSituationService.findPage(new Page<CaseExecuteSituation>(request, response, -1), caseExecuteSituation);
    		new ExportExcel("执行情况", CaseExecuteSituation.class).setDataList(page.getList()).write(response, fileName).dispose();
    		return null;
		} catch (Exception e) {
			return AjaxJson.error("导出执行情况记录失败！失败信息："+e.getMessage());
		}
    }

	/**
	 * 导入Excel数据

	 */
	@RequiresPermissions("case:caseExecuteSituation:import")
    @PostMapping("import")
   	public AjaxJson importFile(@RequestParam("file")MultipartFile file, HttpServletResponse response, HttpServletRequest request) {
		try {
			int successNum = 0;
			int failureNum = 0;
			StringBuilder failureMsg = new StringBuilder();
			ImportExcel ei = new ImportExcel(file, 1, 0);
			List<CaseExecuteSituation> list = ei.getDataList(CaseExecuteSituation.class);
			for (CaseExecuteSituation caseExecuteSituation : list){
				try{
					caseExecuteSituationService.save(caseExecuteSituation);
					successNum++;
				}catch(ConstraintViolationException ex){
					failureNum++;
				}catch (Exception ex) {
					failureNum++;
				}
			}
			if (failureNum>0){
				failureMsg.insert(0, "，失败 "+failureNum+" 条执行情况记录。");
			}
			return AjaxJson.success( "已成功导入 "+successNum+" 条执行情况记录"+failureMsg);
		} catch (Exception e) {
			return AjaxJson.error("导入执行情况失败！失败信息："+e.getMessage());
		}
    }

	/**
	 * 下载导入执行情况数据模板
	 */
	@RequiresPermissions("case:caseExecuteSituation:import")
    @GetMapping("import/template")
     public AjaxJson importFileTemplate(HttpServletResponse response) {
		try {
            String fileName = "执行情况数据导入模板.xlsx";
    		List<CaseExecuteSituation> list = Lists.newArrayList();
    		new ExportExcel("执行情况数据", CaseExecuteSituation.class, 1).setDataList(list).write(response, fileName).dispose();
    		return null;
		} catch (Exception e) {
			return AjaxJson.error( "导入模板下载失败！失败信息："+e.getMessage());
		}
    }


}
/**
 * Copyright © 2015-2020 <a href="http://www.jeeplus.org/">JeePlus</a> All rights reserved.
 */
package com.jeeplus.modules.lawcase.service;

import com.google.common.collect.Maps;
import com.jeeplus.common.utils.StringUtils;
import com.jeeplus.config.properties.JeePlusProperites;
import com.jeeplus.core.persistence.Page;
import com.jeeplus.core.service.CrudService;
import com.jeeplus.modules.lawcase.entity.Case;
import com.jeeplus.modules.lawcase.entity.CaseCaseRelation;
import com.jeeplus.modules.lawcase.entity.CaseUser;
import com.jeeplus.modules.lawcase.mapper.CaseCaseRelationMapper;
import com.jeeplus.modules.lawcase.mapper.CaseUserMapper;
import com.jeeplus.modules.sys.entity.User;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

/**
 * 案件与案件关联 Service
 * <AUTHOR>
 * @version 2021-08-02
 */
@Service
@Transactional(readOnly = true)
public class CaseCaseRelationService extends CrudService<CaseCaseRelationMapper, CaseCaseRelation> {


	public CaseCaseRelation get(String id) {
		return super.get(id);
	}
	
	public List<CaseCaseRelation> findList(CaseCaseRelation caseRelation) {
		return super.findList(caseRelation);
	}
	
	public Page<CaseCaseRelation> findPage(Page<CaseCaseRelation> page, CaseCaseRelation caseRelation) {
		return super.findPage(page, caseRelation);
	}

	/**
	 * 获取用户信息
	 * @param caseId
	 * @param name
	 * @return
	 */
	public List<Case> findRelationCaseList(String caseId, String name){
		Map<String, String> map = Maps.newHashMap();
		map.put("caseId", caseId);
		if (StringUtils.isNotBlank(name)) {
			map.put("name", name);
		}
		return mapper.findRelationCaseList(map);
	}

	@Transactional(readOnly = false)
	public void save(CaseCaseRelation caseRelation) {
		super.save(caseRelation);
	}

	@Transactional(readOnly = false)
	public void delete(CaseCaseRelation caseRelation) {
		super.delete(caseRelation);
	}

	/**
	 * 根据案件删除案件与案件关联
	 * @param caseId
	 */
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public void deleteByCase(String caseId) {
		mapper.deleteByCase(caseId);
	}
}
package com.jeeplus.modules.lawcase.mapper;

import com.jeeplus.core.persistence.BaseMapper;
import com.jeeplus.modules.lawcase.entity.StageRecordFile;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface StageRecordFileMapper{
    int updateBatch(List<StageRecordFile> list);

    int updateBatchSelective(List<StageRecordFile> list);

    int batchInsert(@Param("list") List<StageRecordFile> list);

    int insertOrUpdate(StageRecordFile record);

    int insertOrUpdateSelective(StageRecordFile record);

    List<StageRecordFile> selectListByRecordId(String id);

	void delete(StageRecordFile todoInfoFile);

	StageRecordFile get(String id);

	Integer getMaxSortByRid(String id);

	void updateSort(@Param("id") String id, @Param("sort")Integer sort);

}
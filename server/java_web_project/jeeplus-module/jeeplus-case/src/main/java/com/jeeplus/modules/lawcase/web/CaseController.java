/**
 * Copyright © 2015-2020 <a href="http://www.jeeplus.org/">JeePlus</a> All rights reserved.
 */
package com.jeeplus.modules.lawcase.web;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.jeeplus.common.json.AjaxJson;
import com.jeeplus.common.utils.DateUtils;
import com.jeeplus.common.utils.StringUtils;
import com.jeeplus.common.utils.excel.ExportExcel;
import com.jeeplus.common.utils.excel.ImportExcel;
import com.jeeplus.config.properties.JeePlusProperites;
import com.jeeplus.core.persistence.Page;
import com.jeeplus.core.web.BaseController;
import com.jeeplus.modules.lawcase.constant.CaseConstant;
import com.jeeplus.modules.lawcase.entity.*;
import com.jeeplus.modules.lawcase.service.*;
import com.jeeplus.modules.lawcase.service.common.LawCommonService;
import com.jeeplus.modules.lawcase.util.PackToZipUtils;
import com.jeeplus.modules.lawcase.vo.CaseVO;
import com.jeeplus.modules.lawcase.vo.StageVO;
import com.jeeplus.modules.sys.entity.User;
import com.jeeplus.modules.sys.service.UserService;
import com.jeeplus.modules.sys.utils.GeTuiPushUtils;
import com.jeeplus.modules.sys.utils.GeTuiPushUtils.PushParam;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.ConstraintViolationException;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 案件信息Controller
 * <AUTHOR>
 * @version 2021-08-02
 */
@RestController
@Api(tags = "案件管理")
@RequestMapping(value = "/lawcase/case")
public class CaseController extends BaseController {

	@Autowired
	private CaseService caseService;
	@Autowired
	private TodoInfoService todoInfoService;
	@Autowired
	private CaseConcernPersonService caseConcernPersonService;
	@Autowired
	private CaseUndertakePersonService caseUndertakePersonService;
	@Autowired
	private CaseCaseRelationService caseRelationService;
	@Autowired
	private CaseCustomerRelationService customerRelationService;
	@Autowired
	private LawCommonService lawCommonService;
	@Autowired
	private CaseFileDirectoryService caseFileDirectoryService;
	@Autowired
	private CaseStageService caseStageService;
	@Autowired
	private StageTemplateService stageTemplateService;
	@Autowired
	private StageService stageService;
	@Autowired
	private UserService userService;


	@ModelAttribute
	public Case get(@RequestParam(required=false) String id) {
		Case entity = null;
		if (StringUtils.isNotBlank(id)){
			entity = caseService.get(id);
		}
		if (entity == null){
			entity = new Case();
		}
		return entity;
	}

	/**
	 * 案件信息列表数据
	 */
//	@RequiresPermissions("lawcase:case:list")
	@ApiOperation(value = "案件列表", consumes = "application/form-data")
	@ApiImplicitParams({
			@ApiImplicitParam(value = "当前页码", name = "pageNo", required = true),
			@ApiImplicitParam(value = "每页数量", name = "pageSize", required = true),
			@ApiImplicitParam(value = "案件状态 字典：case_status", name = "status"),
			@ApiImplicitParam(value = "检测字段  1、共享他人  2、他人共享 ", name = "checkWord"),
			@ApiImplicitParam(value = "查询当事人名称", name = "queryConcernPersonName"),
			@ApiImplicitParam(value = "案件类型 字典：case_type", name = "type"),
			@ApiImplicitParam(value = "案件名称", name = "name"),
			@ApiImplicitParam(value = "案号", name = "number"),
			@ApiImplicitParam(value = "受理单位", name = "acceptUnitName"),
			@ApiImplicitParam(value = "查询阶段名称", name = "queryStageName"),
			@ApiImplicitParam(value = "案由id", name = "caseCause.id"),
			@ApiImplicitParam(value = "案件程序", name = "caseProgram.name"),
			@ApiImplicitParam(value = "案件审核状态", name = "auditStatus"),
			@ApiImplicitParam(value = "主办人", name = "hostUser.id"),
			@ApiImplicitParam(value = "查询年份", name = "queryYear"),
			@ApiImplicitParam(value = "查询委托开始日期", name = "queryStartEntrustDate"),
			@ApiImplicitParam(value = "查询委托结束日期", name = "queryEndEntrustDate"),
			@ApiImplicitParam(value = "查询结案开始日期", name = "queryStartSettleCaseDate"),
			@ApiImplicitParam(value = "查询结案结束日期", name = "queryEndSettleCaseDate")
	})
	@PostMapping("list")
	public AjaxJson list(Case lawCase, HttpServletRequest request, HttpServletResponse response) {
		Page<Case> page = new Page<Case>(request, response);
		return AjaxJson.success().put("page", lawCommonService.findCaseVoPage(page, lawCase));
	}

	/**
	 * 案件信息列表数据
	 */
//	@RequiresPermissions("lawcase:case:list")
	@ApiOperation("所有审核已通过案件信息，无分页")
	@PostMapping("allData")
	public AjaxJson allData(Case lawCase, HttpServletRequest request, HttpServletResponse response) {
		lawCase = CaseConstant.dataFilterVerify(lawCase, (obj, user)->{
			obj.setHostUser(user);
			return obj;
		});
		List<Case> list = caseService.findList(lawCase);
		return AjaxJson.success().put("data", list);
	}

	/**
	 * 根据Id获取案件基本信息数据
	 */
//	@RequiresPermissions(value={"lawcase:case:view","lawcase:case:add","lawcase:case:edit"},logical=Logical.OR)
	@ApiOperation(value = "案件基本信息", consumes = "application/form-data")
	@ApiImplicitParam(value = "案件id值", name = "id", required = true)
	@PostMapping("queryById")
	public AjaxJson queryById(Case lawCase) {
		return AjaxJson.success().put("case", lawCase);
	}

	/**
	 * 案件详情信息 包含：案件信息、当事人信息、承办人信息、关联案件信息、关联客户信息
	 * @param lawCase
	 * @return
	 */
//	@RequiresPermissions(value={"lawcase:case:view","lawcase:case:add","lawcase:case:edit"},logical=Logical.OR)
	@ApiOperation(value = "案件所有信息", notes="包含：案件信息、当事人信息、承办人信息、关联案件信息、关联客户信息", consumes = "application/form-data")
	@ApiImplicitParam(value = "案件id值", name = "id", required = true)
	@PostMapping("detailInfo")
	public AjaxJson detailInfo(Case lawCase) {
		CaseVO caseVo = new CaseVO(lawCase);

		if(StringUtils.isNotBlank(lawCase.getId())){
			/* 案件当事人信息 */
			caseVo.setConcernPersonList( caseConcernPersonService.findList(new CaseConcernPerson(lawCase)) );
			/* 案件承办人信息 */
			caseVo.setUndertakePersonList( caseUndertakePersonService.findList(new CaseUndertakePerson(lawCase)) );
			/* 案件-案件关联信息 */
			caseVo.setCaseRelationsList( caseRelationService.findList(new CaseCaseRelation(lawCase)) );
			/* 案件-客户关联信息 */
			caseVo.setCustomerRelationList( customerRelationService.findList( new CaseCustomerRelation(lawCase) ) );
		}
		return AjaxJson.success().put("data", caseVo);
	}


	/**
	 * 保存案件信息
	 */
	@RequiresPermissions(value={"lawcase:case:add","lawcase:case:edit", "user"},logical=Logical.OR)
	@ApiOperation("修改保存")
	@PostMapping("save")
	public AjaxJson save(@RequestBody Case lawCase) throws Exception{
		/**
		 * 后台hibernate-validation插件校验
		 */
		String errMsg = beanValidator(lawCase);
		if (StringUtils.isNotBlank(errMsg)){
			return AjaxJson.error(errMsg);
		}
		if(lawCase.getCaseProgram() == null || StringUtils.isBlank(lawCase.getCaseProgram().getId())){
			return AjaxJson.error("案件程序不能为空");
		}
		if(lawCase.getHostUser() == null || StringUtils.isBlank(lawCase.getHostUser().getId())){
			return AjaxJson.error("主办人员信息不能为空");
		}
		
		if(StringUtils.isNotBlank(lawCase.getMtypeId())){
			Case caseInfo=caseService.get(lawCase.getId());
			if(StringUtils.isBlank(caseInfo.getMtypeId()) ||(!caseInfo.getMtypeId().equals(lawCase.getMtypeId()))) {
				StageTemplate  stageTemplate =stageTemplateService.get(lawCase.getMtypeId());
			    if(stageTemplate==null) {
			    	return AjaxJson.error("请选择正确的模板");
			    }
			    lawCase.setMtypeName(stageTemplate.getCaseModelName()+"/"+stageTemplate.getName());
			    Stage stage = new Stage();
				stage.setStageTemplate(stageTemplate);
				// 获取阶段、阶段待办事项信息
				List<StageVO> stageList = stageService.findListByTemplate(stage);
				if(stageList == null || stageList.size() == 0){
					return AjaxJson.error("模版阶段信息为空");
				}
			    caseStageService.saveBatchByTemplate(lawCase, stageTemplate, stageList);
			}
		}
		
		// 更新时 判断空数据状态  查询原有数据 进行替换
		if(StringUtils.isNotBlank(lawCase.getId())){
			Case oldCase = caseService.get(lawCase.getId());
			Double subjectMatter = lawCase.getSubjectMatter();
			Double contractMoney = lawCase.getContractMoney();
			Double actualBackMoney = lawCase.getActualBackMoney();
			Double winMoney = lawCase.getWinMoney();
			lawCase = CaseConstant.classCopyProperties(lawCase, oldCase);

			lawCase.setSubjectMatter( subjectMatter );
			lawCase.setContractMoney( contractMoney );
			lawCase.setActualBackMoney(actualBackMoney);
			lawCase.setWinMoney(winMoney);
		}
		// 判断、处理 字段值内容
		if(StringUtils.isNotBlank(lawCase.getRemarks()) && "undefined".equals(lawCase.getRemarks())){
			lawCase.setRemarks("");
		}

		//新增或编辑表单保存
		caseService.save(lawCase);//保存
		return AjaxJson.success("保存案件信息成功");
	}

	/**
	 * 新增保存 案件信息、案件当事人信息、案件承办人员信息
	 * @param caseVO
	 * @return
	 * @throws Exception
	 */
	@RequiresPermissions(value={"lawcase:case:add","lawcase:case:edit", "user"},logical=Logical.OR)
	@ApiOperation(value = "新增、拒绝重新修改后 保存")
	@PostMapping("saveInfo")
	public AjaxJson saveInfo(@RequestBody CaseVO caseVO) throws Exception{
		Case lawCase = caseVO.getLawCase();
		String errMsg = beanValidator(lawCase);
		if (StringUtils.isNotBlank(errMsg)){
			return AjaxJson.error(errMsg);
		}
		if(lawCase.getCaseProgram() == null || StringUtils.isBlank(lawCase.getCaseProgram().getId())){
			return AjaxJson.error("案件程序不能为空");
		}
		if(lawCase.getHostUser() == null || StringUtils.isBlank(lawCase.getHostUser().getId())){
			return AjaxJson.error("主办人员信息不能为空");
		}
		// 判断当事人信息
		List<CaseConcernPerson> personList = caseVO.getConcernPersonList();
		if(personList == null || personList.size() == 0){
			return AjaxJson.error("请填写当事人信息");
		}
		List<CaseConcernPerson> newPersonList = personList.stream().filter(obj -> StringUtils.isNotBlank(obj.getName())).collect(Collectors.toList());
		if(personList.size() != newPersonList.size()){
			return AjaxJson.error("请完善当事人信息");
		}
		StageTemplate  stageTemplate1 =null;
		if(StringUtils.isNotBlank(lawCase.getMtypeId())){
			stageTemplate1 =stageTemplateService.get(lawCase.getMtypeId());
		    if(stageTemplate1==null) {
		    	return AjaxJson.error("请选择正确的模板");
		    }
		    lawCase.setMtypeName(stageTemplate1.getCaseModelName()+"/"+stageTemplate1.getName());
		}

		try {
			/* 是否首次 新纪录  保存案件。。若是首次创建案件则默认选择系统模板 创建待办事项  */
			boolean isFirst = StringUtils.isBlank(lawCase.getId());
			Case newLawCase = caseService.saveInfo(caseVO);
			// 查询系统模版 进行创建阶段-待办事项信息 2022-03-21 新增
			if(isFirst){
				try {
					if(stageTemplate1!=null) {
						Stage stage = new Stage();
						stage.setStageTemplate(stageTemplate1);
						// 获取阶段、阶段待办事项信息
						List<StageVO> stageList = stageService.findListByTemplate(stage);
						if(stageList != null && stageList.size() > 0){
							caseStageService.saveBatchByTemplate(newLawCase, stageTemplate1, stageList);
						}
					}else {
						StageTemplate stageTemplate = new StageTemplate();
						stageTemplate.setIsSystem(JeePlusProperites.YES);
						stageTemplate.setType(StageTemplate.TYPE_SYSTEM);
						List<StageTemplate> templateList = stageTemplateService.findList(stageTemplate);
						if(templateList != null && templateList.size() > 0){
							stageTemplate = templateList.get(0);
							Stage stage = new Stage();
							stage.setStageTemplate(stageTemplate);
							// 获取阶段、阶段待办事项信息
							List<StageVO> stageList = stageService.findListByTemplate(stage);
							if(stageList != null && stageList.size() > 0){
								caseStageService.saveBatchByTemplate(newLawCase, stageTemplate, stageList);
							}
						}
					
					}
				}catch (Exception e){
					e.printStackTrace();
					System.out.println("根据模版创建阶段-待办事项失败");
				}
			}

			return AjaxJson.success("保存案件信息成功");
		}catch (Exception e){
			e.printStackTrace();
			return AjaxJson.error("保存案件信息失败");
		}
	}

//	@RequiresPermissions("lawcase:case:edit")
//	@ApiOperation(value = "案件取消/终止", consumes = "application/form-data")
//	@ApiImplicitParam(value = "案件id值", name = "id", required = true)
	@PostMapping("cancel")
	public AjaxJson cancel(Case lawCase) {
		if(StringUtils.isBlank(lawCase.getId()) || StringUtils.isBlank(lawCase.getStatus())){
			return AjaxJson.error("案件信息有误");
		}
		if(CaseConstant.CASE_STATUS_CANCEL.equals(lawCase.getStatus())){
			return AjaxJson.error("该案件信息已取消，不可重复操作");
		}

		List<String> statusList = new ArrayList<String>(){ {
			this.add(CaseConstant.CASE_STATUS_PROCESS);
		} };
		if(!statusList.contains(lawCase.getStatus())){
			return AjaxJson.error("该案件信息不可进行取消操作");
		}
		try {
			lawCase.setStatus(CaseConstant.CASE_STATUS_CANCEL);
			caseService.updateStatus(lawCase);
			return AjaxJson.success("案件取消成功");
		}catch (Exception e){
			return AjaxJson.error("案件取消失败");
		}
	}

	@ApiOperation(value = "提交审核", consumes = "application/form-data")
	@ApiImplicitParams({
			@ApiImplicitParam(value = "案件id值", name = "id", required = true)
	})
	@PostMapping("submitAudit")
	public AjaxJson submitAudit(Case lawCase) {
		if(StringUtils.isBlank(lawCase.getId())){
			return AjaxJson.error("案件信息有误");
		}
		if(CaseConstant.CASE_AUDIT_STATUS_PASS.equals(lawCase.getAuditStatus())){
			return AjaxJson.error("审核已通过，不可进行该操作。");
		}
		if(CaseConstant.CASE_STATUS_CANCEL.equals(lawCase.getStatus())){
			return AjaxJson.error("案件已终止，不可进行该操作。");
		}
		if(CaseConstant.CASE_AUDIT_STATUS_WAIT.equals(lawCase.getAuditStatus())){
			return AjaxJson.success("提交成功");
		}
		try {
			lawCase.setAuditStatus( CaseConstant.CASE_AUDIT_STATUS_WAIT );
			lawCase.setAuditReason("");
			caseService.updateAuditStatus(lawCase);
			try {
				// 通知管理员 消息推送
				auditUserMsgPush(lawCase);
			} catch (Exception e) {
				e.printStackTrace();
			}
			return AjaxJson.success("提交成功");
		}catch (Exception e){
			return AjaxJson.error("提交失败");
		}
	}

	/**
	 * 审核人员 进行案件消息推送
	 * 1、查询管理人员信息
	 * 2、根据管理人员 获取对应的推送 clientId
	 * 3、消息推送
	 * @param lawCase
	 */
	private void auditUserMsgPush(Case lawCase){
		User user = new User();
		user.setType(User.TYPE_MANAGE);
		List<User> userList = userService.findBriefList(user);
		userList = userList.stream().filter(obj -> StringUtils.isNotBlank(obj.getLoginName())).collect(Collectors.toList());
		// 根据管理人员 登录名 获取推送clientId 进行消息推送
		List<String> clientIdList = new ArrayList<>();
		if(userList.size() > 0){
			Map<String, List<String>> userClientMap = GeTuiPushUtils.getUserClientMap();
			for (User us : userList) {
				List<String> list = userClientMap.get(us.getLoginName());
				if(list != null && list.size() > 0){
					clientIdList.addAll(list);
				}
			}
		}
		if(clientIdList.size() > 0){
			String hostUserName = lawCase.getHostUser().getName();
			String content = (StringUtils.isNotBlank(hostUserName) ? hostUserName : "") +"提交的‘"+ lawCase.getName() +"’案件需审核，请尽快处理！";

			PushParam pushParam = new PushParam();
			pushParam.setClientIdArr(clientIdList);
			pushParam.setTitle("案件审核提醒");
			pushParam.setContent(content);
			GeTuiPushUtils.pushBatchByCid( Arrays.asList(pushParam) );
		}
	}

	/**
	 * 案件审核  通过、拒绝
	 * @param lawCase
	 * @param result
	 * @return
	 */
	@RequiresPermissions(value = {"lawcase:case:audit", "user"}, logical = Logical.OR)
	@ApiOperation(value = "案件审核", consumes = "application/form-data")
	@ApiImplicitParams({
			@ApiImplicitParam(value = "案件id值", name = "id", required = true),
			@ApiImplicitParam(value = "审批意见", name = "reason", required = true),
			@ApiImplicitParam(value = "审核结果; 参数：1、通过  2、拒绝", name = "result", required = true)
	})
	@PostMapping("audit")
	public AjaxJson audit(Case lawCase, String result, String reason) {
		List<String> paramList = new ArrayList<String>(){ { this.add(CaseConstant.PASS); this.add(CaseConstant.REJECT); } };
		if(StringUtils.isBlank(result) || !paramList.contains(result)){
			return AjaxJson.error("参数错误");
		}
		if(StringUtils.isBlank(reason)){
			return AjaxJson.error("请填写审批意见");
		}
		if(StringUtils.isBlank(lawCase.getId()) || StringUtils.isBlank(lawCase.getStatus())){
			return AjaxJson.error("案件信息有误");
		}
		if(!CaseConstant.CASE_AUDIT_STATUS_WAIT.equals(lawCase.getAuditStatus())){
			return AjaxJson.error("该案件不可进行该操作");
		}

		try {
			String auditMsg = " 审批不通过";
			String auditStatus = CaseConstant.CASE_AUDIT_STATUS_REJECT;
			if(CaseConstant.PASS.equals( result )){
				auditMsg = " 审批通过";
				auditStatus = CaseConstant.CASE_AUDIT_STATUS_PASS;
			}
			lawCase.setAuditStatus( auditStatus );
			lawCase.setAuditReason( CaseConstant.getCaseAuditReason(auditMsg +"。\n审批意见：\n"+ reason) );
			caseService.updateAuditStatus(lawCase);
			return AjaxJson.success("案件审核成功");
		}catch (Exception e){
			return AjaxJson.error("案件审核失败");
		}
	}

	@ApiOperation(value = "案件终止", consumes = "application/form-data")
	@ApiImplicitParams({
			@ApiImplicitParam(value = "案件id值", name = "id", required = true),
			@ApiImplicitParam(value = "审批意见", name = "reason", required = true)
	})
	@RequiresPermissions(value = {"lawcase:case:audit", "user"}, logical = Logical.OR)
	@PostMapping("caseStop")
	public AjaxJson caseStop(Case lawCase, String reason) {
		if(StringUtils.isBlank(lawCase.getId()) || StringUtils.isBlank(lawCase.getStatus())){
			return AjaxJson.error("案件信息有误");
		}
		if(StringUtils.isBlank(reason)){
			return AjaxJson.error("请填写审批意见");
		}
		if(!CaseConstant.CASE_AUDIT_STATUS_WAIT.equals(lawCase.getAuditStatus())){
			return AjaxJson.error("该案件不可进行该操作");
		}
		try {
			lawCase.setAuditStatus( CaseConstant.CASE_AUDIT_STATUS_REJECT );
			lawCase.setAuditReason( CaseConstant.getCaseAuditReason(" 审批案件终止。\n终止意见：\n"+ reason) );
			lawCase.setStatus(CaseConstant.CASE_STATUS_CANCEL);
			caseService.caseStop(lawCase);
			return AjaxJson.success("案件终止成功");
		}catch (Exception e){
			return AjaxJson.error("案件终止失败");
		}
	}

	/**
	 * 结案
	 * @param lawCase
	 * @return
	 */
	@ApiOperation(value = "案件结案", consumes = "application/form-data")
	@ApiImplicitParams({
			@ApiImplicitParam(value = "案件id值", name = "id", required = true)
	})
	@PostMapping("settle")
	public AjaxJson settle(Case lawCase) {
		if(StringUtils.isBlank(lawCase.getId()) || StringUtils.isBlank(lawCase.getStatus())){
			return AjaxJson.error("案件信息有误");
		}
		if(CaseConstant.CASE_STATUS_SETTLE.equals(lawCase.getStatus())){
			return AjaxJson.error("该案件已完成结案，不可重复操作");
		}
		if(CaseConstant.CASE_STATUS_CANCEL.equals(lawCase.getStatus())){
			return AjaxJson.error("案件已终止，不可进行该操作。");
		}
		if(!CaseConstant.CASE_STATUS_PROCESS.equals(lawCase.getStatus())){
			return AjaxJson.error("该案件不可进行该操作");
		}

		try {
			lawCase.setStatus( CaseConstant.CASE_STATUS_SETTLE );
			caseService.updateSettle(lawCase);
			return AjaxJson.success("案件结案完成");
		}catch (Exception e){
			return AjaxJson.error("案件结案失败");
		}
	}

	/**
	 * 案件归档 TODO 2021-09-23 变更  归档改为文件下载
	 * @param lawCase
	 * @return
	 */
//	@ApiOperation(value = "案件归档", consumes = "application/form-data")
//	@ApiImplicitParams({
//			@ApiImplicitParam(value = "案件id值", name = "id", required = true)
//	})
	@PostMapping("archive")
	public AjaxJson archive(Case lawCase) {
		if(StringUtils.isBlank(lawCase.getId()) || StringUtils.isBlank(lawCase.getStatus())){
			return AjaxJson.error("案件信息有误");
		}
		if(CaseConstant.CASE_STATUS_ARCHIVE.equals(lawCase.getStatus())){
			return AjaxJson.error("该案件已完成归档，不可重复操作");
		}
		if(CaseConstant.CASE_STATUS_CANCEL.equals(lawCase.getStatus())){
			return AjaxJson.error("案件已终止，不可进行该操作。");
		}
		if(!CaseConstant.CASE_STATUS_SETTLE.equals(lawCase.getStatus())){
			return AjaxJson.error("该案件不可进行该操作");
		}

		try {
			lawCase.setStatus( CaseConstant.CASE_STATUS_ARCHIVE );
			caseService.updateArchive(lawCase);
			return AjaxJson.success("案件归档完成");
		}catch (Exception e){
			return AjaxJson.error("案件归档失败");
		}
	}

	/**
	 * 电子归档 文件下载
	 * 根据 传的id值进行判断 单个下载 批量下载，对文件夹单独封装
	 * @date 2021-09-23  变更
	 * @param ids
	 * @return
	 */
	@ApiOperation(value = "案件归档-文件下载", consumes = "application/form-data")
	@ApiImplicitParams({
			@ApiImplicitParam(value = "案件id值，多个以 , 隔开", name = "ids", required = true)
	})
	@GetMapping("archiveDownload")
	public void archiveDownload(String ids, HttpServletResponse response) {
		// 文件夹包 转换 zip 转换对象
		PackToZipUtils packToZip = new PackToZipUtils().createSrcDir();
		File zipFile = null;
		if(ids.contains(",")){
			Map<String, List<PackToZipUtils.PackDTO>> packMap = Maps.newHashMap();
			for (String id : ids.split(",")) {
				Case lawCase = caseService.get(id);
				Map<String, List<PackToZipUtils.PackDTO>> map = getFilePackMap(lawCase);
				if(map.keySet().size() > 0){
					packMap.putAll(map);
				}
			}
			String dirName = "/案件归档";
			packToZip.handleBatchCaseFile(dirName, packMap);
			zipFile = packToZip.createZipFile(dirName, dirName);
		}else {
			Case lawCase = caseService.get(ids);
			Map<String, List<PackToZipUtils.PackDTO>> map = getFilePackMap(lawCase);

			if(map.keySet().size() > 0){
				String dirName = ("/"+ lawCase.getName());
				packToZip.handleBatchCaseFile("", map);
				zipFile = packToZip.createZipFile(dirName, lawCase.getName());
			}
		}

		FileInputStream fis = null;
		OutputStream out = null;
		String downloadFileName = (zipFile == null) ? "案件归档.zip" : zipFile.getName();
		try {
			response.reset();
			response.setContentType("application/octet-stream; charset=utf-8");
			response.setHeader("Content-Disposition", "attachment; filename="+ URLEncoder.encode(downloadFileName, "UTF8"));
			//通过response对象获取outputStream流
			out = response.getOutputStream();

			fis = new FileInputStream(zipFile);
			int len = 0;
			//创建数据缓冲区
			byte[] buffer = new byte[1024];

			//将FileInputStream流写入到buffer缓冲区
			while((len = fis.read(buffer)) > 0) {
				//使用OutputStream将缓冲区的数据输出到浏览器
				out.write(buffer,0,len);
			}
		} catch (IOException e) {
			e.printStackTrace();
		}finally {
			try {
				if(fis!=null){ fis.close(); }
				// 删除案件归档  文件夹、压缩包 临时文件
				if(packToZip != null){
					packToZip.deleteSrcFile();
				}
				if (out != null){ out.close(); }
			} catch (IOException e) {
				logger.info(e.getMessage(), e);
			}
		}
	}

	/**
	 * 根据案件 获取该案件的所有文件信息 及其目录信息
	 * @param lawCase
	 * @return
	 */
	private Map<String, List<PackToZipUtils.PackDTO>> getFilePackMap(Case lawCase){
		Map<String, List<PackToZipUtils.PackDTO>> map = Maps.newHashMap();
		if(StringUtils.isNotBlank(lawCase.getId()) && StringUtils.isNotBlank(lawCase.getName())){
			// 根据案件id 获取文档信息+ 文档路径
			List<PackToZipUtils.PackDTO> packList = caseFileDirectoryService.getFilePackList(lawCase.getId());
			map.put("/"+ lawCase.getName() +"/案件文档/", packList);
			List<PackToZipUtils.PackDTO> todoPackList = todoInfoService.getFilePackList(lawCase.getId());
			map.put("/"+ lawCase.getName() +"/案件流程/", todoPackList);
		}
		return map;
	}


	@ApiOperation(value = "修改受理单位", consumes = "application/form-data")
	@ApiImplicitParams({
			@ApiImplicitParam(value = "案件id值", name = "id", required = true),
			@ApiImplicitParam(value = "地区", name = "acceptUnitArea"),
			@ApiImplicitParam(value = "单位类型 字典值：accept_unit_type", name = "acceptUnitType"),
			@ApiImplicitParam(value = "单位名称", name = "acceptUnitName")
	})
	@PostMapping("saveAccept")
	public AjaxJson saveAccept(Case lawCase) {
		if(StringUtils.isBlank(lawCase.getId())){
			return AjaxJson.error("案件信息有误");
		}
		try {
			caseService.updateAccept(lawCase);
			return AjaxJson.success("保存成功");
		}catch (Exception e){
			return AjaxJson.error("保存失败");
		}
	}

	/**
	 * 批量删除案件信息
	 */
	@RequiresPermissions(value = {"lawcase:case:del", "user"}, logical = Logical.OR)
	@ApiOperation(value = "删除", notes = "删除案件信息", consumes = "application/form-data")
	@ApiImplicitParam(value = "id值拼接以,拼接字符串", name = "ids", required = true)
	@PostMapping("delete")
	public AjaxJson delete(String ids) {
		if(StringUtils.isBlank(ids)){
			return AjaxJson.error("参数错误");
		}
		String idArray[] =ids.split(",");
		for(String id : idArray){
//			caseService.delete(new Case(id));
			caseService.deleteAllRelation(new Case(id));
		}
		return AjaxJson.success("删除案件信息成功");
	}

	/**
	 * 导出excel文件
	 */
	@RequiresPermissions("lawcase:case:export")
    @GetMapping("export")
    public AjaxJson exportFile(Case lawCase, HttpServletRequest request, HttpServletResponse response) {
		try {
            String fileName = "案件信息"+DateUtils.getDate("yyyyMMddHHmmss")+".xlsx";
            Page<Case> page = caseService.findPage(new Page<Case>(request, response, -1), lawCase);
    		new ExportExcel("案件信息", Case.class).setDataList(page.getList()).write(response, fileName).dispose();
    		return null;
		} catch (Exception e) {
			return AjaxJson.error("导出案件信息记录失败！失败信息："+e.getMessage());
		}
    }

	/**
	 * 导入Excel数据

	 */
	@RequiresPermissions("lawcase:case:import")
    @PostMapping("import")
   	public AjaxJson importFile(@RequestParam("file")MultipartFile file, HttpServletResponse response, HttpServletRequest request) {
		try {
			int successNum = 0;
			int failureNum = 0;
			StringBuilder failureMsg = new StringBuilder();
			ImportExcel ei = new ImportExcel(file, 1, 0);
			List<Case> list = ei.getDataList(Case.class);
			for (Case lawCase : list){
				try{
					caseService.save(lawCase);
					successNum++;
				}catch(ConstraintViolationException ex){
					failureNum++;
				}catch (Exception ex) {
					failureNum++;
				}
			}
			if (failureNum>0){
				failureMsg.insert(0, "，失败 "+failureNum+" 条案件信息记录。");
			}
			return AjaxJson.success( "已成功导入 "+successNum+" 条案件信息记录"+failureMsg);
		} catch (Exception e) {
			return AjaxJson.error("导入案件信息失败！失败信息："+e.getMessage());
		}
    }

	/**
	 * 下载导入案件信息数据模板
	 */
	@RequiresPermissions("lawcase:case:import")
    @GetMapping("import/template")
     public AjaxJson importFileTemplate(HttpServletResponse response) {
		try {
            String fileName = "案件信息数据导入模板.xlsx";
    		List<Case> list = Lists.newArrayList();
    		new ExportExcel("案件信息数据", Case.class, 1).setDataList(list).write(response, fileName).dispose();
    		return null;
		} catch (Exception e) {
			return AjaxJson.error( "导入模板下载失败！失败信息："+e.getMessage());
		}
    }


}
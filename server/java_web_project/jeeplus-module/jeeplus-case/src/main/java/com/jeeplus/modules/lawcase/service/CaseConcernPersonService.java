/**
 * Copyright © 2015-2020 <a href="http://www.jeeplus.org/">JeePlus</a> All rights reserved.
 */
package com.jeeplus.modules.lawcase.service;

import java.util.List;

import com.jeeplus.modules.lawcase.dto.CaseConcernPersonDto;
import com.jeeplus.modules.lawcase.entity.CaseConcernPerson;
import com.jeeplus.modules.lawcase.mapper.CaseConcernPersonMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.jeeplus.core.persistence.Page;
import com.jeeplus.core.service.CrudService;

/**
 * 案件当事人Service
 * <AUTHOR>
 * @version 2021-08-02
 */
@Service
@Transactional(readOnly = true)
public class CaseConcernPersonService extends CrudService<CaseConcernPersonMapper, CaseConcernPerson> {

	public CaseConcernPerson get(String id) {
		return super.get(id);
	}
	
	public List<CaseConcernPerson> findList(CaseConcernPerson caseConcernPerson) {
		return super.findList(caseConcernPerson);
	}
	
	public Page<CaseConcernPerson> findPage(Page<CaseConcernPerson> page, CaseConcernPerson caseConcernPerson) {
		return super.findPage(page, caseConcernPerson);
	}

	public Page<CaseConcernPersonDto> findOverallPage(Page<CaseConcernPersonDto> page, CaseConcernPersonDto caseConcernPersonDto) {
		dataRuleFilter(caseConcernPersonDto);
		caseConcernPersonDto.setPage(page);
		page.setList(mapper.findOverallList(caseConcernPersonDto));
		return page;
	}
	
	@Transactional(readOnly = false)
	public void save(CaseConcernPerson caseConcernPerson) {
		super.save(caseConcernPerson);
	}

	@Transactional(readOnly = false)
	public void delete(CaseConcernPerson caseConcernPerson) {
		super.delete(caseConcernPerson);
	}

	/**
	 * 通过 案件信息 删除当事人信息
	 * @param lawCaseId
	 */
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public void deleteByCase(String lawCaseId) {
		mapper.deleteByCase(lawCaseId);
	}
}
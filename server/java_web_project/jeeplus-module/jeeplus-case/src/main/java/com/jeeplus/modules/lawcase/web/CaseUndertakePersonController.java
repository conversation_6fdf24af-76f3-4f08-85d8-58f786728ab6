/**
 * Copyright © 2015-2020 <a href="http://www.jeeplus.org/">JeePlus</a> All rights reserved.
 */
package com.jeeplus.modules.lawcase.web;

import java.util.List;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.ConstraintViolationException;

import com.jeeplus.modules.lawcase.entity.CaseUndertakePerson;
import com.jeeplus.modules.lawcase.service.CaseUndertakePersonService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import com.google.common.collect.Lists;
import com.jeeplus.common.utils.DateUtils;
import com.jeeplus.common.json.AjaxJson;
import com.jeeplus.core.persistence.Page;
import com.jeeplus.core.web.BaseController;
import com.jeeplus.common.utils.StringUtils;
import com.jeeplus.common.utils.excel.ExportExcel;
import com.jeeplus.common.utils.excel.ImportExcel;

/**
 * 案件承办人员Controller
 * <AUTHOR>
 * @version 2021-08-02
 */
@RestController
@Api(tags = "案件-承办人员")
@RequestMapping(value = "/case/caseUndertakePerson")
public class CaseUndertakePersonController extends BaseController {

	@Autowired
	private CaseUndertakePersonService caseUndertakePersonService;

	@ModelAttribute
	public CaseUndertakePerson get(@RequestParam(required=false) String id) {
		CaseUndertakePerson entity = null;
		if (StringUtils.isNotBlank(id)){
			entity = caseUndertakePersonService.get(id);
		}
		if (entity == null){
			entity = new CaseUndertakePerson();
		}
		return entity;
	}

	/**
	 * 案件承办人员列表数据
	 */
//	@RequiresPermissions("case:caseUndertakePerson:list")
	@ApiOperation(value = "列表数据", consumes = "application/form-data")
	@ApiImplicitParams({
			@ApiImplicitParam(value = "案件id值", name = "lawCase.id", required = true)
	})
	@PostMapping("list")
	public AjaxJson list(CaseUndertakePerson caseUndertakePerson, HttpServletRequest request, HttpServletResponse response) {
		Page<CaseUndertakePerson> page = new Page<CaseUndertakePerson>(request, response);
		if(caseUndertakePerson.getLawCase() != null && StringUtils.isNotBlank(caseUndertakePerson.getLawCase().getId())){
			page = caseUndertakePersonService.findPage(page, caseUndertakePerson);
		}
		return AjaxJson.success().put("page",page);
	}

	/**
	 * 根据Id获取案件承办人员数据
	 */
//	@RequiresPermissions(value={"case:caseUndertakePerson:view","case:caseUndertakePerson:add","case:caseUndertakePerson:edit"},logical=Logical.OR)
	@ApiOperation(value = "详情", consumes = "application/form-data")
	@ApiImplicitParam(value = "id值", name = "id", required = true)
	@PostMapping("queryById")
	public AjaxJson queryById(CaseUndertakePerson caseUndertakePerson) {
		return AjaxJson.success().put("caseUndertakePerson", caseUndertakePerson);
	}

	/**
	 * 保存案件承办人员
	 */
	@RequiresPermissions(value={"case:caseUndertakePerson:add","case:caseUndertakePerson:edit", "user"},logical=Logical.OR)
	@ApiOperation(value = "保存", consumes = "application/form-data")
	@PostMapping("save")
	public AjaxJson save(CaseUndertakePerson caseUndertakePerson) throws Exception{
		/**
		 * 后台hibernate-validation插件校验
		 */
		String errMsg = beanValidator(caseUndertakePerson);
		if (StringUtils.isNotBlank(errMsg)){
			return AjaxJson.error(errMsg);
		}
		if(caseUndertakePerson.getLawCase() == null || StringUtils.isBlank(caseUndertakePerson.getLawCase().getId())){
			return AjaxJson.error("案件信息有误");
		}
		//新增或编辑表单保存
		try {
			caseUndertakePersonService.save(caseUndertakePerson);
			return AjaxJson.success("保存成功");
		} catch (Exception e) {
			e.printStackTrace();
			return AjaxJson.error("保存失败");
		}
	}


	/**
	 * 批量删除案件承办人员
	 */
	@RequiresPermissions(value = {"case:caseUndertakePerson:del", "user"}, logical = Logical.OR)
	@ApiOperation(value = "删除", consumes = "application/form-data")
	@ApiImplicitParam(value = "id值以,拼接", name = "ids", required = true)
	@PostMapping("delete")
	public AjaxJson delete(String ids) {
		String idArray[] =ids.split(",");
		for(String id : idArray){
			caseUndertakePersonService.delete(new CaseUndertakePerson(id));
		}
		return AjaxJson.success("删除成功");
	}

	/**
	 * 导出excel文件
	 */
	@RequiresPermissions("case:caseUndertakePerson:export")
    @GetMapping("export")
    public AjaxJson exportFile(CaseUndertakePerson caseUndertakePerson, HttpServletRequest request, HttpServletResponse response) {
		try {
            String fileName = "案件承办人员"+DateUtils.getDate("yyyyMMddHHmmss")+".xlsx";
            Page<CaseUndertakePerson> page = caseUndertakePersonService.findPage(new Page<CaseUndertakePerson>(request, response, -1), caseUndertakePerson);
    		new ExportExcel("案件承办人员", CaseUndertakePerson.class).setDataList(page.getList()).write(response, fileName).dispose();
    		return null;
		} catch (Exception e) {
			return AjaxJson.error("导出案件承办人员记录失败！失败信息："+e.getMessage());
		}
    }

	/**
	 * 导入Excel数据

	 */
	@RequiresPermissions("case:caseUndertakePerson:import")
    @PostMapping("import")
   	public AjaxJson importFile(@RequestParam("file")MultipartFile file, HttpServletResponse response, HttpServletRequest request) {
		try {
			int successNum = 0;
			int failureNum = 0;
			StringBuilder failureMsg = new StringBuilder();
			ImportExcel ei = new ImportExcel(file, 1, 0);
			List<CaseUndertakePerson> list = ei.getDataList(CaseUndertakePerson.class);
			for (CaseUndertakePerson caseUndertakePerson : list){
				try{
					caseUndertakePersonService.save(caseUndertakePerson);
					successNum++;
				}catch(ConstraintViolationException ex){
					failureNum++;
				}catch (Exception ex) {
					failureNum++;
				}
			}
			if (failureNum>0){
				failureMsg.insert(0, "，失败 "+failureNum+" 条案件承办人员记录。");
			}
			return AjaxJson.success( "已成功导入 "+successNum+" 条案件承办人员记录"+failureMsg);
		} catch (Exception e) {
			return AjaxJson.error("导入案件承办人员失败！失败信息："+e.getMessage());
		}
    }

	/**
	 * 下载导入案件承办人员数据模板
	 */
	@RequiresPermissions("case:caseUndertakePerson:import")
    @GetMapping("import/template")
     public AjaxJson importFileTemplate(HttpServletResponse response) {
		try {
            String fileName = "案件承办人员数据导入模板.xlsx";
    		List<CaseUndertakePerson> list = Lists.newArrayList();
    		new ExportExcel("案件承办人员数据", CaseUndertakePerson.class, 1).setDataList(list).write(response, fileName).dispose();
    		return null;
		} catch (Exception e) {
			return AjaxJson.error( "导入模板下载失败！失败信息："+e.getMessage());
		}
    }


}
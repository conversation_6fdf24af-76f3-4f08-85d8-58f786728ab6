/**
 * Copyright © 2015-2020 <a href="http://www.jeeplus.org/">JeePlus</a> All rights reserved.
 */
package com.jeeplus.modules.lawcase.web;

import java.util.ArrayList;
import java.util.List;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.ConstraintViolationException;

import com.alibaba.fastjson.JSONArray;
import com.jeeplus.common.utils.Encodes;
import com.jeeplus.modules.lawcase.constant.CaseConstant;
import com.jeeplus.modules.lawcase.entity.Case;
import com.jeeplus.modules.lawcase.entity.FinanceFlowFile;
import com.jeeplus.modules.lawcase.vo.CaseFinanceVO;
import com.jeeplus.modules.lawcase.vo.FinanceAccountVO;
import com.jeeplus.modules.sys.entity.User;
import com.jeeplus.modules.sys.utils.UserUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import com.google.common.collect.Lists;
import com.jeeplus.common.utils.DateUtils;
import com.jeeplus.common.json.AjaxJson;
import com.jeeplus.core.persistence.Page;
import com.jeeplus.core.web.BaseController;
import com.jeeplus.common.utils.StringUtils;
import com.jeeplus.common.utils.excel.ExportExcel;
import com.jeeplus.common.utils.excel.ImportExcel;
import com.jeeplus.modules.lawcase.entity.FinanceFlowRecord;
import com.jeeplus.modules.lawcase.service.FinanceFlowRecordService;

/**
 * 财务流水记录Controller
 * <AUTHOR>
 * @version 2021-10-29
 */
@RestController
@Api(tags = "财务流水")
@RequestMapping(value = "/lawcase/financeFlowRecord")
public class FinanceFlowRecordController extends BaseController {

	@Autowired
	private FinanceFlowRecordService financeFlowRecordService;

	@ModelAttribute
	public FinanceFlowRecord get(@RequestParam(required=false) String id) {
		FinanceFlowRecord entity = null;
		if (StringUtils.isNotBlank(id)){
			entity = financeFlowRecordService.get(id);
		}
		if (entity == null){
			entity = new FinanceFlowRecord();
		}
		return entity;
	}

	/**
	 * 财务记录列表数据
	 */
	@ApiOperation(value = "财务流水列表数据", consumes = "application/form-data")
	@RequiresPermissions(value = {"lawcase:financeFlowRecord:list", "user"}, logical = Logical.OR)
	@ApiImplicitParams({
			@ApiImplicitParam(value = "名称", name = "name"),
			@ApiImplicitParam(value = "财务类型", name = "type"),
			@ApiImplicitParam(value = "关联案件id", name = "lawCase.id"),
			@ApiImplicitParam(value = "发生人id", name = "happenUser.id"),
			@ApiImplicitParam(value = "查询日期区间 开始发生日期", name = "beginHappenDate"),
			@ApiImplicitParam(value = "查询日期区间 结束发生日期", name = "endHappenDate")
	})
	@PostMapping("list")
	public AjaxJson list(FinanceFlowRecord financeFlowRecord, HttpServletRequest request, HttpServletResponse response) {
		// 当前用户权限过滤
		financeFlowRecord = CaseConstant.dataFilterVerify(financeFlowRecord, (obj, user)->{
			obj.setCreateBy(user);
			return obj;
		});
		Page<FinanceFlowRecord> page = financeFlowRecordService.findInfoPage(new Page<FinanceFlowRecord>(request, response), financeFlowRecord);
		return AjaxJson.success().put("page",page);
	}

	@ApiOperation(value = "案件的财务流水列表。案件为必传字段", consumes = "application/form-data")
	@RequiresPermissions(value = {"lawcase:financeFlowRecord:list", "user"}, logical = Logical.OR)
	@ApiImplicitParams({
			@ApiImplicitParam(value = "名称", name = "name"),
			@ApiImplicitParam(value = "财务类型", name = "type"),
			@ApiImplicitParam(value = "关联案件id", name = "lawCase.id", required = true),
			@ApiImplicitParam(value = "发生人id", name = "happenUser.id"),
			@ApiImplicitParam(value = "查询日期区间 开始发生日期", name = "beginHappenDate"),
			@ApiImplicitParam(value = "查询日期区间 结束发生日期", name = "endHappenDate")
	})
	@PostMapping("listByCase")
	public AjaxJson listByCase(FinanceFlowRecord financeFlowRecord, HttpServletRequest request, HttpServletResponse response) {
		Page<FinanceFlowRecord> page = new Page<FinanceFlowRecord>(request, response);
		// 若案件信息不存在 则返回空数据
		if(financeFlowRecord.getLawCase() != null && StringUtils.isNotBlank(financeFlowRecord.getLawCase().getId())){
			financeFlowRecord = CaseConstant.dataFilterVerify(financeFlowRecord, (obj, user)->{
				obj.setCreateBy(user);
				return obj;
			});
			page = financeFlowRecordService.findInfoPage(page, financeFlowRecord);
		}
		return AjaxJson.success().put("page",page);
	}

	@ApiOperation(value = "案件财务统计数据列表", consumes = "application/form-data")
	@RequiresPermissions(value = {"user"}, logical = Logical.OR)
	@ApiImplicitParams({
			@ApiImplicitParam(value = "案件名称", name = "lawCase.name"),
			@ApiImplicitParam(value = "案件类型", name = "lawCase.type")
	})
	@PostMapping("caseFinanceList")
	public AjaxJson caseFinanceList(CaseFinanceVO caseFinance, HttpServletRequest request, HttpServletResponse response) {
		caseFinance = CaseConstant.dataFilterVerify(caseFinance, (obj, user)->{
			if(obj.getLawCase() == null){
				obj.setLawCase(new Case());
			}
			obj.getLawCase().setHostUser(user);
			return obj;
		});
		Page<CaseFinanceVO> page = financeFlowRecordService.findCaseFinancePage(new Page<CaseFinanceVO>(request, response), caseFinance);
		return AjaxJson.success().put("page",page);
	}

	@ApiOperation(value = "案件财务统计数据，根据案件id 查询此案件的财务统计信息", consumes = "application/form-data")
	@RequiresPermissions(value = {"user"}, logical = Logical.OR)
	@ApiImplicitParams({
			@ApiImplicitParam(value = "案件id", name = "caseId")
	})
	@PostMapping("queryCaseFinance")
	public AjaxJson getCaseFinance(String caseId) {
		CaseFinanceVO caseFinance = new CaseFinanceVO();
		if(StringUtils.isNotBlank(caseId)){
			caseFinance = financeFlowRecordService.getCaseFinance(caseId);
			if(caseFinance == null) {
				caseFinance = new CaseFinanceVO();
			}
		}
		return AjaxJson.success().put("data",caseFinance);
	}

	/**
	 * 根据Id获取财务记录数据
	 */
	@ApiOperation(value = "财务流水详情信息", consumes = "application/form-data")
	@RequiresPermissions(value={"lawcase:financeFlowRecord:view","lawcase:financeFlowRecord:add","lawcase:financeFlowRecord:edit", "user"},logical=Logical.OR)
	@ApiImplicitParam(value = "id值", name = "id")
	@PostMapping("queryById")
	public AjaxJson queryById(String id) {
		return AjaxJson.success().put("financeFlowRecord", financeFlowRecordService.getInfo(id));
	}

	/**
	 * 保存财务记录
	 */
	@ApiOperation(value = "编辑保存数据", consumes = "application/form-data")
	@RequiresPermissions(value={"lawcase:financeFlowRecord:add","lawcase:financeFlowRecord:edit", "user"},logical=Logical.OR)
	@PostMapping("save")
	public AjaxJson save(FinanceFlowRecord financeFlowRecord, String fileJsonStr) throws Exception{
		/**
		 * 后台hibernate-validation插件校验
		 */
		String errMsg = beanValidator(financeFlowRecord);
		if (StringUtils.isNotBlank(errMsg)){
			return AjaxJson.error(errMsg);
		}
		if(financeFlowRecord.getLawCase() == null || StringUtils.isBlank(financeFlowRecord.getLawCase().getId())){
			return AjaxJson.error("请选择案件信息");
		}
		if(financeFlowRecord.getMoney() == null || financeFlowRecord.getMoney() < 0){
			return AjaxJson.error("请填写金额信息");
		}
		try {
			List<FinanceFlowFile> fileList = new ArrayList<>();
			if(StringUtils.isNotBlank(fileJsonStr)){
				fileList = JSONArray.parseArray(Encodes.unescapeHtml(fileJsonStr), FinanceFlowFile.class);
				if(fileList == null){
					fileList = new ArrayList<>();
				}
			}
			financeFlowRecord.setFileList(fileList);
		}catch (Exception e){
			return AjaxJson.error("解析附件信息失败");
		}

		if(financeFlowRecord.getHappenUser() == null || StringUtils.isBlank(financeFlowRecord.getHappenUser().getId())){
			financeFlowRecord.setHappenUser(UserUtils.getUser());
		}
		try {
			financeFlowRecordService.saveInfo(financeFlowRecord);//保存
			return AjaxJson.success("保存成功");
		} catch (Exception e) {
			e.printStackTrace();
			return AjaxJson.error("保存失败");
		}

	}

	@ApiOperation(value = "新增保存数据")
	@RequiresPermissions(value={"lawcase:financeFlowRecord:add","lawcase:financeFlowRecord:edit", "user"},logical=Logical.OR)
	@PostMapping("saveInfo")
	public AjaxJson save(@RequestBody FinanceAccountVO financeAccount) throws Exception{
		/**
		 * 后台hibernate-validation插件校验
		 */
		String errMsg = beanValidator(financeAccount);
		if (StringUtils.isNotBlank(errMsg)){
			return AjaxJson.error(errMsg);
		}
		if(financeAccount.getLawCase() == null || StringUtils.isBlank(financeAccount.getLawCase().getId())){
			return AjaxJson.error("请选择案件信息");
		}
		List<FinanceFlowRecord> financeFlowRecordList = financeAccount.getFlowRecordList();
		if(financeFlowRecordList == null || financeFlowRecordList.size() == 0){
			return AjaxJson.error("请填写财务记录");
		}
		User user = UserUtils.getUser();
		for (FinanceFlowRecord flowRecord : financeFlowRecordList) {
			flowRecord.setName(financeAccount.getName());
			flowRecord.setType(financeAccount.getType());
			flowRecord.setReceivableMoney(financeAccount.getReceivableMoney());
			flowRecord.setLawCase(financeAccount.getLawCase());
			flowRecord.setAgreedPaymentDate(financeAccount.getAgreedPaymentDate());
			flowRecord.setRemarks(financeAccount.getRemarks());
			if(flowRecord.getHappenUser() == null || StringUtils.isBlank(flowRecord.getHappenUser().getId())){
				flowRecord.setHappenUser(user);
			}
			// 校验 参数
			String flowErrMsg = beanValidator(flowRecord);
			if (StringUtils.isNotBlank(flowErrMsg)){
				return AjaxJson.error(flowErrMsg);
			}

		}


		try {
			financeFlowRecordService.batchSaveInfo(financeFlowRecordList);
			return AjaxJson.success("保存成功");
		} catch (Exception e) {
			e.printStackTrace();
			return AjaxJson.error("保存失败");
		}

	}

	/**
	 * 批量删除财务记录
	 */
	@ApiOperation(value = "删除财务流水数据", consumes = "application/form-data")
	@RequiresPermissions(value = {"lawcase:financeFlowRecord:del", "user"}, logical = Logical.OR)
	@ApiImplicitParam(value = "id值拼接以,拼接字符串", name = "ids", required = true)
	@PostMapping("delete")
	public AjaxJson delete(String ids) {
		String idArray[] =ids.split(",");
		for(String id : idArray){
			financeFlowRecordService.delete(new FinanceFlowRecord(id));
		}
		return AjaxJson.success("删除记录成功");
	}

	/**
	 * 导出excel文件
	 */
	@RequiresPermissions("lawcase:financeFlowRecord:export")
    @GetMapping("export")
    public AjaxJson exportFile(FinanceFlowRecord financeFlowRecord, HttpServletRequest request, HttpServletResponse response) {
		try {
            String fileName = "财务记录"+DateUtils.getDate("yyyyMMddHHmmss")+".xlsx";
            Page<FinanceFlowRecord> page = financeFlowRecordService.findPage(new Page<FinanceFlowRecord>(request, response, -1), financeFlowRecord);
    		new ExportExcel("财务记录", FinanceFlowRecord.class).setDataList(page.getList()).write(response, fileName).dispose();
    		return null;
		} catch (Exception e) {
			return AjaxJson.error("导出财务记录记录失败！失败信息："+e.getMessage());
		}
    }

	/**
	 * 导入Excel数据

	 */
	@RequiresPermissions("lawcase:financeFlowRecord:import")
    @PostMapping("import")
   	public AjaxJson importFile(@RequestParam("file")MultipartFile file, HttpServletResponse response, HttpServletRequest request) {
		try {
			int successNum = 0;
			int failureNum = 0;
			StringBuilder failureMsg = new StringBuilder();
			ImportExcel ei = new ImportExcel(file, 1, 0);
			List<FinanceFlowRecord> list = ei.getDataList(FinanceFlowRecord.class);
			for (FinanceFlowRecord financeFlowRecord : list){
				try{
					financeFlowRecordService.save(financeFlowRecord);
					successNum++;
				}catch(ConstraintViolationException ex){
					failureNum++;
				}catch (Exception ex) {
					failureNum++;
				}
			}
			if (failureNum>0){
				failureMsg.insert(0, "，失败 "+failureNum+" 条财务记录记录。");
			}
			return AjaxJson.success( "已成功导入 "+successNum+" 条财务记录记录"+failureMsg);
		} catch (Exception e) {
			return AjaxJson.error("导入财务记录失败！失败信息："+e.getMessage());
		}
    }

	/**
	 * 下载导入财务记录数据模板
	 */
	@RequiresPermissions("lawcase:financeFlowRecord:import")
    @GetMapping("import/template")
     public AjaxJson importFileTemplate(HttpServletResponse response) {
		try {
            String fileName = "财务记录数据导入模板.xlsx";
    		List<FinanceFlowRecord> list = Lists.newArrayList();
    		new ExportExcel("财务记录数据", FinanceFlowRecord.class, 1).setDataList(list).write(response, fileName).dispose();
    		return null;
		} catch (Exception e) {
			return AjaxJson.error( "导入模板下载失败！失败信息："+e.getMessage());
		}
    }


}
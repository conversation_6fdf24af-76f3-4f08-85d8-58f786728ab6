/**
 * Copyright &copy; 2015-2020 <a href="http://www.jeesharp.org/">jeesharp</a> All rights reserved.
 */
package com.jeeplus.modules.lawcase.service;

import com.jeeplus.config.properties.JeePlusProperites;
import com.jeeplus.core.persistence.Page;
import com.jeeplus.core.service.CrudService;
import com.jeeplus.modules.lawcase.entity.Version;
import com.jeeplus.modules.lawcase.mapper.VersionMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 版本信息Service
 * <AUTHOR>
 * @version 2021-10-20
 */
@Service
@Transactional(readOnly = true)
public class VersionService extends CrudService<VersionMapper, Version> {

	public Version get(String id) {
		return super.get(id);
	}
	
	public List<Version> findList(Version version) {
		return super.findList(version);
	}
	
	public Page<Version> findPage(Page<Version> page, Version version) {
		return super.findPage(page, version);
	}
	
	@Transactional(readOnly = false)
	public void save(Version version) {
		super.save(version);
	}

	@Transactional(readOnly = false)
	public void saveInfo(Version version) {
		// 若是启用状态 则将其他启用状态记录 改为 未启用
		if (JeePlusProperites.YES.equals(version.getIsEnable())) {
			Version queryVersion = new Version();
			queryVersion.setIsEnable(JeePlusProperites.YES);
			List<Version> versionList = this.findList(queryVersion);
			if(versionList != null && versionList.size() > 0){
				String ids = versionList.stream().map(Version::getId).collect(Collectors.joining(","));
				mapper.updateEnable(JeePlusProperites.NO, ids);
			}
		}
		super.save(version);
	}

	@Transactional(readOnly = false)
	public void delete(Version version) {
		super.delete(version);
	}
	
}
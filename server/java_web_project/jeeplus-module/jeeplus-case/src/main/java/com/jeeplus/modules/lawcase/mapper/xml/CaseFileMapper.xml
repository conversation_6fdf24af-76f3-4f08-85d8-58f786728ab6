<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jeeplus.modules.lawcase.mapper.CaseFileMapper">

	<sql id="caseFileColumns">
		a.id AS "id",
		a.create_by AS "createBy.id",
		a.create_date AS "createDate",
		a.update_by AS "updateBy.id",
		a.update_date AS "updateDate",
		a.remarks AS "remarks",
		a.del_flag AS "delFlag",
		a.name AS "name",
		a.file_directory_id AS "fileDirectory.id",
		a.path AS "path"
	</sql>

	<sql id="caseFileJoins">
		LEFT JOIN law_case_file_directory fd ON a.file_directory_id = fd.id
	</sql>


	<select id="get" resultType="CaseFile" >
		SELECT
			<include refid="caseFileColumns"/>
		FROM law_case_file a
		<include refid="caseFileJoins"/>
		WHERE a.id = #{id}
	</select>

	<select id="findList" resultType="CaseFile" >
		SELECT
			<include refid="caseFileColumns"/>
		FROM law_case_file a
		<include refid="caseFileJoins"/>
		<where>
			a.del_flag = #{DEL_FLAG_NORMAL}
			${dataScope}
			<if test="name != null and name != ''">
				AND a.name LIKE
				    <if test="_databaseId == 'postgre'">'%'||#{name}||'%'</if>
					<if test="_databaseId == 'oracle'">'%'||#{name}||'%'</if>
					<if test="_databaseId == 'mssql'">'%'+#{name}+'%'</if>
					<if test="_databaseId == 'mysql'">concat('%',#{name},'%')</if>
			</if>
			<if test="fileDirectory != null and fileDirectory.id != null and fileDirectory.id != ''">
				AND (a.file_directory_id = #{fileDirectory.id} OR CONCAT(',', fd.parent_ids) LIKE concat('%,',#{fileDirectory.id},',%') )
			</if>
			<if test="queryFileDirectoryId != null and queryFileDirectoryId != ''">
				AND a.file_directory_id = #{queryFileDirectoryId}
			</if>
		</where>
		<choose>
			<when test="page !=null and page.orderBy != null and page.orderBy != ''">
				ORDER BY ${page.orderBy}
			</when>
			<otherwise>
				ORDER BY a.create_date DESC
			</otherwise>
		</choose>
	</select>

	<select id="findFileListByCase" resultType="CaseFile" >
		SELECT a.id AS "id", a.name AS "name", a.path AS "path"
			, a.file_directory_id AS "fileDirectory.id", fd.name AS "fileDirectory.name", fd.parent_ids AS "fileDirectory.parentIds"
		FROM law_case_file a
		INNER JOIN law_case_file_directory fd ON a.file_directory_id = fd.id AND fd.case_id = #{caseId}
	</select>

	<select id="findAllList" resultType="CaseFile" >
		SELECT
			<include refid="caseFileColumns"/>
		FROM law_case_file a
		<include refid="caseFileJoins"/>
		<where>
			a.del_flag = #{DEL_FLAG_NORMAL}
			${dataScope}
		</where>
		<choose>
			<when test="page !=null and page.orderBy != null and page.orderBy != ''">
				ORDER BY ${page.orderBy}
			</when>
			<otherwise>
				ORDER BY a.create_date DESC
			</otherwise>
		</choose>
	</select>

	<insert id="insert">
		INSERT INTO law_case_file(
			id,
			create_by,
			create_date,
			update_by,
			update_date,
			remarks,
			del_flag,
			name,
			file_directory_id,
			path
		) VALUES (
			#{id},
			#{createBy.id},
			#{createDate},
			#{updateBy.id},
			#{updateDate},
			#{remarks},
			#{delFlag},
			#{name},
			#{fileDirectory.id},
			#{path}
		)
	</insert>

	<update id="update">
		UPDATE law_case_file SET
			update_by = #{updateBy.id},
			update_date = #{updateDate},
			remarks = #{remarks},
			name = #{name},
			file_directory_id = #{fileDirectory.id},
			path = #{path}
		WHERE id = #{id}
	</update>


	<!--物理删除-->
	<update id="delete">
		DELETE FROM law_case_file
		WHERE id = #{id}
	</update>

	<!--逻辑删除-->
	<update id="deleteByLogic">
		UPDATE law_case_file SET
			del_flag = #{DEL_FLAG_DELETE}
		WHERE id = #{id}
	</update>


	<!-- 根据实体名称和字段名称和字段值获取唯一记录 -->
	<select id="findUniqueByProperty" resultType="CaseFile">
		select * FROM law_case_file  where ${propertyName} = #{value}
	</select>

</mapper>
/**
 * Copyright © 2015-2020 <a href="http://www.jeeplus.org/">JeePlus</a> All rights reserved.
 */
package com.jeeplus.modules.lawcase.mapper;

import org.springframework.stereotype.Repository;
import com.jeeplus.core.persistence.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import com.jeeplus.modules.lawcase.entity.CaseFile;

import java.util.List;

/**
 * 案件文档MAPPER接口
 * <AUTHOR>
 * @version 2021-08-13
 */
@Mapper
@Repository
public interface CaseFileMapper extends BaseMapper<CaseFile> {
    /**
     * 根据案件id 查询案件文档信息
     * @param caseId
     * @return
     */
    List<CaseFile> findFileListByCase(String caseId);
}
/**
 * Copyright © 2015-2020 <a href="http://www.jeeplus.org/">JeePlus</a> All rights reserved.
 */
package com.jeeplus.modules.lawcase.mapper;

import org.springframework.stereotype.Repository;
import com.jeeplus.core.persistence.TreeMapper;
import org.apache.ibatis.annotations.Mapper;
import com.jeeplus.modules.lawcase.entity.StageRecord;

import java.util.List;

/**
 * 阶段记录MAPPER接口
 * <AUTHOR>
 * @version 2021-08-12
 */
@Mapper
@Repository
public interface StageRecordMapper extends TreeMapper<StageRecord> {
    /**
     * 根据模版、阶段删除阶段记录
     * @param stageRecord
     * @return
     */
    int deleteByStage(StageRecord stageRecord);

    /**
     * 根据模版id删除记录
     * @param templateId
     * @return
     */
    int deleteByTemplate(String templateId);
}
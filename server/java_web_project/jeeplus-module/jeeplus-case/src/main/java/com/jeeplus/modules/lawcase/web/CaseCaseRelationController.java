/**
 * Copyright © 2015-2020 <a href="http://www.jeeplus.org/">JeePlus</a> All rights reserved.
 */
package com.jeeplus.modules.lawcase.web;

import com.jeeplus.common.json.AjaxJson;
import com.jeeplus.common.utils.StringUtils;
import com.jeeplus.core.persistence.Page;
import com.jeeplus.core.web.BaseController;
import com.jeeplus.modules.lawcase.entity.Case;
import com.jeeplus.modules.lawcase.entity.CaseCaseRelation;
import com.jeeplus.modules.lawcase.entity.CaseUser;
import com.jeeplus.modules.lawcase.entity.Stage;
import com.jeeplus.modules.lawcase.service.CaseCaseRelationService;
import com.jeeplus.modules.lawcase.service.CaseService;
import com.jeeplus.modules.lawcase.service.CaseUserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 案件与案件关联 Controller
 * <AUTHOR>
 * @version 2021-08-02
 */
@RestController
@Api(tags = "案件-关联案件信息")
@RequestMapping(value = "/case/caseRelation")
public class CaseCaseRelationController extends BaseController {

	@Autowired
	private CaseCaseRelationService caseRelationService;
	@Autowired
	private CaseService caseService;

	/**
	 * 案件成员 信息列表数据
	 */
	@RequiresPermissions(value = {"case:caseRelation:list", "user"}, logical = Logical.OR)
	@ApiOperation(value = "关联案件信息列表", consumes = "application/form-data")
	@ApiImplicitParam(value = "案件id", name = "lawCase.id",required = true)
	@PostMapping("list")
	public AjaxJson list(CaseCaseRelation caseRelation, HttpServletRequest request, HttpServletResponse response) {
		Page<CaseCaseRelation> page = new Page<CaseCaseRelation>(request, response, -1);
		if(caseRelation.getLawCase() != null && StringUtils.isNotBlank(caseRelation.getLawCase().getId())){
			page = caseRelationService.findPage(page, caseRelation);
		}
		return AjaxJson.success().put("page",page);
	}

	@RequiresPermissions(value = {"case:caseRelation:list", "user"}, logical = Logical.OR)
	@ApiOperation(value = "案件信息(便于查询)", consumes = "application/form-data")
	@ApiImplicitParams({
			@ApiImplicitParam(value = "案件id", name = "caseId", required = true),
			@ApiImplicitParam(value = "案件名称", name = "name")
	})
	@PostMapping("caseList")
	public AjaxJson caseList(String name, String caseId, HttpServletRequest request, HttpServletResponse response) {
		return AjaxJson.success().put("data", caseRelationService.findRelationCaseList(caseId, name));
	}

	/**
	 * 根据Id获取关联案件数据
	 */
	@RequiresPermissions(value={"case:caseRelation:view","case:caseRelation:add","case:caseRelation:edit", "user"},logical=Logical.OR)
	@GetMapping("queryById")
	public AjaxJson queryById(CaseCaseRelation caseCaseRelation) {
		return AjaxJson.success().put("caseCaseRelation", caseCaseRelation);
	}

	/**
	 * 保存阶段信息
	 */
	@RequiresPermissions(value={"case:caseRelation:add","case:caseRelation:edit", "user"},logical=Logical.OR)
	@ApiOperation(value = "保存", consumes = "application/form-data")
	@ApiImplicitParams({
			@ApiImplicitParam(value = "案件id", name = "lawCase.id", required = true),
			@ApiImplicitParam(value = "关联案件id", name = "relationCase.id", required = true)
	})
	@PostMapping("save")
	public AjaxJson save(CaseCaseRelation caseCaseRelation) throws Exception{
		/**
		 * 后台hibernate-validation插件校验
		 */
		String errMsg = beanValidator(caseCaseRelation);
		if (StringUtils.isNotBlank(errMsg)){
			return AjaxJson.error(errMsg);
		}
		if(caseCaseRelation.getLawCase() == null || StringUtils.isBlank(caseCaseRelation.getLawCase().getId())){
			return AjaxJson.error("案件信息有误");
		}
		if(caseCaseRelation.getRelationCase() == null || StringUtils.isBlank(caseCaseRelation.getRelationCase().getId())){
			return AjaxJson.error("关联案件信息有误");
		}
		Case lawCase = caseService.get(caseCaseRelation.getLawCase());
		if(lawCase == null || StringUtils.isBlank(lawCase.getId())){
			return AjaxJson.error("案件信息有误");
		}
		caseCaseRelation.setLawCase(lawCase);

		/* 验证是否已关联 */
		List<CaseCaseRelation> list = caseRelationService.findList(caseCaseRelation);
		if(list != null && list.size() > 0){
			return AjaxJson.success("关联成功");
		}
		try {
			caseRelationService.save(caseCaseRelation);
			return AjaxJson.success("关联成功");
		} catch (Exception e) {
			e.printStackTrace();
			return AjaxJson.error("关联失败");
		}
	}


	/**
	 * 批量删除阶段信息
	 */
	@RequiresPermissions(value = {"case:caseRelation:del", "user"}, logical = Logical.OR)
	@ApiOperation(value = "删除", consumes = "application/form-data")
	@ApiImplicitParam(value = "记录id 多个以 , 拼接", name = "ids", required = true)
	@PostMapping("delete")
	public AjaxJson delete(String ids) {
		String idArray[] =ids.split(",");
		for(String id : idArray){
			caseRelationService.delete(new CaseCaseRelation(id));
		}
		return AjaxJson.success("移除成功");
	}



}
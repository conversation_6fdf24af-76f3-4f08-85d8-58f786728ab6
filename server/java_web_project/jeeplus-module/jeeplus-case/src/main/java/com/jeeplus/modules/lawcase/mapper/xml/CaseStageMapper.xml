<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jeeplus.modules.lawcase.mapper.CaseStageMapper">

	<sql id="caseStageColumns">
		a.id AS "id",
		a.create_by AS "createBy.id",
		a.create_date AS "createDate",
		a.update_by AS "updateBy.id",
		a.update_date AS "updateDate",
		a.remarks AS "remarks",
		a.del_flag AS "delFlag",
		a.case_id AS "lawCase.id",
		a.name AS "name",
		a.sort AS "sort",
		a.is_current AS "isCurrent"
	</sql>

	<sql id="caseStageJoins">

	</sql>


	<select id="get" resultType="CaseStage" >
		SELECT
			<include refid="caseStageColumns"/>
		FROM law_case_stage a
		<include refid="caseStageJoins"/>
		WHERE a.id = #{id}
	</select>

	<select id="getMaxSortByCase" resultType="int" >
		SELECT IFNULL(MAX(a.sort), 0) FROM law_case_stage a
		WHERE a.del_flag = '0' AND a.case_id = #{lawCaseId}
	</select>

	<select id="findList" resultType="CaseStage" >
		SELECT
			<include refid="caseStageColumns"/>
			<if test="checkWord != null and checkWord != '' and checkWord == 'statistic_todo' ">
				,IFNULL(td.totalAmount, 0) AS "todoTotalAmount"
				,IFNULL(td.completeAmount, 0) AS "todoCompleteAmount"
			</if>
		FROM law_case_stage a
		<include refid="caseStageJoins"/>
		<if test="checkWord != null and checkWord != '' and checkWord == 'statistic_todo' ">
			LEFT JOIN (
				SELECT a.stage_id, COUNT(*) AS "totalAmount", SUM( CASE WHEN a.status = '2' THEN 1 ELSE 0 END ) AS "completeAmount"
				FROM law_todo_info a
				WHERE a.del_flag = '0' AND a.relevance_type = '2' AND a.relevance_id = #{lawCase.id}
				GROUP BY a.stage_id
			) td ON a.id = td.stage_id
		</if>

		<where>
			a.del_flag = #{DEL_FLAG_NORMAL}
			${dataScope}
			<if test="lawCase != null and lawCase.id != null and lawCase.id != ''">
				AND a.case_id = #{lawCase.id}
			</if>
		</where>
		<choose>
			<when test="page !=null and page.orderBy != null and page.orderBy != ''">
				ORDER BY ${page.orderBy}
			</when>
			<otherwise>
				ORDER BY a.sort
			</otherwise>
		</choose>
	</select>

	<select id="findAllList" resultType="CaseStage" >
		SELECT
			<include refid="caseStageColumns"/>
		FROM law_case_stage a
		<include refid="caseStageJoins"/>
		<where>
			a.del_flag = #{DEL_FLAG_NORMAL}
			${dataScope}
		</where>
		<choose>
			<when test="page !=null and page.orderBy != null and page.orderBy != ''">
				ORDER BY ${page.orderBy}
			</when>
			<otherwise>
				ORDER BY a.update_date DESC
			</otherwise>
		</choose>
	</select>

	<insert id="insert">
		INSERT INTO law_case_stage(
			id,
			create_by,
			create_date,
			update_by,
			update_date,
			remarks,
			del_flag,
			case_id,
			name,
			sort,
			is_current
		) VALUES (
			#{id},
			#{createBy.id},
			#{createDate},
			#{updateBy.id},
			#{updateDate},
			#{remarks},
			#{delFlag},
			#{lawCase.id},
			#{name},
			#{sort},
			#{isCurrent}
		)
	</insert>

	<insert id="insertBatch">
		INSERT INTO law_case_stage(
			id,
			create_by,
			create_date,
			update_by,
			update_date,
			remarks,
			del_flag,
			case_id,
			`name`,
			sort,
			is_current
		) VALUES
		<foreach collection="list" item="item" separator=" , ">
			(
			#{item.id},
			#{item.createBy.id},
			#{item.createDate},
			#{item.updateBy.id},
			#{item.updateDate},
			#{item.remarks},
			#{item.delFlag},
			#{item.lawCase.id},
			#{item.name},
			#{item.sort},
			#{item.isCurrent}
			)
		</foreach>
	</insert>

	<update id="update">
		UPDATE law_case_stage SET
			update_by = #{updateBy.id},
			update_date = #{updateDate},
			remarks = #{remarks},
			case_id = #{lawCase.id},
			`name` = #{name},
			sort = #{sort},
			is_current = #{isCurrent}
		WHERE id = #{id}
	</update>

	<update id="updateSortByRange">
		UPDATE law_case_stage SET sort = (sort + #{difference})
		WHERE case_id = #{lawCaseId}
		<if test="startSort != null and startSort > '1'.toString">
			AND sort <![CDATA[  >=  ]]>  #{startSort}
		</if>
		<if test="endSort != null and endSort > '0'.toString">
			AND sort <![CDATA[  <=  ]]>  #{endSort}
		</if>
	</update>

	<update id="updateSortById">
		UPDATE law_case_stage SET sort = #{sort} WHERE id = #{id}
	</update>

	<update id="updateCurrent">
		UPDATE law_case_stage SET is_current = #{isCurrent}
		WHERE case_id = #{lawCaseId}
			<if test="oldIsCurrent != null and oldIsCurrent != ''">
				AND is_current = #{oldIsCurrent}
			</if>
			<if test="id != null and id != ''">
				AND id = #{id}
			</if>
	</update>

	<!--物理删除-->
	<update id="delete">
		DELETE FROM law_case_stage
		WHERE id = #{id}
	</update>

	<update id="deleteByCase">
		DELETE FROM law_case_stage WHERE case_id = #{lawCaseId}
	</update>

	<!--逻辑删除-->
	<update id="deleteByLogic">
		UPDATE law_case_stage SET
			del_flag = #{DEL_FLAG_DELETE}
		WHERE id = #{id}
	</update>


	<!-- 根据实体名称和字段名称和字段值获取唯一记录 -->
	<select id="findUniqueByProperty" resultType="CaseStage">
		select * FROM law_case_stage  where ${propertyName} = #{value}
	</select>

</mapper>
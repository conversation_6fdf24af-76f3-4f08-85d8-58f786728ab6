/**
 * Copyright &copy; 2015-2020 <a href="http://www.jeesharp.org/">jeesharp</a> All rights reserved.
 */
package com.jeeplus.modules.lawcase.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jeeplus.common.utils.excel.annotation.ExcelField;
import com.jeeplus.core.persistence.DataEntity;

import javax.validation.constraints.NotBlank;
import java.util.Date;

/**
 * 版本信息Entity
 * <AUTHOR>
 * @version 2021-10-20
 */
public class Version extends DataEntity<Version> {
	
	private static final long serialVersionUID = 1L;
	private String number;		// 版本号
	private String path;		// apk文件路径
	private String title;		// 标题
	private Date publishDate;	// 发布日期
	private String content;		// 内容
	private String isEnable;	// 是否启用
	
	public Version() {
		super();
	}

	public Version(String id){
		super(id);
	}

	@NotBlank(message = "版本号不能为空")
	@ExcelField(title="版本号", align=2, sort=8)
	public String getNumber() {
		return number;
	}

	public void setNumber(String number) {
		this.number = number;
	}

	public String getPath() {
		return path;
	}

	public void setPath(String path) {
		this.path = path;
	}

	@ExcelField(title="标题", align=2, sort=9)
	public String getTitle() {
		return title;
	}

	public void setTitle(String title) {
		this.title = title;
	}
	
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@ExcelField(title="发布日期", align=2, sort=10)
	public Date getPublishDate() {
		return publishDate;
	}

	public void setPublishDate(Date publishDate) {
		this.publishDate = publishDate;
	}
	
	@ExcelField(title="内容", align=2, sort=11)
	public String getContent() {
		return content;
	}

	public void setContent(String content) {
		this.content = content;
	}

	@ExcelField(title="是否启用", dictType = "yes_no", align=2, sort=12)
	public String getIsEnable() {
		return isEnable;
	}

	public void setIsEnable(String isEnable) {
		this.isEnable = isEnable;
	}
}
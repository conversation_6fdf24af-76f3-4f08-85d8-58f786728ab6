package com.jeeplus.modules.lawcase.util;

import cn.hutool.core.util.ZipUtil;
import com.jeeplus.common.utils.FileUtils;
import com.jeeplus.common.utils.StringUtils;
import com.jeeplus.config.properties.JeePlusProperites;
import com.jeeplus.modules.sys.utils.FileKit;
import lombok.Data;
import org.apache.commons.lang.math.RandomUtils;

import java.io.File;
import java.io.IOException;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 *
 */
public class PackToZipUtils {

    private String baseDir = JeePlusProperites.newInstance().getUserfilesBaseDir();
    private String srcPath = "";


    public PackToZipUtils(){
        this.srcPath =  this.baseDir + ("/caseTemp/case-"+ RandomUtils.nextInt(9999)) + File.separator ;
    }

    public PackToZipUtils createSrcDir(){
        FileUtils.createDirectory(this.srcPath);
        return this;
    }


    public void handleCaseFile (String dirName, List<PackDTO> packList){
        if (packList.size() > 0) {
            Map<String, List<PackDTO>> packMap = packList.stream().collect(
                    Collectors.toMap(PackDTO::getDirName, obj -> new ArrayList<PackDTO>() {{ this.add(obj); }}
                            , (t1, t2) -> {
                                List<PackDTO> list = new ArrayList<>(t1);
                                list.addAll(t2);
                                return list;
                            }
                    )
            );

            for (String dir : packMap.keySet()) {
                String dirPath = (this.srcPath + dirName+ File.separator + dir + File.separator);
                FileUtils.createDirectory(dirPath);

                List<PackDTO> list = packMap.get(dir);
                if(list != null && list.size() > 0){
                    // 循环判断 源文件是否存在 存在则进行复制
                    for (PackDTO pack : list) {
                        File sourceFile = new File(FileKit.getFileDir(pack.getPath()));
                        if(sourceFile.exists()){
                            // 处理文件名
                            String sourceFileName = sourceFile.getName();
                            String suffix = sourceFileName.substring(sourceFileName.lastIndexOf("."));
                            String fileName = pack.getFileName();
                            if(StringUtils.isBlank(fileName)){
                                fileName = sourceFileName;
                            }else{
                                if(!fileName.endsWith(suffix)) {
                                    fileName += suffix;
                                }
                            }
                            File newFile = FileUtils.getAvailableFile ((dirPath+ fileName), 0);
                            // 复制文件
                            try {
                                FileUtils.copyFile(sourceFile, newFile);
                            } catch (IOException e) {
                                e.printStackTrace();
                            }
                        }
                    }
                }
            }

        }
    }

    public void handleBatchCaseFile (String dirName, Map<String, List<PackDTO>> packMap){
        for (String dir : packMap.keySet()) {
            List<PackDTO> packList = packMap.get(dir);
            if(packList != null && packList.size() > 0){
                // 单独处理目录文件
                handleCaseFile((dirName + dir), packList);
            }
        }

    }

    public File createZipFile(String descDir, String zipName){
        String descPath = (this.srcPath + descDir);
        String zipPath = (this.srcPath + zipName +".zip");
        File zipFile = null;
        File descFile = new File(descPath);
        if(descFile.exists()){
            zipFile = ZipUtil.zip(descPath, zipPath, true);
        }
        return zipFile;
    }

    public void deleteSrcFile(){
        File srcDir = new File(this.srcPath);
        if (srcDir.exists()) {
            try {
                FileUtils.deleteDirectory(srcDir);
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }


    @Data
    public static class PackDTO implements Serializable {

        private static final long serialVersionUID = 3L;

        private String dirName;
        private String fileName;
        private String path;

    }

}

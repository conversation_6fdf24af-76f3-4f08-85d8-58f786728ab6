package com.jeeplus.modules.lawcase.util;

import com.google.common.collect.Lists;
import com.jeeplus.common.utils.StringUtils;
import com.jeeplus.core.persistence.TreeEntity;
import com.jeeplus.modules.lawcase.vo.BaseTreeVO;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 树结构工具类
 * <AUTHOR>
 * @date 2021-08-21
 */
public class TreeUtils {
    /**
     * 将list 数据处理为 上下级关系的List数据
     * @param list
     * @param <M>
     * @return
     */
    public static  <M extends BaseTreeVO<M>> List<M> getTreeData(List<M> list){
        List<M> treeList = new ArrayList<M>();
        if(list != null && list.size() > 0){
            Map<String, List<M>> map = listToMap(list);
            treeList = mapToTree(map, "0");
        }
        return treeList;
    }

    /**
     * list 数据转为 Map 结构
     * @param list
     * @param <T>
     * @return
     */
    private static  <T extends BaseTreeVO> Map<String, List<T>>  listToMap(List<T> list){
        return list.stream().collect(
                Collectors.toMap(T::getParentId, record -> new ArrayList<T>(){{ this.add(record); }}
                        , (oldList, newList) ->{
                            List<T> recordList = new ArrayList<T>(oldList);
                            recordList.addAll(newList);
                            return recordList;
                        }
                )
        );
    }

    /**
     * 递归使用 封装自己List
     * @param map
     * @param parentId
     * @param <T>
     * @return
     */
    private static <T extends BaseTreeVO<T>> List<T>  mapToTree (Map<String, List<T>> map, String parentId){
        List<T> list = null;
        if(map == null || (list = map.get(parentId)) == null){
            return new ArrayList<T>();
        }
        if(list.size() > 0){
            for (T obj : list) {
                List<T> children = mapToTree(map, obj.getId());
                obj.setChildren(children);
            }
        }
        return list;
    }

    /**
     * 将list 数据处理为 上下级关系的List数据  实体类结构
     * @param list
     * @param <M>
     * @return
     */
    public static <M extends TreeEntity<M>> List<M> getTreeDataEntity(List<M> list){
        List<M> treeList = Lists.newArrayList();
        if(list != null && list.size() > 0){
            Map<String, List<M>> map = list.stream().collect(
                    Collectors.toMap(M::getParentId, entity -> new ArrayList<M>(){{ this.add(entity); }}
                        , (oldList, newList) -> {
                                List<M> entityList = new ArrayList<M>(oldList);
                                entityList.addAll(newList);
                                return entityList;
                            }
                    )
            );
            treeList = mapToTreeEntity(map, "0");
        }
        return treeList;
    }

    /**
     * 递归 处理list 封装子级
     * @param map
     * @param parentId
     * @param <T>
     * @return
     */
    private static <T extends TreeEntity<T>> List<T>  mapToTreeEntity (Map<String, List<T>> map, String parentId){
        List<T> list = null;
        if(map == null || (list = map.get(parentId)) == null){
            return new ArrayList<T>();
        }
        if(list.size() > 0){
            for (T obj : list) {
                List<T> children = mapToTreeEntity(map, obj.getId());
                obj.setChildren(children);
            }
        }
        return list;
    }


    /**
     * 树结构（多级）转 List（一级）
     * @param parent    父级
     * @param voList    树结构List
     * @param function  泛型T 数据转换为 泛型M
     * @param <M>
     * @param <T>
     * @return
     */
    public static <M extends TreeEntity<M>, T extends BaseTreeVO<T>>   List<M>   treeToList (M parent, List<T> voList, Function<T, M> function){
        List<M> list = Lists.newArrayList();
        if(voList != null && voList.size() > 0){
            if(StringUtils.isBlank(parent.getId())){
                parent.setId("0");
                parent.setParentIds(StringUtils.EMPTY);
            }
            int sort = 0;
            for (T vo : voList) {
                if(StringUtils.isBlank(vo.getName())){
                   continue;
                }
                M entity = function.apply(vo);
                entity.setParent(parent);
                entity.setParentIds(parent.getParentIds() + parent.getId() +",");
                entity.setSort( ++sort );
                if(StringUtils.isBlank(entity.getId())){
                    entity.preInsert();
                }else {
                    entity.preUpdate();
                    entity.setCreateBy(entity.getUpdateBy());
                    entity.setCreateDate(entity.getUpdateDate());
                }
                list.add(entity);

                if (vo.getChildren() != null && vo.getChildren().size() > 0){
                    List<M> newList = treeToList(entity, vo.getChildren(), function);
                    list.addAll(newList);
                }
            }
        }
        return list;
    }


}

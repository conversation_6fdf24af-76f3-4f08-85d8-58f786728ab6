/**
 * Copyright © 2015-2020 <a href="http://www.jeeplus.org/">JeePlus</a> All rights reserved.
 */
package com.jeeplus.modules.lawcase.web;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.ConstraintViolationException;

import com.jeeplus.modules.lawcase.constant.CaseConstant;
import com.jeeplus.modules.lawcase.entity.Customer;
import com.jeeplus.modules.lawcase.entity.CustomerFollowUp;
import com.jeeplus.modules.lawcase.entity.TodoInfo;
import com.jeeplus.modules.lawcase.service.CustomerService;
import com.jeeplus.modules.lawcase.service.TodoInfoService;
import com.jeeplus.modules.lawcase.vo.CustomerVO;
import com.jeeplus.modules.sys.utils.DictUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import com.google.common.collect.Lists;
import com.jeeplus.common.utils.DateUtils;
import com.jeeplus.common.json.AjaxJson;
import com.jeeplus.core.persistence.Page;
import com.jeeplus.core.web.BaseController;
import com.jeeplus.common.utils.StringUtils;
import com.jeeplus.common.utils.excel.ExportExcel;
import com.jeeplus.common.utils.excel.ImportExcel;

/**
 *  客户信息Controller
 * <AUTHOR>
 * @version 2021-08-02
 */
@RestController
@Api(tags = "客户管理", description = "客户信息")
@RequestMapping(value = "/lawcase/customer")
public class CustomerController extends BaseController {

	@Autowired
	private CustomerService customerService;
	@Autowired
	private TodoInfoService todoInfoService;


	@ModelAttribute
	public Customer get(@RequestParam(required=false) String id) {
		Customer entity = null;
		if (StringUtils.isNotBlank(id)){
			entity = customerService.get(id);
		}
		if (entity == null){
			entity = new Customer();
		}
		return entity;
	}

	/**
	 * 客户信息列表数据
	 */
	@RequiresPermissions(value = {"lawcase:customer:list", "user"}, logical = Logical.OR)
	@ApiOperation(value = "客户信息数据", consumes = "application/form-data")
	@ApiImplicitParams({
			@ApiImplicitParam(value = "当前页码", name = "pageNo", required = true),
			@ApiImplicitParam(value = "每页数量", name = "pageSize", required = true),
			@ApiImplicitParam(value = "客户标识 字典：customer_type", name = "type"),
			@ApiImplicitParam(value = "姓名/单位名称", name = "name"),
			@ApiImplicitParam(value = "合作状态", name = "status"),
			@ApiImplicitParam(value = "客户重要性", name = "importance"),
			@ApiImplicitParam(value = "所属行业", name = "industry.id")
	})
	@PostMapping("list")
	public AjaxJson list(Customer customer, HttpServletRequest request, HttpServletResponse response) {
		// 数据过滤验证 仅 律师权限的用户只能看到 创建人是自己 的数据
		customer = CaseConstant.dataFilterVerify(customer, (cus, user) ->{
			cus.setCreateBy(user);
			return cus;
		});
		Page<Customer> page = customerService.findOverallPage(new Page<Customer>(request, response), customer);

		/* 对客户信息进行封装
			1、根据客户id 查询待办事项列表
			2、进行封装
		 */
		List<CustomerVO> customerVoList = new ArrayList<>();
		List<Customer> customerList = page.getList();
		// TODO 2021-09-08 变更，去掉客户待办事项数据展示
		/*if(customerList != null && customerList.size() > 0){
			// 待办事项
			TodoInfo todoInfo = new TodoInfo();
			String customerIds = customerList.stream().map(Customer::getId).collect(Collectors.joining());
			todoInfo.setRelevanceType(CaseConstant.TODO_RELEVANCE_TYPE_CUSTOMER);
			todoInfo.setStatus(CaseConstant.TODO_STATUS_WAIT);
			todoInfo.setType(CaseConstant.TODO_TYPE_TASK);
			todoInfo.setQueryRelevanceIds(customerIds);

			Map<String, List<TodoInfo>> todoMap = todoInfoService.findGroupMap(todoInfo);

			for (Customer ct : customerList) {
				CustomerVO customerVO = new CustomerVO(ct);
				List<TodoInfo> todoList = todoMap.get(ct.getId());
				if(todoList == null){ todoList = new ArrayList<>(); }
				customerVO.setTodoInfoList(todoList);
				customerVoList.add(customerVO);
			}
		}*/
		List<TodoInfo> todoList = new ArrayList<>();
		for (Customer ct : customerList) {
			CustomerVO customerVO = new CustomerVO(ct);
			customerVO.setTodoInfoList(todoList);
			customerVoList.add(customerVO);
		}

		Page<CustomerVO> pageVo = new Page<CustomerVO>();
		pageVo.setCount(page.getCount());
		pageVo.setPageNo(page.getPageNo());
		pageVo.setPageSize(page.getPageSize());
		pageVo.setList(customerVoList);
		return AjaxJson.success().put("page",pageVo);
	}

//	@RequiresPermissions("lawcase:customer:list")
	@ApiOperation("所有客户数据，没有分页")
	@ApiImplicitParams({
			@ApiImplicitParam(value = "姓名/单位名称", name = "name")
	})
	@PostMapping("allData")
	public AjaxJson allData(Customer customer) {
		// 数据过滤验证 仅 律师权限的用户只能看到 创建人是自己 的数据
		customer = CaseConstant.dataFilterVerify(customer, (cus, user) ->{
			cus.setCreateBy(user);
			return cus;
		});
		List<Customer> list = customerService.findList(customer);
		return AjaxJson.success().put("data",list);
	}

	/**
	 * 根据Id获取客户信息数据
	 */
	@RequiresPermissions(value={"lawcase:customer:view","lawcase:customer:add","lawcase:customer:edit", "user"},logical=Logical.OR)
	@ApiOperation(value = "基本信息", consumes = "application/form-data")
	@ApiImplicitParam(value = "信息id值", name = "id", required = true)
	@PostMapping("queryById")
	public AjaxJson queryById(Customer customer) {
		CustomerVO customerVo = new CustomerVO();
		customerVo.setCustomer(customer);
		customerVo.setCustomerFollowUpList(customerService.findFollowUpList(new CustomerFollowUp(customer)));
		return AjaxJson.success().put("data", customerVo);
	}

	/**
	 * 保存客户信息
	 */
	@ApiOperation("编辑保存")
	@RequiresPermissions(value={"lawcase:customer:add","lawcase:customer:edit", "user"},logical=Logical.OR)
	@PostMapping("save")
	public AjaxJson save(@RequestBody CustomerVO customerVO) throws Exception{
		/**
		 * 后台hibernate-validation插件校验
		 */
		Customer customer = customerVO.getCustomer();
		String errMsg = beanValidator(customer);
		if (StringUtils.isNotBlank(errMsg)){
			return AjaxJson.error(errMsg);
		}
		try {
			// 查询原有数据 进行替换
			if(StringUtils.isNotBlank(customer.getId())){
				Customer oldCustomer = customerService.get(customer.getId());
				customer = CaseConstant.classCopyProperties(customer, oldCustomer);
			}
			customerService.save(customer, customerVO.getCustomerFollowUpList());
			return AjaxJson.success("保存成功");
		} catch (Exception e) {
			e.printStackTrace();
			return AjaxJson.error("保存失败");
		}
	}

	/**
	 * 新增保存、 客户信息、客户跟进人信息、客户联系人
	 * @param customerVO
	 * @return
	 * @throws Exception
	 */
	@ApiOperation("新增保存")
	@RequiresPermissions(value={"lawcase:customer:add", "user"}, logical = Logical.OR)
	@PostMapping("saveInfo")
	public AjaxJson saveInfo(@RequestBody CustomerVO customerVO) throws Exception{
		/**
		 * 后台hibernate-validation插件校验
		 */
		Customer customer = customerVO.getCustomer();
		String errMsg = beanValidator(customer);
		if (StringUtils.isNotBlank(errMsg)){
			return AjaxJson.error(errMsg);
		}

		//新增或编辑表单保存
		try {
			customerService.saveInfo(customerVO);
			return AjaxJson.success("保存成功");
		} catch (Exception e) {
			e.printStackTrace();
			return AjaxJson.error("保存失败");
		}
	}

	/**
	 * 批量删除客户信息
	 */
	@ApiOperation(value = "删除", consumes = "application/form-data")
	@RequiresPermissions(value = {"lawcase:customer:del", "user"}, logical = Logical.OR)
	@PostMapping("delete")
	public AjaxJson delete(String ids) {
		if(StringUtils.isBlank(ids)){
			return AjaxJson.error("参数错误");
		}
		String idArray[] =ids.split(",");
		for(String id : idArray){
			customerService.delete(new Customer(id));
		}
		return AjaxJson.success("删除成功");
	}

	/**
	 * 导出excel文件
	 */
	@RequiresPermissions("lawcase:customer:export")
    @GetMapping("export")
    public AjaxJson exportFile(Customer customer, HttpServletRequest request, HttpServletResponse response) {
		try {
            String fileName = "客户信息"+DateUtils.getDate("yyyyMMddHHmmss")+".xlsx";
            Page<Customer> page = customerService.findPage(new Page<Customer>(request, response, -1), customer);
    		new ExportExcel("客户信息", Customer.class).setDataList(page.getList()).write(response, fileName).dispose();
    		return null;
		} catch (Exception e) {
			return AjaxJson.error("导出客户信息记录失败！失败信息："+e.getMessage());
		}
    }

	/**
	 * 导入Excel数据

	 */
	@RequiresPermissions("lawcase:customer:import")
    @PostMapping("import")
   	public AjaxJson importFile(@RequestParam("file")MultipartFile file, HttpServletResponse response, HttpServletRequest request) {
		try {
			int successNum = 0;
			int failureNum = 0;
			StringBuilder failureMsg = new StringBuilder();
			ImportExcel ei = new ImportExcel(file, 1, 0);
			List<Customer> list = ei.getDataList(Customer.class);
			for (Customer customer : list){
				try{
					customerService.save(customer);
					successNum++;
				}catch(ConstraintViolationException ex){
					failureNum++;
				}catch (Exception ex) {
					failureNum++;
				}
			}
			if (failureNum>0){
				failureMsg.insert(0, "，失败 "+failureNum+" 条客户信息记录。");
			}
			return AjaxJson.success( "已成功导入 "+successNum+" 条客户信息记录"+failureMsg);
		} catch (Exception e) {
			return AjaxJson.error("导入客户信息失败！失败信息："+e.getMessage());
		}
    }

	/**
	 * 下载导入客户信息数据模板
	 */
	@RequiresPermissions("lawcase:customer:import")
    @GetMapping("import/template")
     public AjaxJson importFileTemplate(HttpServletResponse response) {
		try {
            String fileName = "客户信息数据导入模板.xlsx";
    		List<Customer> list = Lists.newArrayList();
    		new ExportExcel("客户信息数据", Customer.class, 1).setDataList(list).write(response, fileName).dispose();
    		return null;
		} catch (Exception e) {
			return AjaxJson.error( "导入模板下载失败！失败信息："+e.getMessage());
		}
    }


}
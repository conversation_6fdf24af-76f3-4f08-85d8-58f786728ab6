/**
 * Copyright © 2015-2020 <a href="http://www.jeeplus.org/">JeePlus</a> All rights reserved.
 */
package com.jeeplus.modules.lawcase.service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.jeeplus.common.json.AjaxJson;
import com.jeeplus.modules.lawcase.entity.Case;
import com.jeeplus.modules.lawcase.entity.CaseFile;
import com.jeeplus.modules.lawcase.util.PackToZipUtils;
import com.jeeplus.modules.sys.utils.FileKit;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.jeeplus.core.service.TreeService;
import com.jeeplus.common.utils.StringUtils;
import com.jeeplus.modules.lawcase.entity.CaseFileDirectory;
import com.jeeplus.modules.lawcase.mapper.CaseFileDirectoryMapper;
import org.springframework.web.multipart.MultipartFile;

/**
 * 案件文档目录Service
 * <AUTHOR>
 * @version 2021-08-12
 */
@Service
@Transactional(readOnly = true)
public class CaseFileDirectoryService extends TreeService<CaseFileDirectoryMapper, CaseFileDirectory> {

	@Autowired
	private CaseFileService caseFileService;

	public CaseFileDirectory get(String id) {
		return super.get(id);
	}

	public List<CaseFileDirectory> findList(CaseFileDirectory caseFileDirectory) {
		if (StringUtils.isNotBlank(caseFileDirectory.getParentIds())){
			caseFileDirectory.setParentIds(","+caseFileDirectory.getParentIds()+",");
		}
		return super.findList(caseFileDirectory);
	}

	public List<PackToZipUtils.PackDTO> getFilePackList(String caseId){
		List<PackToZipUtils.PackDTO> packList = new ArrayList<>();

		List<CaseFile> caseFileList = caseFileService.findFileListByCase(caseId);
		if(caseFileList != null && caseFileList.size() > 0){
			CaseFileDirectory caseFileDirectory = new CaseFileDirectory();
			caseFileDirectory.setLawCase(new Case(caseId));
			List<CaseFileDirectory> list = this.findList(caseFileDirectory);
			if(list != null && list.size() > 0){
				Map<String, String> dirMap = list.stream().collect(
						Collectors.toMap(CaseFileDirectory::getId, CaseFileDirectory::getName, (t1, t2) -> t2)
				);
				for (CaseFile caseFile : caseFileList) {
					if(caseFile.getFileDirectory().getParentIdsArr().length > 0){
						PackToZipUtils.PackDTO pack = new PackToZipUtils.PackDTO();
						List<String> dirList = new ArrayList<>();
						for (String dirId : caseFile.getFileDirectory().getParentIdsArr()) {
							String dirName = dirMap.get(dirId);
							if(StringUtils.isNotBlank(dirName)){
								dirList.add(dirName);
							}
						}
						pack.setDirName(StringUtils.join(dirList,"/"));
						pack.setFileName(caseFile.getName());
						pack.setPath(caseFile.getPath());
						packList.add(pack);
					}
				}
			}
		}

		return packList;
	}

	/**
	 * 创建 案件 根目录
	 * @param lawCaseId
	 */
	@Transactional(readOnly = false)
	public CaseFileDirectory createRoot(String lawCaseId) {
		CaseFileDirectory caseFileDirectory = new CaseFileDirectory();
		caseFileDirectory.setLawCase(new Case(lawCaseId));
		caseFileDirectory.setName("/");
		super.save(caseFileDirectory);
		return caseFileDirectory;
	}

	/**
	 * 根据案件id 获取根目录
	 * 若不存在 则进行创建
	 * @param lawCaseId
	 */
	@Transactional(readOnly = false)
	public CaseFileDirectory getRootByCase(String lawCaseId) {
		CaseFileDirectory queryDir = new CaseFileDirectory();
		queryDir.setLawCase(new Case(lawCaseId));

		CaseFileDirectory fileDirectory = mapper.getRootByCase(queryDir);
		if (fileDirectory == null || StringUtils.isBlank(fileDirectory.getId())) {
			fileDirectory = this.createRoot(lawCaseId);
		}
		return fileDirectory;
	}


	@Transactional(readOnly = false)
	public void save(CaseFileDirectory caseFileDirectory) {
		super.save(caseFileDirectory);
	}

	/**
	 * 根据文件、目录信息 保存文件记录，并根据文件名称创建目录结构
	 * @param file
	 * @param parentDir
	 * @throws Exception
	 */
	@Transactional(readOnly = false)
	public void saveInfo(MultipartFile file, CaseFileDirectory parentDir) throws Exception{
		String originalFilename = file.getOriginalFilename();
		String fileName = originalFilename;
		String[] dirArr = new String[]{};
		if(originalFilename.contains("/")){
			int index = originalFilename.lastIndexOf("/");
			dirArr = originalFilename.substring(0, index).split("/");
			fileName = originalFilename.substring( index + 1 );
		}
		/* 文件上传 */
		AjaxJson j = FileKit.fileUpload(file, CaseFile.FILE_PATH, true, fileName);
		if(!j.isSuccess()){
			throw new RuntimeException(j.getMsg());
		}

		// 创建目录 循环创建，先查询存不存在，存在直接取用，不存在进行创建
		if(dirArr.length > 0){
			for (String dirPath : dirArr) {
				CaseFileDirectory queryDir = new CaseFileDirectory();
				queryDir.setParent(parentDir);
				queryDir.setQueryAccurateName(dirPath);
				List<CaseFileDirectory> list = this.findList(queryDir);
				if(list != null && list.size() > 0){
					queryDir = list.get(0);
				}else {
					queryDir.setName(dirPath);
					queryDir.setLawCase(parentDir.getLawCase());
					this.save(queryDir);
				}
				queryDir.setQueryAccurateName("");
				parentDir = queryDir;
			}
		}
		// 保存文件记录
		CaseFile caseFile = new CaseFile();
		caseFile.setFileDirectory(parentDir);
		caseFile.setPath( j.get("url").toString());
		caseFile.setName( String.valueOf(j.get("name")) );
		caseFileService.save(caseFile);//保存
	}

	/**
	 * 先删除 文件记录 再删除文件目录
	 * @param caseFileDirectory
	 */
	@Transactional(readOnly = false)
	public void delete(CaseFileDirectory caseFileDirectory) {
		CaseFile caseFile = new CaseFile();
		caseFile.setFileDirectory(caseFileDirectory);
		List<CaseFile> fileList = caseFileService.findList(caseFile);
		for (CaseFile file : fileList) {
			caseFileService.delete(file);
		}
		// 删除目录
		super.delete(caseFileDirectory);
	}

}
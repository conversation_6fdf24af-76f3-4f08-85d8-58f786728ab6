package com.jeeplus.modules.sys.utils;

import com.getui.push.v2.sdk.ApiHelper;
import com.getui.push.v2.sdk.GtApiConfiguration;
import com.getui.push.v2.sdk.api.PushApi;
import com.getui.push.v2.sdk.common.ApiResult;
import com.getui.push.v2.sdk.dto.req.Audience;
import com.getui.push.v2.sdk.dto.req.Settings;
import com.getui.push.v2.sdk.dto.req.Strategy;
import com.getui.push.v2.sdk.dto.req.message.PushBatchDTO;
import com.getui.push.v2.sdk.dto.req.message.PushChannel;
import com.getui.push.v2.sdk.dto.req.message.PushDTO;
import com.getui.push.v2.sdk.dto.req.message.PushMessage;
import com.getui.push.v2.sdk.dto.req.message.android.AndroidDTO;
import com.getui.push.v2.sdk.dto.req.message.android.GTNotification;
import com.getui.push.v2.sdk.dto.req.message.android.ThirdNotification;
import com.getui.push.v2.sdk.dto.req.message.android.Ups;
import com.getui.push.v2.sdk.dto.req.message.ios.Alert;
import com.getui.push.v2.sdk.dto.req.message.ios.Aps;
import com.getui.push.v2.sdk.dto.req.message.ios.IosDTO;
import com.google.common.collect.Maps;
import com.jeeplus.common.utils.CacheUtils;
import com.jeeplus.common.utils.StringUtils;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * uni-个推 推送工具类
 *
 * <AUTHOR>
 * @date 2022/03/03
 */
public class GeTuiPushUtils {

    private static String APP_ID = "1fPRm9fdXh7ZkmW2ytcjA1";
    private static String APP_SECRET = "sro9TpPNte758hNZdXAqy9";
    private static String APP_KEY = "XBsJFCjqcs7AsGDI1XDCcA";
    private static String MASTER_SECRET = "mlnLiPcqnQAgfJwnJ78DV";
    private static String APP_PACKAGE_NAME = "l.g.y";    // app包名

    /** 推送 用户clientId 存放集合。。 一个clientId 对应一个APP端一个用户账号 */
    private static final String CACHE_PUST_CLIENT_MAP = "pushClientMap";
    /** 推送对象 用于消息推送 */
    private static PushApi push = null;

    /** 添加clientId 缓存信息 */
    public static void addClient(String loginName, String clientId){
        if(StringUtils.isBlank(loginName) || StringUtils.isBlank(clientId)){
            return;
        }
        Map<String, String> pushClientMap = getPushClientMap();
        String ln = pushClientMap.get(clientId);
        if (StringUtils.isBlank(ln) || !loginName.equals(ln)){
            pushClientMap.put(clientId, loginName);
            CacheUtils.put(CACHE_PUST_CLIENT_MAP, pushClientMap);
        }
    }

    /** 移除clientId缓存信息 */
    public static void removeClient(String clientId){
        if(StringUtils.isBlank(clientId)){
            return;
        }
        Map<String, String> pushClientMap = getPushClientMap();
        if(StringUtils.isNotBlank(pushClientMap.get(clientId))){
            pushClientMap.remove(clientId);
            CacheUtils.put(CACHE_PUST_CLIENT_MAP, pushClientMap);
        }
    }

    /**
     * 获取用户-clientId 对应信息。同一用户可能在多个APP端
     * @return
     */
    public static Map<String, List<String>> getUserClientMap(){
        Map<String, String> pushClientMap = getPushClientMap();
        // 调转 以缓存的用户登录名为key   clientId为value值。。同一个loginName用户可能在多个APP端（clientId）
        Map<String, List<String>> userClientMap = Maps.newHashMap();
        for (String key : pushClientMap.keySet()) {
            String val = pushClientMap.get(key);
            List<String> loginNameList = userClientMap.computeIfAbsent(val, k -> new ArrayList<>());
            loginNameList.add(key);
        }
        return userClientMap;
    }

    /**
     * 通过 clientId 单推
     * @param pushParam
     * @return
     */
    public static ApiResult pushToSingleByCid(PushParam pushParam){
        // 创建推送对象
        PushApi pushApi = getPushApi();
        // 设置接收对象
        List<String> clientIdArr = pushParam.getClientIdArr();
        if(clientIdArr == null || clientIdArr.size() > 1){
            throw new RuntimeException("clientIdArr不能为空或者clientIdArr数组只能填一个cid");
        }
        Audience audience = new Audience();
        audience.setCid(clientIdArr);
        PushDTO<Audience> pushDTO = createPushDTO(audience, pushParam.getTitle(), pushParam.getContent());
        @SuppressWarnings("unchecked")
        ApiResult<Map<String, Map<String, String>>> apiResult = pushApi.pushToSingleByCid(pushDTO);
        return apiResult;
    }

    /**
     * 执行 clientId 批量单推
     * @param pushParamList
     * @return
     */
    public static ApiResult pushBatchByCid(List<PushParam> pushParamList){
        if(pushParamList.size() == 0){
            return new ApiResult();
        }
        // 创建推送对象
        PushApi pushApi = getPushApi();
        // 设置接收对象
        List<PushDTO<Audience>> msgList = new ArrayList<>();
        for (PushParam pushParam : pushParamList) {
            List<PushDTO<Audience>> pushList = createPushDTOByClientId(pushParam.getClientIdArr(), pushParam.getTitle(), pushParam.getContent());
            if(pushList.size() > 0){
                msgList.addAll(pushList);
            }
        }

        PushBatchDTO pushBatchDTO = new PushBatchDTO();
        // 是否异步推送，true是异步，false同步。异步推送不会返回data详情
        pushBatchDTO.setAsync(false);
        pushBatchDTO.setMsgList(msgList);
        @SuppressWarnings("unchecked")
        ApiResult<Map<String, Map<String, String>>> apiResult = pushApi.pushBatchByCid(pushBatchDTO);
        return apiResult;
    }

    /**
     * 通过别名 进行单推
     * @param pushParam
     * @return
     */
    public static ApiResult pushToSingleByAlias(PushParam pushParam){
        List<String> aliasArr = pushParam.getAliasArr();
        if(aliasArr == null || aliasArr.size() > 1){
            throw new RuntimeException("aliasArr不能为空或者aliasArr数组只能填一个alias");
        }
        // 创建推送对象
        PushApi pushApi = getPushApi();
        Audience audience = new Audience();
        audience.setAlias(pushParam.getAliasArr());
        PushDTO pushDTO = createPushDTO(audience, pushParam.getTitle(), pushParam.getContent());
        @SuppressWarnings("unchecked")
        ApiResult<Map<String, Map<String, String>>> apiResult = pushApi.pushToSingleByAlias(pushDTO);
        return apiResult;
    }

    /**
     * 执行 别名 Alias 批量单推
     * @param pushParamList
     * @return
     */
    public static ApiResult pushBatchByAlias(List<PushParam> pushParamList){
        if(pushParamList.size() == 0){
            return new ApiResult();
        }
        // 创建推送对象
        PushApi pushApi = getPushApi();
        // 创建接收对象
        List<PushDTO<Audience>> msgList = new ArrayList<>();
        for (PushParam pushParam : pushParamList) {
            List<PushDTO<Audience>> pushList = createPushDTOByAlias(pushParam.getAliasArr(), pushParam.getTitle(), pushParam.getContent());
            if(pushList.size() > 0){
                msgList.addAll(pushList);
            }
        }

        PushBatchDTO pushBatchDTO = new PushBatchDTO();
        // 是否异步推送，true是异步，false同步。异步推送不会返回data详情
        pushBatchDTO.setAsync(false);
        pushBatchDTO.setMsgList(msgList);
        @SuppressWarnings("unchecked")
        ApiResult<Map<String, Map<String, String>>> apiResult = pushApi.pushBatchByAlias(pushBatchDTO);
        return apiResult;
    }


    /**
     * 获取 用户-clientId 缓存Map
     * @return
     */
    @SuppressWarnings("unchecked")
    private static Map<String, String> getPushClientMap(){
        Map<String, String> pushClientMap = (Map<String, String>) CacheUtils.get(CACHE_PUST_CLIENT_MAP);
        if(pushClientMap == null){
            pushClientMap = Maps.newHashMap();
        }
        return pushClientMap;
    }


    /** 获取推送对象信息 */
    private static PushApi getPushApi(){
        if(push == null){
            // 设置应用配置信息
            GtApiConfiguration apiConfiguration = new GtApiConfiguration();
            apiConfiguration.setAppId(APP_ID);
            apiConfiguration.setAppKey(APP_KEY);
            apiConfiguration.setMasterSecret(MASTER_SECRET);
//            apiConfiguration.setDomain("https://restapi.getui.com/v2/");
            // 实例化ApiHelper对象，用于创建接口对象。目前有PushApi、StatisticApi、UserApi
            ApiHelper apiHelper = ApiHelper.build(apiConfiguration);
            // 创建对象，建议复用。目前有PushApi、StatisticApi、UserApi
            push = apiHelper.creatApi(PushApi.class);
        }
        if(push == null){
            throw new RuntimeException("推送配置异常");
        }
        return push;
    }

    /**
     * 处理 clientId数组问题。。Audience -- cid数组，只能填一个cid
     * @param clientIdList
     * @param title
     * @param content
     * @return
     */
    private static List<PushDTO<Audience>> createPushDTOByClientId(List<String> clientIdList, String title, String content){
        List<PushDTO<Audience>> msgList = new ArrayList<>();
        for (String clientId : clientIdList) {
            Audience audience = new Audience();
            audience.setCid(Arrays.asList(clientId));
            msgList.add( createPushDTO(audience, title, content) );
        }
        return  msgList;
    }
    /**
     * 处理 alias数组问题。 Audience -- alias别名数组，只能填一个别名
     * @param aliasList
     * @param title
     * @param content
     * @return
     */
    private static List<PushDTO<Audience>> createPushDTOByAlias(List<String> aliasList, String title, String content){
        List<PushDTO<Audience>> msgList = new ArrayList<>();
        for (String alias : aliasList) {
            Audience audience = new Audience();
            audience.setAlias(Arrays.asList(alias));
            msgList.add( createPushDTO(audience, title, content) );
        }
        return  msgList;
    }
    /**
     * 创建 推送消息DTO
     * audience -- cid 数组，只能填一个cid
     * audience -- alias别名数组，只能填一个别名
     */
    private static PushDTO<Audience> createPushDTO(Audience audience, String title, String content){
        //根据cid进行单推
        PushDTO<Audience> pushDTO = new PushDTO<Audience>();

        // 设置推送参数 requestid需要每次变化唯一
        pushDTO.setRequestId(String.valueOf(System.currentTimeMillis()));
        //配置推送条件
        // 1: 表示该消息在用户在线时推送个推通道，用户离线时推送厂商通道;
        // 2: 表示该消息只通过厂商通道策略下发，不考虑用户是否在线;
        // 3: 表示该消息只通过个推通道下发，不考虑用户是否在线；
        // 4: 表示该消息优先从厂商通道下发，若消息内容在厂商通道代发失败后会从个推通道下发。
        Strategy strategy=new Strategy();
        strategy.setDef(3);
        Settings settings=new Settings();
        settings.setStrategy(strategy);
        //消息有效期，走厂商消息需要设置该值
        settings.setTtl(36000000);
        pushDTO.setSettings(settings);

        // 设置厂商 离线通知
//        PushChannel pushChannel = new PushChannel();
//        //推送苹果离线通知标题内容
//        Alert alert=new Alert();
//        alert.setTitle(title);
//        alert.setBody(content);
//        Aps aps = new Aps();
//        /*
//            1表示静默推送(无通知栏消息)，静默推送时不需要填写其他参数。
//            苹果建议1小时最多推送3条静默消息
//        */
//        aps.setContentAvailable(0);
//        aps.setSound("default");
//        aps.setAlert(alert);
//        IosDTO iosDTO = new IosDTO();
//        iosDTO.setAps(aps);
//        iosDTO.setType("notify");
//        pushChannel.setIos(iosDTO);
//        // 安卓离线厂商通道推送消息体
//        AndroidDTO androidDTO = new AndroidDTO();
//        ThirdNotification notification1 = new ThirdNotification();;
//        notification1.setTitle(title); // 安卓离线展示的标题
//        notification1.setBody(content); // 安卓离线展示的内容
//        notification1.setClickType("intent");
//        notification1.setIntent("intent:#Intent;launchFlags=0x04000000;action=android.intent.action.oppopush;component="+ APP_PACKAGE_NAME +"/io.dcloud.PandoraEntry;S.UP-OL-SU=true;S.title="+ title +";S.content="+ content +";S.payload=test;end");
//        Ups ups = new Ups();
//        ups.setNotification(notification1);
//        //各厂商自有功能单项设置
////        ups.addOption("HW", "/message/android/notification/badge/class", "io.dcloud.PandoraEntry ");
////        ups.addOption("HW", "/message/android/notification/badge/add_num", 1);
////        ups.addOption("HW", "/message/android/notification/importance", "HIGH");
////        ups.addOption("VV","classification",1);
//        androidDTO.setUps(ups);
//        pushChannel.setAndroid(androidDTO);
//        // 放入 推送消息体
//        pushDTO.setPushChannel(pushChannel);

        // PushMessage在线走个推通道才会起作用的消息体
        PushMessage pushMessage = new PushMessage();
//        Map<String, String> messageMap = Maps.newHashMap();
//        messageMap.put("title", title);
//        messageMap.put("content", content);
//        messageMap.put("payload", "payload");
//        pushMessage.setTransmission(JSON.toJSONString(messageMap));
        GTNotification notification = new GTNotification();
        notification.setTitle(title);
        notification.setBody(content);
        notification.setChannelLevel("3");
        /*  ClickType点击通知后续动作。。包含类型有
            intent：打开应用内特定页面，为此类型时需设置intent参数值
            url：打开网页地址，为此类型时需设置url参数值
            payload：自定义消息内容启动应用，为此类型时需设置payload参数值
            payload_custom：自定义消息内容不启动应用，为此类型时需设置payload参数值
            startapp：打开应用首页，
            none：纯通知，无后续动作
         */
        notification.setClickType( "startapp" );
//        notification.setUrl("https://www.baidu.com");
        pushMessage.setNotification(notification);
        pushDTO.setPushMessage(pushMessage);
        // 设置接收人信息
        pushDTO.setAudience(audience);

        return pushDTO;
    }

    /**
     * 推送参数
     */
    @Data
    public static class PushParam implements Serializable {

        private static final long serialVersionUID = 3L;

        // 登录名
        private String loginName;
        // 推送 clientId 数组
        private List<String> clientIdArr;
        // 推送 alias 数组
        private List<String> aliasArr;
        // 推送标题
        private String title;
        // 推送内容
        private String content;

        public PushParam(){}

    }



    public static void main(String[] args) {
        String clientId = "6de2830ceeea6ecbbd9af359859dee39";

        PushParam pushParam = new PushParam();
        pushParam.setClientIdArr(Arrays.asList(clientId));
        pushParam.setTitle("待办消息提醒");
        pushParam.setContent("您有新66的待办事项需要处理。");

        ApiResult apiResult = pushToSingleByCid(pushParam);
        if (apiResult.isSuccess()) {
            // success
            System.out.println(apiResult.getData());
        } else {
            // failed
            System.out.println("code:" + apiResult.getCode() + ", msg: " + apiResult.getMsg());
        }
    }


}

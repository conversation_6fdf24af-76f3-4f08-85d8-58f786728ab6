<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jeeplus.modules.sys.mapper.UserMapper">

	<resultMap id="userResult" type="User">
		<id property="id" column="id" />
		<result property="company.id" column="company.id" />
		<result property="loginName" column="loginName" />
		<result property="password" column="password" />
		<result property="no" column="no" />
		<result property="name" column="name" />
		<result property="email" column="email" />
		<result property="phone" column="phone" />
		<result property="mobile" column="mobile" />
		<result property="photo" column="photo"/>
		<result property="sign" column="sign" />
		<result property="remarks" column="remarks" />
		<result property="loginFlag" column="login_flag"/>
		<result property="type" column="type" />
		<result property="company.name" column="company.name" />
		<result property="office.id" column="office.id" />
		<result property="office.name" column="office.name" />
		<result property="post.id" column="post.id" />
		<result property="post.name" column="post.name" />
		<result property="office.parentIds" column="office.parentIds" />
		<collection property="roleList" ofType="Role">
			<id property="id" column="role.id" />
			<result property="name" column="role.name"/>
			<result property="enname" column="role.enname"/>
		</collection>
	</resultMap>

	<sql id="userColumns">
    	a.id,
    	a.company_id AS "company.id",
    	a.office_id AS "office.id",
        a.post_id AS "post.id",
    	a.login_name AS "loginName",
    	a.password,
    	a.no,
		a.name,
		a.email,
		a.phone,
		a.mobile,
		a.login_ip,
		a.login_date,
		a.remarks,
		a.login_flag,
		a.photo,
		a.qrcode,
		a.sign,
		a.create_by AS "createBy.id",
		a.create_date,
		a.update_by AS "updateBy.id",
		a.update_date,
		a.del_flag,
		a.type,
    	c.name AS "company.name",
    	c.parent_id AS "company.parent.id",
    	c.parent_ids AS "company.parentIds",
    	o.name AS "office.name",
    	o.parent_id AS "office.parent.id",
    	o.parent_ids AS "office.parentIds",
        p.name AS "post.name"
    </sql>

    <sql id="userJoins">
		LEFT JOIN sys_office c ON c.id = a.company_id
		LEFT JOIN sys_office o ON o.id = a.office_id
        LEFT JOIN sys_post p ON p.id = a.post_id
    </sql>



	<!-- 根据编号获得用户 -->
	<select id="get" resultMap="userResult">
		SELECT
			<include refid="userColumns"/>,
			r.id AS "role.id",
			r.name AS "role.name",
			r.enname AS "role.enname"
		FROM sys_user a
		<include refid="userJoins"/>
		LEFT JOIN sys_user_role ur ON ur.user_id = a.id
		LEFT JOIN sys_role r ON r.id = ur.role_id
		WHERE a.id = #{id}
	</select>

	<!-- 根据登录名查询用户 -->
	<select id="getByLoginName" resultMap="userResult" parameterType="User">
		SELECT
			<include refid="userColumns"/>,
			r.id AS "role.id",
			r.name AS "role.name",
			r.enname AS "role.enname"
		FROM sys_user a
		<include refid="userJoins"/>
		LEFT JOIN sys_user_role ur ON ur.user_id = a.id
		LEFT JOIN sys_role r ON r.id = ur.role_id
		WHERE a.login_name = #{loginName} AND a.del_flag = #{DEL_FLAG_NORMAL}
	</select>

	<!-- 分页查询用户信息 -->
	<select id="findList" resultMap="userResult">
		SELECT
			<include refid="userColumns"/>
			<if test="role != null and role.id != null and role.id != ''">
				,
				r.id AS "role.id",
				r.name AS "role.name",
				r.enname AS "role.enname"
			</if>
		FROM sys_user a
		<include refid="userJoins"/>
		<if test="role != null and role.id != null and role.id != ''">
			LEFT JOIN sys_user_role ur ON ur.user_id = a.id
			LEFT JOIN sys_role r ON r.id = ur.role_id
		</if>
		WHERE a.del_flag = #{DEL_FLAG_NORMAL}
		<if test="role != null and role.id != null and role.id != ''">
		   AND ur.role_id = #{role.id}
		</if>
		<if test="company != null and company.id != null and company.id != ''">
			AND (c.id = #{company.id} OR c.parent_ids LIKE
					<if test="_databaseId == 'postgre'">'%,'||#{company.id}||',%')</if>
					<if test="_databaseId == 'oracle'">'%,'||#{company.id}||',%')</if>
					<if test="_databaseId == 'mysql'">CONCAT('%,', #{company.id}, ',%'))</if>
					<if test="_databaseId == 'mssql'">'%'+#{company.id}+'%')</if>
		</if>
		<if test="office != null and office.id != null and office.id != ''">
			AND (o.id = #{office.id} OR o.parent_ids LIKE
					<if test="_databaseId == 'postgre'">'%,'||#{office.id}||',%')</if>
					<if test="_databaseId == 'oracle'">'%,'||#{office.id}||',%')</if>
					<if test="_databaseId == 'mysql'">CONCAT('%,', #{office.id}, ',%'))</if>
					<if test="_databaseId == 'mssql'">'%'+#{office.id}+'%')</if>
		</if>
		<if test="post != null and post.id != null and post.id != ''">
			AND a.post_id = #{post.id}
		</if>
		<!-- 如果不是超级管理员，则不显示超级管理员用户 -->
		<if test="!currentUser.admin">
			AND a.id != '1'
		</if>
		<if test="loginName != null and loginName != ''">
			AND a.login_name like
					<if test="_databaseId == 'postgre'">'%'||#{loginName}||'%'</if>
					<if test="_databaseId == 'oracle'">'%'||#{loginName}||'%'</if>
					<if test="_databaseId == 'mysql'">CONCAT('%', #{loginName}, '%')</if>
					<if test="_databaseId == 'mssql'">'%'+#{loginName}+'%'</if>
		</if>
		<if test="name != null and name != ''">
			AND a.name like
					<if test="_databaseId == 'postgre'">'%'||#{name}||'%'</if>
					<if test="_databaseId == 'oracle'">'%'||#{name}||'%'</if>
					<if test="_databaseId == 'mysql'">CONCAT('%', #{name}, '%')</if>
					<if test="_databaseId == 'mssql'">'%'+#{name}+'%'</if>
		</if>
		<!-- 数据范围过滤 -->
		${dataScope}
		<choose>
			<when test="page !=null and page.orderBy != null and page.orderBy != ''">
				ORDER BY ${page.orderBy}
			</when>
			<otherwise>
				ORDER BY c.code, o.code, a.name
			</otherwise>
		</choose>
	</select>

	<select id="findBriefList" resultType="User">
		SELECT a.id, a.login_name AS "loginName", a.no, a.name, a.mobile, a.photo, a.type
		FROM sys_user a
		WHERE a.del_flag = #{DEL_FLAG_NORMAL}
		<!-- 如果不是超级管理员，则不显示超级管理员用户 -->
		<if test="!currentUser.admin">
			AND a.id != '1'
		</if>
		<if test="loginName != null and loginName != ''">
			AND a.login_name like
			<if test="_databaseId == 'postgre'">'%'||#{loginName}||'%'</if>
			<if test="_databaseId == 'oracle'">'%'||#{loginName}||'%'</if>
			<if test="_databaseId == 'mysql'">CONCAT('%', #{loginName}, '%')</if>
			<if test="_databaseId == 'mssql'">'%'+#{loginName}+'%'</if>
		</if>
		<if test="name != null and name != ''">
			AND a.name like
			<if test="_databaseId == 'postgre'">'%'||#{name}||'%'</if>
			<if test="_databaseId == 'oracle'">'%'||#{name}||'%'</if>
			<if test="_databaseId == 'mysql'">CONCAT('%', #{name}, '%')</if>
			<if test="_databaseId == 'mssql'">'%'+#{name}+'%'</if>
		</if>
		<if test="type != null and type != ''">
			AND FIND_IN_SET(#{type}, a.type)
		</if>
		<!-- 数据范围过滤 -->
		${dataScope}
		<choose>
			<when test="page !=null and page.orderBy != null and page.orderBy != ''">
				ORDER BY ${page.orderBy}
			</when>
			<otherwise>
				ORDER BY a.name
			</otherwise>
		</choose>
	</select>

	<!-- 根据部门查询用户信息 -->
	<select id="findListByOffice" resultMap="userResult">
		SELECT
			<include refid="userColumns"/>
		FROM sys_user a
		<include refid="userJoins"/>

		WHERE a.del_flag = #{DEL_FLAG_NORMAL}
		<if test="company != null and company.id != null and company.id != ''">
			AND c.id = #{company.id}
		</if>
		<if test="office != null and office.id != null and office.id != ''">
			AND o.id = #{office.id}
		</if>
		<if test="office == null">
			AND (o.id = ''  or o.id is null)
		</if>

		<if test="loginName != null and loginName != ''">
			AND a.login_name like
					<if test="_databaseId == 'postgre'">'%'||#{loginName}||'%'</if>
					<if test="_databaseId == 'oracle'">'%'||#{loginName}||'%'</if>
					<if test="_databaseId == 'mysql'">CONCAT('%', #{loginName}, '%')</if>
					<if test="_databaseId == 'mssql'">'%'+#{loginName}+'%'</if>
		</if>
		<if test="name != null and name != ''">
			AND a.name like
					<if test="_databaseId == 'postgre'">'%'||#{name}||'%'</if>
					<if test="_databaseId == 'oracle'">'%'||#{name}||'%'</if>
					<if test="_databaseId == 'mysql'">CONCAT('%', #{name}, '%')</if>
					<if test="_databaseId == 'mssql'">'%'+#{name}+'%'</if>
		</if>
		<!-- 数据范围过滤 -->
		${dataScope}
		<!-- 排序 -->
		ORDER BY  a.name

	</select>

	<!-- 根据OfficeId获取用户（树查询用户时用） -->
	<select id="findUserByOfficeId" resultMap="userResult" useCache="true">
		SELECT
			a.id, a.name, a.login_name
		FROM sys_user a
		WHERE a.del_flag = #{DEL_FLAG_NORMAL}
			AND a.office_id = #{office.id}
		ORDER BY a.name
	</select>

	<!-- 查询全部用户 -->
	<select id="findAllList" resultMap="userResult">
		SELECT
			<include refid="userColumns"/>
		FROM sys_user a
		<include refid="userJoins"/>
		WHERE a.del_flag = #{DEL_FLAG_NORMAL}
		ORDER BY c.code, o.code, a.name
	</select>

	<!-- 查询全部用户数目 -->
	<select id="findAllCount" resultType="long">
		SELECT
			COUNT(1)
		FROM sys_user a
		WHERE a.del_flag = #{DEL_FLAG_NORMAL}
	</select>

	<!-- 插入用户 -->
	<insert id="insert">
		INSERT INTO sys_user(
			id,
			company_id,
			office_id,
		    post_id,
			login_name,
			password,
			`no`,
			`name`,
			email,
			phone,
			mobile,
			create_by,
			create_date,
			update_by,
			update_date,
			remarks,
			login_flag,
			photo,
			qrcode,
			del_flag,
			`type`
		) VALUES (
			#{id},
			#{company.id},
			#{office.id},
		    #{post.id},
			#{loginName},
			#{password},
			#{no},
			#{name},
			#{email},
			#{phone},
			#{mobile},
			#{createBy.id},
			#{createDate},
			#{updateBy.id},
			#{updateDate},
			#{remarks},
			#{loginFlag},
			#{photo},
			#{qrCode},
			#{delFlag},
			#{type}
		)
	</insert>

	<!-- 更新用户 -->
	<update id="update">
		UPDATE sys_user SET
			company_id = #{company.id},
			office_id = #{office.id},
		    post_id = #{post.id},
			login_name = #{loginName},
			password = #{password},
			`no` = #{no},
			`name` = #{name},
			email = #{email},
			phone = #{phone},
			mobile = #{mobile},
			update_by = #{updateBy.id},
			update_date = #{updateDate},
			remarks = #{remarks},
			login_flag = #{loginFlag},
			photo = #{photo},
			qrcode = #{qrCode},
			`type` = #{type}
		WHERE id = #{id}
	</update>

	<!-- 删除用户和角色关联表数据 -->
	<delete id="deleteUserRole">
		DELETE FROM sys_user_role WHERE user_id = #{id}
	</delete>

	<!-- 插入用户和角色关联表数据 -->
	<insert id="insertUserRole">
		INSERT INTO sys_user_role(user_id, role_id)
		<foreach collection="roleList" item="role" separator=" union all ">
			SELECT #{id}, #{role.id}  <if test="_databaseId == 'oracle'"> from dual </if>
		</foreach>
	</insert>

	<!-- 更新用户信息  -->
	<update id="updateUserInfo">
		UPDATE sys_user SET
			`name` = #{name},
			email = #{email},
			phone = #{phone},
			mobile = #{mobile},
			update_by = #{updateBy.id},
			update_date = #{updateDate},
			remarks = #{remarks},
			photo = #{photo},
			qrcode = #{qrCode},
			sign = #{sign}
		WHERE id = #{id}
	</update>

	<!-- 更新用户密码 -->
	<update id="updatePasswordById">
		UPDATE sys_user SET
			password = #{password}
		WHERE id = #{id}
	</update>

	<!-- 更新登录信息，如登录IP、登录时间 -->
	<update id="updateLoginInfo">
		UPDATE sys_user SET
			login_ip = #{loginIp},
			login_Date = #{loginDate}
		WHERE id = #{id}
	</update>

	<!-- 物理删除用户 -->
	<update id="delete">
		DELETE FROM sys_user
		WHERE id = #{id}
	</update>

	<!-- 逻辑删除用户 -->
	<update id="deleteByLogic">
		UPDATE sys_user SET
			del_flag = #{DEL_FLAG_DELETE}
		WHERE id = #{id}
	</update>

	<!-- 根据实体名称和字段名称和字段值获取唯一记录 -->
	<select id="findUniqueByProperty"  resultType="User">
		select * from sys_user where ${propertyName} = #{value}
   </select>
	<!-- 添加好友 -->
	<insert id="insertFriend">
		INSERT INTO sys_user_friend(
			id,
			userId,
			friendId
		) VALUES (
			#{id},
			#{userId},
			#{friendId}
		)
	</insert>

	<!-- 根据用户id和好友id获取唯一记录 -->
	<select id="findFriend" resultType="User">
		SELECT
			*
		FROM sys_user a
		LEFT JOIN sys_user_friend p ON p.userId = a.id
		WHERE p.userId = #{userId} and p.friendId = #{friendId}
	</select>

	<!-- 删除好友 -->
	<select id="deleteFriend">
		DELETE FROM sys_user_friend  WHERE userId = #{userId} and friendId = #{friendId}
	</select>

		<!-- 查询我的好友列表 -->
	<select id="findFriends"  resultMap="userResult">
		SELECT
			<include refid="userColumns"/>
		FROM sys_user a
		<include refid="userJoins"/>
		LEFT JOIN sys_user_friend p ON p.friendId = a.id
		WHERE  p.userId = #{id}
	</select>

		<!-- 根据条件检索用户，添加到好友列表 -->
	<select id="searchUsers"  resultMap="userResult">
		SELECT
			<include refid="userColumns"/>
		FROM sys_user a
	  	<include refid="userJoins"/>
		<if test="name != null and name != ''">
			WHERE  a.name like
					<if test="_databaseId == 'postgre'">'%'||#{name}||'%'</if>
					<if test="_databaseId == 'oracle'">'%'||#{name}||'%'</if>
					<if test="_databaseId == 'mysql'">CONCAT('%', #{name}, '%')</if>
					<if test="_databaseId == 'mssql'">'%'+#{name}+'%'</if>
		</if>
	</select>

</mapper>

<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jeeplus.modules.sys.mapper.MenuMapper">

     <resultMap id="menuResult" type="Menu">
		<id property="id" column="id" />
		<result property="parentIds" column="parentIds" />
		<result property="name" column="name" />
		<result property="href" column="href" />
		<result property="target" column="target" />
		<result property="icon" column="icon" />
		<result property="sort" column="sort" />
		<result property="type" column="type" />
		<result property="isShow" column="isShow" />
		<result property="affix" column="affix" />
		<result property="permission" column="permission" />
	</resultMap>

	<resultMap id="menuChildrenResult" type="Menu">
		<id property="id" column="id" />
		<result property="parentIds" column="parentIds" />
		<result property="name" column="name" />
		<result property="href" column="href" />
		<result property="target" column="target" />
		<result property="icon" column="icon" />
		<result property="sort" column="sort" />
		<result property="type" column="type" />
		<result property="isShow" column="isShow" />
		<result property="affix" column="affix" />
		<result property="permission" column="permission" />
		<association property="hasChildren" column="id" select="hasChildren" />
	</resultMap>


	<resultMap id="menuDataRuleResult" type="Menu">
		<id property="id" column="id" />
		<result property="parentIds" column="parentIds" />
		<result property="name" column="name" />
		<result property="href" column="href" />
		<result property="target" column="target" />
		<result property="icon" column="icon" />
		<result property="sort" column="sort" />
		<result property="type" column="type" />
		<result property="isShow" column="isShow" />
		<result property="affix" column="affix" />
		<result property="permission" column="permission" />
		<collection property="dataRuleList" column="id" javaType="ArrayList"
                ofType="DataRule" select="findDataRuleList"/>
	</resultMap>



	<sql id="menuColumns">
		a.id AS "id",
		a.parent_id AS "parent.id",
		a.parent_ids AS "parentIds",
		a.name,
		a.href,
		a.target,
		a.icon,
		a.sort,
		a.is_show AS "isShow",
		a.affix AS "affix",
		a.menu_type AS "type",
		a.permission,
		a.remarks,
		a.create_by AS "createBy.id",
		a.create_date AS "createDate",
		a.update_by AS "updateBy.id",
		a.update_date AS "updateDate",
		a.del_flag AS "delFlag",
		p.name AS "parent.name"
	</sql>

	<sql id="menuJoins">
		LEFT JOIN sys_menu p ON p.id = a.parent_id
    </sql>

	<select id="get"  resultMap="menuResult">
		SELECT
			<include refid="menuColumns"/>
		FROM sys_menu a
		<include refid="menuJoins"/>
		WHERE a.id = #{id}
	</select>
	<select id="findUniqueByProperty"  resultMap="menuResult">
		select * from sys_menu where ${propertyName} = #{value}
	</select>

	<select id="getChildren" parameterType="String" resultMap="menuChildrenResult">
        select <include refid="menuColumns"/> from sys_menu a <include refid="menuJoins"/> where a.parent_id = #{id} ORDER BY sort
    </select>
    <select id="hasChildren" parameterType="String" resultType="Boolean">
		select case when exists(select 1 from sys_menu where parent_id = #{id}) then 1 else 0 end <if test="_databaseId == 'oracle'"> from dual </if>
    </select>
    <select id="getParent" parameterType="String" resultMap="menuResult">
        select <include refid="menuColumns"/> from sys_menu a <include refid="menuJoins"/> where a.id = #{id}
    </select>
	<select id="findAllList" resultMap="menuResult">
		SELECT
			<include refid="menuColumns"/>
		FROM sys_menu a
		<include refid="menuJoins"/>
		WHERE a.del_flag = #{DEL_FLAG_NORMAL}
		ORDER BY a.sort
	</select>

	<select id="findAllDataRuleList" resultMap="menuDataRuleResult">
		SELECT
			<include refid="menuColumns"/>
		FROM sys_menu a
		<include refid="menuJoins"/>
		WHERE a.del_flag = #{DEL_FLAG_NORMAL} AND ( SELECT COUNT(*) FROM sys_datarule sd WHERE sd.menu_id = a.id ) &gt; 0
		ORDER BY a.sort
	</select>

	<select id="findByParentIdsLike"  resultMap="menuResult">
		SELECT
			a.id,
			a.parent_id AS "parent.id",
			a.parent_ids AS "parentIds"
		FROM sys_menu a
		WHERE a.del_flag = #{DEL_FLAG_NORMAL} AND a.parent_ids LIKE #{parentIds}
		ORDER BY a.sort
	</select>

	<select id="findByUserId"  resultMap="menuResult">
		SELECT DISTINCT
			<include refid="menuColumns"/>
		FROM sys_menu a
		LEFT JOIN sys_menu p ON p.id = a.parent_id
		JOIN sys_role_menu rm ON rm.menu_id = a.id
		JOIN sys_role r ON r.id = rm.role_id AND r.useable='1'
		JOIN sys_user_role ur ON ur.role_id = r.id
		JOIN sys_user u ON u.id = ur.user_id AND u.id = #{userId}
		WHERE a.del_flag = #{DEL_FLAG_NORMAL} AND r.del_flag = #{DEL_FLAG_NORMAL} AND u.del_flag = #{DEL_FLAG_NORMAL}
		ORDER BY a.sort
	</select>

	<select id="findDataRuleList" resultType="DataRule" >
		SELECT
			a.id AS "id",
			a.menu_id AS "menuId",
			a.name AS "name",
			a.t_field AS "field",
			a.t_express AS "express",
			a.t_value AS "value",
			a.sql_segment AS "sqlSegment",
			a.remarks AS "remarks",
			a.del_flag AS "delFlag"
		FROM sys_datarule a
		<where>
			 a.menu_id =#{id}
		</where>
	</select>

	<insert id="insert">
		INSERT INTO sys_menu(
			id,
			parent_id,
			parent_ids,
			name,
			href,
			target,
			icon,
			sort,
			menu_type,
			is_show,
			permission,
			create_by,
			create_date,
			update_by,
			update_date,
			remarks,
			affix,
			del_flag
		) VALUES (
			#{id},
			#{parent.id},
			#{parentIds},
			#{name},
			#{href},
			#{target},
			#{icon},
			#{sort},
			#{type},
			#{isShow},
			#{permission},
			#{createBy.id},
			#{createDate},
			#{updateBy.id},
			#{updateDate},
			#{remarks},
		    #{affix},
			#{delFlag}
		)
	</insert>

	<update id="update">
		UPDATE sys_menu SET
			parent_id = #{parent.id},
			parent_ids = #{parentIds},
			name = #{name},
			href = #{href},
			target = #{target},
			icon = #{icon},
			sort = #{sort},
			menu_type = #{type},
			is_show = #{isShow},
			permission = #{permission},
			update_by = #{updateBy.id},
			update_date = #{updateDate},
			affix = #{affix},
			remarks = #{remarks}
		WHERE id = #{id}
	</update>

	<update id="updateParentIds">
		UPDATE sys_menu SET
			parent_id = #{parent.id},
			parent_ids = #{parentIds}
		WHERE id = #{id}
	</update>

	<update id="updateSort">
		UPDATE sys_menu SET
			sort = #{sort}
		WHERE id = #{id}
	</update>

	<update id="delete">
		DELETE FROM sys_menu
		WHERE id = #{id} OR parent_ids LIKE
					<if test="_databaseId == 'postgre'">'%,'||#{id}||',%'</if>
					<if test="_databaseId == 'oracle'">'%,'||#{id}||',%'</if>
					<if test="_databaseId == 'mysql'">CONCAT('%,', #{id}, ',%')</if>
					<if test="_databaseId == 'mssql'">'%'+#{id}+'%'</if>
	</update>


	<update id="deleteMenuRole">
	   Delete from sys_role_menu where menu_id = #{menu_id}
	</update>


	<update id="deleteMenuDataRule">
		Delete FROM sys_datarule where menu_id = #{menu_id}
	</update>

	<update id="deleteByLogic">
		UPDATE sys_menu SET
			del_flag = #{DEL_FLAG_DELETE}
		WHERE id = #{id} OR parent_ids LIKE
					<if test="_databaseId == 'postgre'">'%,'||#{id}||',%'</if>
					<if test="_databaseId == 'oracle'">'%,'||#{id}||',%'</if>
					<if test="_databaseId == 'mysql'">CONCAT('%,', #{id}, ',%')</if>
					<if test="_databaseId == 'mssql'">'%'+#{id}+'%'</if>
	</update>

</mapper>

package com.jeeplus.modules.sys.security.shiro;

import org.apache.shiro.authc.AuthenticationToken;

public class J<PERSON>TToken implements AuthenticationToken {

    // 密钥
    private String token;

    public JWTToken(String token) {
        this.token = token;
    }

    @Override
    public Object getPrincipal() {
        return token;
    }

    @Override
    public Object getCredentials() {
        return token;
    }
}

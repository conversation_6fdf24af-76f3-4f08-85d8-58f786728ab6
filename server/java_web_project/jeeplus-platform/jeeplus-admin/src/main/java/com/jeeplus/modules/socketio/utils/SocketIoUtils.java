package com.jeeplus.modules.socketio.utils;

import com.corundumstudio.socketio.SocketIOClient;
import com.google.common.collect.Maps;
import com.jeeplus.common.utils.CacheUtils;

import java.util.Collection;
import java.util.Map;

/**
 * SocketIo 工具类
 * <AUTHOR>
 * @date 2021-10-22
 */
public class SocketIoUtils {
    // map 存放socket 连接client
    private static Map<String, SocketIOClient> SOCKET_CLIENT_MAP = Maps.newHashMap();

    public static void put(String key, SocketIOClient client){
        SOCKET_CLIENT_MAP.put(key, client);
    }

    public static SocketIOClient get(String key){
        return SOCKET_CLIENT_MAP.get(key);
    }

    public static void remove(String key){
        SOCKET_CLIENT_MAP.remove(key);
    }

    public static Collection<SocketIOClient> getAllValues(){
        return SOCKET_CLIENT_MAP.values();
    }
}

<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jeeplus.modules.sys.mapper.AreaMapper">
    
    <resultMap id="areaResult" type="Area">
    	<id property="id" column="id" />
		<result property="parentIds" column="parentIds" />
		<result property="name" column="name" />
		<result property="sort" column="sort" />
		<result property="code" column="code" />
		<result property="type" column="type" />
    </resultMap>
    
	<sql id="areaColumns">
		a.id,
		a.parent_id AS "parent.id",
		a.parent_ids,
		a.code,
		a.name,
		a.sort,
		a.type,
		a.remarks,
		a.create_by AS "createBy.id",
		a.create_date,
		a.update_by AS "updateBy.id",
		a.update_date,
		a.del_flag,
		p.name AS "parent.name"
	</sql>
	
	<sql id="areaJoins">
		LEFT JOIN sys_area p ON p.id = a.parent_id
    </sql>
    
	<select id="get" resultType="Area">
		SELECT
			<include refid="areaColumns"/>
		FROM sys_area a
		<include refid="areaJoins"/>
		WHERE a.id = #{id}
	</select>
	
	<select id="getChildren" parameterType="String" resultMap="areaResult">  
        select * from sys_area where parent_id = #{id} ORDER BY sort 
    </select> 
     
	<select id="findList" resultType="Area">
		SELECT
			<include refid="areaColumns"/>
		FROM sys_area a
		<include refid="areaJoins"/>
		WHERE a.del_flag = #{DEL_FLAG_NORMAL}
		<!-- 数据范围过滤 -->
		${dataScope}
		OR a.id = #{currentUser.office.area.id}
		ORDER BY a.code
	</select>
	
	<select id="findAllList" resultType="Area">
		SELECT
			<include refid="areaColumns"/>
		FROM sys_area a
		<include refid="areaJoins"/>
		WHERE a.del_flag = #{DEL_FLAG_NORMAL}
		ORDER BY a.code
	</select>
	
	<select id="findByParentIdsLike" resultType="Area">
		SELECT
			a.id,
			a.parent_id AS "parent.id",
			a.parent_ids
		FROM sys_area a
		WHERE a.del_flag = #{DEL_FLAG_NORMAL} AND a.parent_ids LIKE #{parentIds}
		ORDER BY a.code
	</select>
	
	<insert id="insert">
		INSERT INTO sys_area(
			id, 
			parent_id, 
			parent_ids, 
			code, 
			name, 
			sort,
			type, 
			create_by, 
			create_date, 
			update_by, 
			update_date, 
			remarks, 
			del_flag
		) VALUES (
			#{id}, 
			#{parent.id}, 
			#{parentIds}, 
			#{code}, 
			#{name}, 
			#{sort}, 
			#{type}, 
			#{createBy.id}, 
			#{createDate}, 
			#{updateBy.id}, 
			#{updateDate}, 
			#{remarks}, 
			#{delFlag}
		)
	</insert>
	
	<update id="update">
		UPDATE sys_area SET 
			parent_id = #{parent.id}, 
			parent_ids = #{parentIds}, 
			code = #{code}, 
			name = #{name}, 
			sort = #{sort}, 
			type = #{type}, 
			update_by = #{updateBy.id}, 
			update_date = #{updateDate}, 
			remarks = #{remarks}
		WHERE id = #{id}
	</update>
	
	<update id="updateParentIds">
		UPDATE sys_area SET 
			parent_id = #{parent.id}, 
			parent_ids = #{parentIds}
		WHERE id = #{id}
	</update>
	
	<update id="delete">
		DELETE FROM sys_area 
		WHERE id = #{id} OR parent_ids LIKE 
					<if test="_databaseId == 'postgre'">'%,'||#{id}||',%'</if>
					<if test="_databaseId == 'oracle'">'%,'||#{id}||',%'</if>
					<if test="_databaseId == 'mysql'">CONCAT('%,', #{id}, ',%')</if>
					<if test="_databaseId == 'mssql'">'%'+#{id}+'%'</if>
	</update>
	
	<update id="deleteByLogic">
		UPDATE sys_area SET 
			del_flag = #{DEL_FLAG_DELETE}
		WHERE id = #{id} OR parent_ids LIKE 
					<if test="_databaseId == 'postgre'">'%,'||#{id}||',%'</if>
					<if test="_databaseId == 'oracle'">'%,'||#{id}||',%'</if>
					<if test="_databaseId == 'mysql'">CONCAT('%,', #{id}, ',%')</if>
					<if test="_databaseId == 'mssql'">'%'+#{id}+'%'</if>
	</update>
	
</mapper>
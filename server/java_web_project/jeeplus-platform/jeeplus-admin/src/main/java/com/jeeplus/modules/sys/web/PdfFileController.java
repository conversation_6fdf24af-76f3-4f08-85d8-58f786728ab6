/**
 * Copyright &copy; 2015-2020 <a href="http://www.jeeplus.org/">JeePlus</a> All rights reserved.
 */
package com.jeeplus.modules.sys.web;

import java.io.Closeable;
import java.io.File;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.io.FileUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.jeeplus.core.web.BaseController;
 
@RestController
@RequestMapping("/pdf")
public class PdfFileController extends BaseController {

	  @GetMapping("/look")
    public void download(String  filePath,HttpServletResponse response) {
        File file = new File(filePath);
        try {
            response.setHeader("Content-Disposition", "inline; filename*=UTF-8''" + URLEncoder.encode(file.getName(), "UTF-8"));
	        } catch (UnsupportedEncodingException e) {
	            e.printStackTrace();
	        }
	 
	        ServletOutputStream os = null;
	        try {
	            os = response.getOutputStream();
	            os.write(FileUtils.readFileToByteArray(file));
	            os.flush();
	        } catch (IOException e) {
	            e.printStackTrace();
	        } finally {
	            if (os != null) {
	                close(os);
	            }
	        }
	    }
	 
		private void close(Closeable closeable) {
			if (closeable != null) {
				try {
					closeable.close();
				} catch (IOException e) {
					e.printStackTrace();
				}
			}
		}
 
	 

}

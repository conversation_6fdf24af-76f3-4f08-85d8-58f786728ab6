<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jeeplus.modules.sys.mapper.OfficeMapper">

	<resultMap id="officeResult" type="Office">
    	<id property="id" column="id" />
		<result property="parentIds" column="parent_ids" />
		<result property="name" column="name" />
		<result property="code" column="code" />
		<result property="type" column="type" />
		<result property="grade" column="grade" />
		<result property="address" column="address" />
		<result property="zipCode" column="zip_code" />
		<result property="master" column="master" />
		<result property="phone" column="phone" />
		<result property="fax" column="fax" />
		<result property="email" column="email" />
		<result property="useable" column="useable" />
		<result property="type" column="type" />
		<result property="remarks" column="remarks" />
		<association property="area" column="area_id" select="getArea" />
    </resultMap>



	<sql id="officeColumns">
		a.id,
		a.parent_id AS "parent.id",
		a.parent_ids,
		a.area_id AS "area.id",
		a.code,
		a.name,
		a.sort,
		a.type,
		a.grade,
		a.address,
		a.zip_code,
		a.master,
		a.phone,
		a.fax,
		a.email,
		a.remarks,
		a.create_by AS "createBy.id",
		a.create_date,
		a.update_by AS "updateBy.id",
		a.update_date,
		a.del_flag,
		a.useable AS useable,
		a.primary_person AS "primaryPerson.id",
		a.deputy_person AS "deputyPerson.id",
		p.name AS "parent.name",
		ar.name AS "area.name",
		ar.parent_ids AS "area.parentIds",
		pp.name AS "primaryPerson.name",
		dp.name AS "deputyPerson.name"
	</sql>

	<sql id="officeJoins">
		LEFT JOIN sys_office p ON p.id = a.parent_id
		LEFT JOIN sys_area ar ON ar.id = a.area_id
		LEFT JOIN sys_user pp ON pp.id = a.primary_person
		LEFT JOIN sys_user dp ON dp.id = a.deputy_person
    </sql>

	<select id="getChildren" parameterType="String" resultMap="officeResult">
        select * from sys_office where parent_id = #{id} ORDER BY sort asc
    </select>
    <select id="getArea" parameterType="String" resultType="Area">
        select * from sys_area where id = #{id}
    </select>

	<select id="get" resultType="Office">
		SELECT
			<include refid="officeColumns"/>
		FROM sys_office a
		<include refid="officeJoins"/>
		WHERE a.id = #{id}
	</select>

	<select id="getByCode" resultType="Office">
		SELECT
			<include refid="officeColumns"/>
		FROM sys_office a
		<include refid="officeJoins"/>

		WHERE a.code = #{code}
	</select>
	<select id="findList" resultType="Office">
		SELECT
			<include refid="officeColumns"/>
		FROM sys_office a
		<include refid="officeJoins"/>
		WHERE a.del_flag = #{DEL_FLAG_NORMAL}
		<!-- 数据范围过滤 -->
		${dataScope}
		<if test="name != null and name != ''">
			AND a.name = #{name}
		</if>
		ORDER BY a.sort asc
	</select>

	<select id="findAllList" resultType="Office">
		SELECT
			<include refid="officeColumns"/>
		FROM sys_office a
		<include refid="officeJoins"/>
		WHERE a.del_flag = #{DEL_FLAG_NORMAL}
		<!-- 数据范围过滤 -->
		${dataScope}
		ORDER BY a.sort asc
	</select>

	<select id="findByParentIdsLike" resultType="Office">
		SELECT
			<include refid="officeColumns"/>
		FROM sys_office a
		<include refid="officeJoins"/>
		WHERE a.del_flag = #{DEL_FLAG_NORMAL} AND a.parent_ids LIKE #{parentIds}
		ORDER BY a.sort asc
	</select>

	<insert id="insert">
		INSERT INTO sys_office(
			id,
			parent_id,
			parent_ids,
			area_id,
			code,
			name,
			sort,
			type,
			grade,
			address,
			zip_code,
			master,
			phone,
			fax,
			email,
			create_by,
			create_date,
			update_by,
			update_date,
			remarks,
			del_flag,
			useable,
			primary_person,
			deputy_person
		) VALUES (
			#{id},
			#{parent.id},
			#{parentIds},
			#{area.id},
			#{code},
			#{name},
			#{sort},
			#{type},
			#{grade},
			#{address},
			#{zipCode},
			#{master},
			#{phone},
			#{fax},
			#{email},
			#{createBy.id},
			#{createDate},
			#{updateBy.id},
			#{updateDate},
			#{remarks},
			#{delFlag},
			#{useable},
			#{primaryPerson.id},
			#{deputyPerson.id}
		)
	</insert>

	<update id="update">
		UPDATE sys_office SET
			parent_id = #{parent.id},
			parent_ids = #{parentIds},
			area_id = #{area.id},
			code = #{code},
			name = #{name},
		    sort = #{sort},
			type = #{type},
			grade = #{grade},
			address = #{address},
			zip_code = #{zipCode},
			master = #{master},
			phone = #{phone},
			fax = #{fax},
			email = #{email},
			update_by = #{updateBy.id},
			update_date = #{updateDate},
			remarks = #{remarks},
			useable=#{useable},
			primary_person=#{primaryPerson.id},
			deputy_person=#{deputyPerson.id}
		WHERE id = #{id}
	</update>

	<update id="updateParentIds">
		UPDATE sys_office SET
			parent_id = #{parent.id},
			parent_ids = #{parentIds}
		WHERE id = #{id}
	</update>

	<update id="delete">
		DELETE FROM sys_office
		WHERE id = #{id} OR parent_ids LIKE
					<if test="_databaseId == 'postgre'">'%,'||#{id}||',%'</if>
					<if test="_databaseId == 'oracle'">'%,'||#{id}||',%'</if>
					<if test="_databaseId == 'mysql'">CONCAT('%,', #{id}, ',%')</if>
					<if test="_databaseId == 'mssql'">'%'+#{id}+'%'</if>
	</update>

	<update id="deleteByLogic">
		UPDATE sys_office SET
			del_flag = #{DEL_FLAG_DELETE}
		WHERE id = #{id} OR parent_ids LIKE
					<if test="_databaseId == 'postgre'">'%,'||#{id}||',%'</if>
					<if test="_databaseId == 'oracle'">'%,'||#{id}||',%'</if>
					<if test="_databaseId == 'mysql'">CONCAT('%,', #{id}, ',%')</if>
					<if test="_databaseId == 'mssql'">'%'+#{id}+'%'</if>
	</update>


</mapper>

package com.jeeplus.modules.socketio.utils;

import com.corundumstudio.socketio.SocketConfig;
import com.corundumstudio.socketio.SocketIOServer;
import com.corundumstudio.socketio.annotation.SpringAnnotationScanner;
import com.jeeplus.modules.sys.security.util.JWTUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * socket.io 配置信息
 * <AUTHOR>
 * @date 2021-10-21
 */
@Configuration
public class SocketIoConfig {

    @Value("${socketIo.host}")
    private String host;
    @Value("${socketIo.prot}")
    private Integer prot;
    // 协议升级超时时间（毫秒），默认10秒。HTTP握手升级为ws协议超时时间
    @Value("${socketIo.upgradeTimeout}")
    private Integer upgradeTimeout;
// Ping消息超时时间（毫秒），默认60秒，这个时间间隔内没有接收到心跳消息就会发送超时事件
    @Value("${socketIo.pingTimeout}")
    private Integer pingTimeout;
    // Ping消息间隔（毫秒），默认25秒。客户端向服务器发送一条心跳消息间隔
    @Value("${socketIo.pingInterval}")
    private Integer pingInterval;
    // 设置最大每帧处理数据的长度，防止他人利用大数据来攻击服务器
    @Value("${socketIo.maxFramePayloadLength}")
    private Integer maxFramePayloadLength;
    // 设置http交互最大内容长度
    @Value("${socketIo.maxHttpContentLength}")
    private Integer maxHttpContentLength;
    // socket连接数大小（如只监听一个端口boss线程组为1即可）
    @Value("${socketIo.bossCount}")
    private Integer bossCount;
    @Value("${socketIo.workCount}")
    private Integer workCount;
    @Value("${socketIo.allowCustomRequests}")
    private boolean allowCustomRequests;

    @Bean
    public SocketIOServer socketIoServer(){
        SocketConfig socketConfig = new SocketConfig();
        socketConfig.setTcpNoDelay(true);
        socketConfig.setSoLinger(0);
        com.corundumstudio.socketio.Configuration config = new com.corundumstudio.socketio.Configuration();
        config.setSocketConfig(socketConfig);
        config.setHostname(host);
        config.setPort(prot);
        config.setUpgradeTimeout(upgradeTimeout);
        config.setPingTimeout(pingTimeout);
        config.setPingInterval(pingInterval);

        config.setMaxFramePayloadLength(maxFramePayloadLength);
        config.setMaxHttpContentLength(maxHttpContentLength);
        config.setBossThreads(bossCount);
        config.setWorkerThreads(workCount);
        config.setAllowCustomRequests(allowCustomRequests);
        // 连接认证，这里使用token更合适
//        config.setAuthorizationListener(new AuthorizationListener() {
//            @Override
//            public boolean isAuthorized(HandshakeData data) {
//                // 检验token
//                 String token = data.getSingleUrlParam("token");
//                 String loginName = JWTUtil.getLoginName(token);
//                return true;
//            }
//        });

        return new SocketIOServer(config);
    }

    @Bean
    public SpringAnnotationScanner springAnnotationScanner(SocketIOServer socketServer) {
        return new SpringAnnotationScanner(socketServer);
    }
}

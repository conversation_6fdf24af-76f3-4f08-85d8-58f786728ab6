<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jeeplus.modules.database.datamodel.mapper.DataSetMapper">

    <sql id="dataSetColumns">
        a.id AS "id",
        a.db AS "db",
        a.name AS "name",
        a.sqlcmd AS "sqlcmd",
        a.create_date AS "createDate",
        a.update_date AS "updateDate",
        db.id AS "db.id",
        db.name AS "db.name",
        db.enname AS "db.enName"
    </sql>

    <sql id="dataSetJoins">
        LEFT JOIN plugin_datasource_link db ON db.id = a.db
    </sql>


    <select id="get" resultType="DataSet">
        SELECT
        <include refid="dataSetColumns"/>
        FROM plugin_datasource_model a
        <include refid="dataSetJoins"/>
        WHERE a.id = #{id}
    </select>

    <select id="findList" resultType="DataSet">
        SELECT
        <include refid="dataSetColumns"/>
        FROM plugin_datasource_model a
        <include refid="dataSetJoins"/>
        <where>

            ${dataScope}
            <if test="db !=null and db.id != null and db.id != ''">
                AND a.db = #{db.id}
            </if>
            <if test="name != null and name != ''">
                AND a.name LIKE
                <if test="_databaseId == 'postgre'">'%'||#{name}||'%'</if>
                <if test="_databaseId == 'oracle'">'%'||#{name}||'%'</if>
                <if test="_databaseId == 'mssql'">'%'+#{name}+'%'</if>
                <if test="_databaseId == 'mysql'">concat('%',#{name},'%')</if>
            </if>
        </where>
        <choose>
            <when test="page !=null and page.orderBy != null and page.orderBy != ''">
                ORDER BY ${page.orderBy}
            </when>
            <otherwise>
                ORDER BY a.update_date DESC
            </otherwise>
        </choose>
    </select>

    <select id="findAllList" resultType="DataSet">
        SELECT
        <include refid="dataSetColumns"/>
        FROM plugin_datasource_model a
        <include refid="dataSetJoins"/>
        <where>

            ${dataScope}
        </where>
        <choose>
            <when test="page !=null and page.orderBy != null and page.orderBy != ''">
                ORDER BY ${page.orderBy}
            </when>
            <otherwise>
                ORDER BY a.update_date DESC
            </otherwise>
        </choose>
    </select>

    <insert id="insert">
        INSERT INTO plugin_datasource_model(
        id,
        db,
        name,
        sqlcmd,
        create_date,
        update_date
        ) VALUES (
        #{id},
        #{db.id},
        #{name},
        #{sqlcmd},
        #{createDate},
        #{updateDate}
        )
    </insert>

    <update id="update">
        UPDATE plugin_datasource_model SET
        db = #{db.id},
        name = #{name},
        sqlcmd = #{sqlcmd},
        create_date = #{createDate},
        update_date = #{updateDate}
        WHERE id = #{id}
    </update>


    <!--物理删除-->
    <update id="delete">
        DELETE FROM plugin_datasource_model
        WHERE id = #{id}
    </update>

    <!--逻辑删除-->
    <update id="deleteByLogic">
        UPDATE plugin_datasource_model SET
        del_flag = #{DEL_FLAG_DELETE}
        WHERE id = #{id}
    </update>


    <!-- 根据实体名称和字段名称和字段值获取唯一记录 -->
    <select id="findUniqueByProperty" resultType="DataSet">
        select * FROM plugin_datasource_model where ${propertyName} = #{value}
    </select>

</mapper>

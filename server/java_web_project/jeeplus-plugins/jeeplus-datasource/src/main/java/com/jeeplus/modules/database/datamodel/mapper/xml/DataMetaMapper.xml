<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jeeplus.modules.database.datamodel.mapper.DataMetaMapper">

    <sql id="dataMetaColumns">
        a.id AS "id",
        a.dsid AS "dataSet.id",
        a.name AS "name",
        a.label AS "label",
        a.type AS "type",
        a.isneed AS "isNeed",
        a.sort AS "sort"
    </sql>

    <sql id="dataMetaJoins">

    </sql>


    <select id="get" resultType="DataMeta">
        SELECT
        <include refid="dataMetaColumns"/>
        FROM plugin_datasource_model_meta a
        <include refid="dataMetaJoins"/>
        WHERE a.id = #{id}
    </select>

    <select id="findList" resultType="DataMeta">
        SELECT
        <include refid="dataMetaColumns"/>
        FROM plugin_datasource_model_meta a
        <include refid="dataMetaJoins"/>
        <where>

            ${dataScope}
            <if test="dataSet != null and  dataSet.id != ''">
                AND a.dsId = #{dataSet.id}
            </if>
            <if test="isNeed != null and isNeed != ''">
                AND a.isneed = #{isNeed}
            </if>
            <if test="name != null and name != ''">
                AND a.name LIKE
                <if test="_databaseId == 'postgre'">'%'||#{name}||'%'</if>
                <if test="_databaseId == 'oracle'">'%'||#{name}||'%'</if>
                <if test="_databaseId == 'mssql'">'%'+#{name}+'%'</if>
                <if test="_databaseId == 'mysql'">concat('%',#{name},'%')</if>
            </if>
        </where>
        ORDER BY a.sort asc
    </select>

    <select id="findAllList" resultType="DataMeta">
        SELECT
        <include refid="dataMetaColumns"/>
        FROM plugin_datasource_model_meta a
        <include refid="dataMetaJoins"/>
        <where>

            ${dataScope}
        </where>
        ORDER BY a.sort asc
    </select>

    <insert id="insert">
        INSERT INTO plugin_datasource_model_meta(
        id,
        dsId,
        name,
        label,
        type,
        isneed,
        sort
        ) VALUES (
        #{id},
        #{dataSet.id},
        #{name},
        #{label},
        #{type},
        #{isNeed},
        #{sort}
        )
    </insert>

    <update id="update">
        UPDATE plugin_datasource_model_meta SET
        dsid = #{dataSet.id},
        name = #{name},
        label = #{label},
        type = #{type},
        isneed = #{isNeed}
        WHERE id = #{id}
    </update>


    <!--物理删除-->
    <update id="delete">
        DELETE FROM plugin_datasource_model_meta
        WHERE id = #{id}
    </update>

    <!--物理删除-->
    <update id="deleteByDataSetId" parameterType="String">
        DELETE FROM plugin_datasource_model_meta
        WHERE dsid = #{id}
    </update>

    <!--逻辑删除-->
    <update id="deleteByLogic">
        UPDATE plugin_datasource_model_meta SET
        del_flag = #{DEL_FLAG_DELETE}
        WHERE id = #{id}
    </update>


    <!-- 根据实体名称和字段名称和字段值获取唯一记录 -->
    <select id="findUniqueByProperty" resultType="DataMeta">
        select * FROM plugin_datasource_model_meta where ${propertyName} = #{value}
    </select>

</mapper>
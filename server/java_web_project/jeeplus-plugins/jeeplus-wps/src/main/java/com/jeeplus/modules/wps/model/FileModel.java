package com.jeeplus.modules.wps.model;

public class FileModel {
    public String id;
    public String name;
    public long version;
    public long size;
    public String creator;
    public String modifier;
    public static String download_url;

    public FileModel() {
    }

    public FileModel(String id, String name, long version, long size, String creator, String modifier) {
        this.id = id;
        this.name = name;
        this.version = version;
        this.size = size;
        this.creator = creator;
        this.modifier = modifier;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public long getVersion() {
        return version;
    }

    public void setVersion(long version) {
        this.version = version;
    }

    public long getSize() {
        return size;
    }

    public void setSize(long size) {
        this.size = size;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public String getModifier() {
        return modifier;
    }

    public void setModifier(String modifier) {
        this.modifier = modifier;
    }

    public static String getDownload_url() {
        return download_url;
    }

    public static void setDownload_url(String download_url) {
        FileModel.download_url = download_url;
    }
}

/**
 * Copyright © 2015-2020 <a href="http://www.jeeplus.org/">JeePlus</a> All rights reserved.
 */
package com.jeeplus.modules.office.web;

import java.util.List;

import com.jeeplus.modules.office.entity.DocCategory;
import com.jeeplus.modules.office.service.DocCategoryService;
import io.swagger.annotations.ApiImplicitParam;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.google.common.collect.Lists;
import com.jeeplus.common.json.AjaxJson;
import com.jeeplus.core.web.BaseController;
import com.jeeplus.common.utils.StringUtils;

/**
 * 文书模板Controller
 * <AUTHOR>
 * @version 2020-06-23
 */
@Api(tags ="文书模板")
@RestController
@RequestMapping(value = "/wps/docCategory")
public class DocCategoryController extends BaseController {

	@Autowired
	private DocCategoryService docCategoryService;

	@ModelAttribute
	public DocCategory get(@RequestParam(required=false) String id) {
		DocCategory entity = null;
		if (StringUtils.isNotBlank(id)){
			entity = docCategoryService.get(id);
		}
		if (entity == null){
			entity = new DocCategory();
		}
		return entity;
	}


	/**
	 * 文书模板树表数据
	 */
//	@ApiOperation("获取文书模板树表数据")
	@GetMapping("list")
	public AjaxJson list(DocCategory docCategory) {
		return AjaxJson.success().put("list", docCategoryService.findList(docCategory));
	}

	/**
	 * 根据Id获取文书模板数据
	 */
	@RequiresPermissions("user")
	@ApiOperation(value = "获取文书模板分类数据", consumes = "application/form-data")
	@PostMapping("queryById")
	public AjaxJson queryById(DocCategory docCategory) {
		return AjaxJson.success().put("docCategory", docCategory);
	}

	/**
	 * 保存文书模板
	 */
	@RequiresPermissions("user")
	@ApiOperation(value = "保存文书模板分类", consumes = "application/form-data")
	@PostMapping("save")
	public AjaxJson save(DocCategory docCategory, Model model) throws Exception{
		/**
		 * 后台hibernate-validation插件校验
		 */
		String errMsg = beanValidator(docCategory);
		if (StringUtils.isNotBlank(errMsg)){
			return AjaxJson.error(errMsg);
		}
		//新增或编辑表单保存
		docCategoryService.save(docCategory);//保存
		return AjaxJson.success("保存模板分类成功");
	}

	/**
	 * 删除文书模板
	 */
	@RequiresPermissions("user")
	@ApiOperation(value = "删除文书模板分类", consumes = "application/form-data")
	@ApiImplicitParam(value = "id值", name = "id", required = true)
	@PostMapping("delete")
	public AjaxJson delete(DocCategory docCategory) {
		docCategoryService.delete(docCategory);
		return AjaxJson.success("删除模板分类成功");
	}

	/**
	     * 获取JSON树形数据。
	     * @param extId 排除的ID
	     * @return
	*/
	@RequiresPermissions("user")
	@ApiOperation("获取模版分类JSON树形数据")
	@GetMapping("treeData")
	public AjaxJson treeData(@RequestParam(required = false) String extId) {
		List<DocCategory> list = docCategoryService.findList(new DocCategory());
		List rootTree = getDocCategoryTree(list, extId);
		return AjaxJson.success().put("treeData", rootTree);
	}

	private List<DocCategory> getDocCategoryTree(List<DocCategory> list, String extId) {
		List<DocCategory> docCategorys = Lists.newArrayList();
		List<DocCategory> rootTrees = docCategoryService.getChildren("0");
		for (DocCategory root : rootTrees) {
		    if (StringUtils.isBlank(extId) ||  !extId.equals(root.getId())) {
		        docCategorys.add(getChildOfTree(root, list, extId));
		    }
		}
		return docCategorys;
	}

	private DocCategory getChildOfTree(DocCategory docCategory, List<DocCategory> docCategoryList, String extId) {
		docCategory.setChildren(Lists.newArrayList());
		for (DocCategory child : docCategoryList) {
		    if (StringUtils.isBlank(extId) ||  (!extId.equals(child.getId()) && child.getParentIds().indexOf("," + extId + ",") == -1)) {
		        if (child.getParentId().equals(docCategory.getId())) {
		            docCategory.getChildren().add(getChildOfTree(child, docCategoryList, extId));
		        }
		    }
		}
		return docCategory;
	}



}

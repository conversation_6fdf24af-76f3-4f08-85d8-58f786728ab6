package com.jeeplus.modules.wps.controller;

import java.io.BufferedOutputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.util.Arrays;
import java.util.Calendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.jeeplus.common.utils.FileUtils;
import com.jeeplus.common.utils.StringUtils;
import com.jeeplus.config.properties.FileProperties;
import com.jeeplus.config.properties.JeePlusProperites;
import com.jeeplus.modules.office.entity.DocTemplate;
import com.jeeplus.modules.office.entity.WpsTodoInfoFile;
import com.jeeplus.modules.office.mapper.WpsTodoInfoFileMapper;
import com.jeeplus.modules.office.service.DocTemplateService;
import com.jeeplus.modules.sys.entity.User;
import com.jeeplus.modules.sys.utils.FileKit;
import com.jeeplus.modules.sys.utils.UserUtils;
import com.jeeplus.modules.wps.ApplicationProperties;
import com.jeeplus.modules.wps.model.FileModel;
import com.jeeplus.modules.wps.model.UserModel;
import com.jeeplus.modules.wps.utils.WpsUtils;

@RestController
public class WebOfficeController {
    /* 权限许可 read 预览  write 编辑 */
    public static String permission_read = "read";
    public static String permission_write = "write";

    @Value("${wps.downloadCallbackPath}")
    private String downloadCallbackPath;
    @Autowired
    private DocTemplateService docTemplateService;
    @Autowired
    private WpsTodoInfoFileMapper wpsTodoInfoFileMapper;
    
    
    
    @Autowired
    FileProperties fileProperties;

    // 在线编辑时,wps会根据文件名_w_fname,从这里获取file信息,其中包含了文档的下载url
    @RequestMapping(value = "/v1/3rd/file/info", method = RequestMethod.GET)
    public Object fileInfo(@RequestParam("_w_fname") String filename, String _w_userid, @RequestParam("_w_fileid") String fileid, String _w_operateType) throws Exception {
        System.out.println("Method fileIno(" + filename + ") is invoked");
        JSONObject jsonObject = new JSONObject();
        JSONObject file = new JSONObject();
        JSONObject user = new JSONObject();
        File f = null;
        String file1 = FileKit.getFileDir(filename);
//        String file1 = JeePlusProperites.newInstance().getUserfilesBaseDir() +filename;
        f = new File(file1);

        try {
//            FileModel fileModel = new FileModel();
            file.put("id", fileid);         // 文件id,字符串长度小于40
            file.put("name", f.getName());  //文件名(必须带文件后缀)
            file.put("version", 1);         //当前版本号，必须大于 0，同时位数小于 11
            file.put("size", f.length());   //文件大小，单位为B(文件真实大小，否则会出现异常)
            file.put("creator", _w_userid); //创建者id，字符串长度小于40
            file.put("modifier", _w_userid);    //修改者id，字符串长度小于40
            // 文档下载url
            file.put("download_url", FileModel.download_url +  URLEncoder.encode(filename, "utf-8"));
            // 水印设置
            JSONObject watermark = new JSONObject();
            watermark.put("type", 0);   // 水印类型， 0为无水印； 1为文字水印
            watermark.put("value", "律管云"); // 文字水印的文字，当type为1时此字段必选
//            watermark.put("fillstyle", "rgba( 192, 192, 192, 0.6 )"); //水印的透明度，非必选，有默认值
//            watermark.put("font", "bold 20px Serif");   //水印的字体，非必选，有默认值
//            watermark.put("rotate",  -0.7853982); //水印的旋转度，非必选，有默认值
//            watermark.put("horizontal", 50);    //水印水平间距，非必选，有默认值
//            watermark.put("vertical", 100);     //水印垂直间距，非必选，有默认值

            file.put("watermark", watermark);
            jsonObject.put("file", file);
            UserModel userModel = new UserModel();
            user.put("id", _w_userid);
            user.put("name", UserUtils.getByLoginName(_w_userid).getName ());
            // 权限 预览、编辑
            List<String> perList = Arrays.asList(permission_read, permission_write);
            String permission = (StringUtils.isNotBlank(_w_operateType) && perList.contains(_w_operateType))? _w_operateType : permission_read;
            user.put("permission", permission);
            user.put("avatar_url",  "");
            jsonObject.put("user", user);
//            System.out.println("json信息："+ jsonObject.toString());
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return jsonObject.toString();

    }

    // 文档修改后调用的回调接口
    @RequestMapping(value = "/v1/3rd/file/save", method = RequestMethod.POST)
    public Object save(@RequestParam("file") MultipartFile file, HttpServletRequest request, String _w_userid, @RequestParam("_w_fname") String filename) throws Exception{
        String file1 = JeePlusProperites.newInstance().getUserfilesBaseDir() +filename;

        File f = new File(file1);
        if (!file.isEmpty()) {
            try {
                BufferedOutputStream out = new BufferedOutputStream(
                        new FileOutputStream(f));
                out.write(file.getBytes());
                out.flush();
                out.close();
            } catch (FileNotFoundException e) {
                e.printStackTrace();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }

        JSONObject jsonObject = new JSONObject();
//        FileModel fileModel = new FileModel();
        JSONObject file2 = new JSONObject();
        String name = filename.substring(filename.lastIndexOf("/")+1);
        String path = filename.substring(0,filename.lastIndexOf("/")+1 );
        String fileid = request.getHeader("x-weboffice-file-id");
        file2.put("id", fileid);
        file2.put("name", name);
        file2.put("version", 1);
        file2.put("size", file.getSize());
        file2.put("creator", _w_userid);
        file2.put("modifier",_w_userid);
        // 文档url
        file2.put("download_url", FileModel.download_url + path+ URLEncoder.encode(name, "utf-8"));
        jsonObject.put("file", file2);
 
        System.out.println("====wps保存======fileid:"+fileid);
        WpsTodoInfoFile infoFile= wpsTodoInfoFileMapper.get(fileid);
        if(infoFile!=null) {
        	 System.out.println("====wps保存==修改成功====fileid:"+fileid);
        	 if(infoFile.getEditFlag()==0) {
        			infoFile.setEditFlag(1);
                	wpsTodoInfoFileMapper.update(infoFile);
        	 }
        
        }
     

        return jsonObject.toString();
    }

    // 文档的下载url
    @GetMapping(value="/weboffice/getFile", produces="application/octet-stream;charset=UTF-8")
    public ResponseEntity<byte[]> getFile(@RequestParam("_w_fname") String filename) throws Exception {
        String file1 = FileKit.getFileDir(filename);
//        String file1 = JeePlusProperites.newInstance().getUserfilesBaseDir() +filename;
        System.out.println("Method getFile(" + filename + ") is invoked");
        File file = new File(file1);
        InputStream inputStream = new FileInputStream(file);
        byte[] body = new byte[inputStream.available()];
        HttpHeaders headers = new HttpHeaders();

        headers.add("Content-Disposition", "attachment;filename=" + URLEncoder.encode(file.getName(), "UTF-8"));
        inputStream.read(body);



        return new ResponseEntity(body, headers, HttpStatus.OK);

    }

    public static byte[] readInputStream(InputStream inStream) throws Exception {

        ByteArrayOutputStream outStream = new ByteArrayOutputStream();

        byte[] buffer = new byte[10240];

        int len = 0;

        while ((len = inStream.read(buffer)) != -1) {

            outStream.write(buffer, 0, len);

        }

        inStream.close();

        return outStream.toByteArray();

    }


    @RequestMapping(value = "/v1/3rd/file/version/{version}", method = RequestMethod.GET)
    public Object fileVersionInfo(@PathVariable("version") Long version, String _w_userid, @RequestParam("_w_fname") String filename) {
        JSONObject jsonObject = new JSONObject();
        JSONObject file = new JSONObject();
        JSONObject user = new JSONObject();
        try {
            FileModel fileModel = new FileModel();
            file.put("id", fileModel.id);
            file.put("name", filename);
            file.put("version", fileModel.version);
            file.put("size", fileModel.size);
            file.put("creator", _w_userid);
            file.put("modifier", _w_userid);
            file.put("download_url", filename);
            jsonObject.put("file", file);
            UserModel userModel = new UserModel();
            user.put("id", _w_userid);
            user.put("name", UserUtils.get (_w_userid).getName ());
            user.put("permission", "write");
            user.put("avatar_url", "");
            jsonObject.put("user", user);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return jsonObject.toString();
    }

    @RequestMapping(value = "/v1/3rd/user/info", method = RequestMethod.POST)
    public Object userInfo( String _w_userid) {
        JSONObject jsonObject = new JSONObject();
        JSONArray jsonArray = new JSONArray();
        JSONObject user = new JSONObject();
        try {
            user.put("id", _w_userid);
            user.put("name", UserUtils.getByLoginName (_w_userid).getName ());
            user.put("permission", "write");
            user.put("avatar_url",  "");
            jsonArray.put(user);
            jsonObject.put("users", jsonArray);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return jsonObject.toString();
    }



    /**
     * 新建文件
     */
    @PostMapping("/v1/3rd/file/new")
    public String fileNew(@RequestBody MultipartFile file, String _w_userid, @RequestParam("_w_fname") String filename) throws Exception{
        String uploadPath = DocTemplate.FILE_PATH;
        Calendar cal = Calendar.getInstance();
        int year = cal.get(Calendar.YEAR);
        int month = cal.get(Calendar.MONTH )+1;

        User user = UserUtils.getByLoginName(_w_userid);
        String userId = (user != null ? user.getId() : "1");
        String fileUrl = WpsUtils.getAttachmentUrl(userId)+uploadPath+"/"+year+"/"+month+"/";
        String fileDir = WpsUtils.getAttachmentDir(userId)+uploadPath+"/"+year+"/"+month+"/";
        String url = "";
        String type = "/w";
        // 判断文件是否为空
        if (!file.isEmpty()) {
            String name = file.getOriginalFilename ();
            if (fileProperties.isAvailable (name)) {
                // 文件保存路径
                // 转存文件
                FileUtils.createDirectory (fileDir);
                String filePath = fileDir + name;
                File newFile = FileUtils.getAvailableFile (filePath, 0);
                file.transferTo (newFile);
                url = fileUrl + newFile.getName ();
                filename = newFile.getName ();
            }
        }

        if (filename.endsWith("xls") || filename.endsWith("xlsx")) {
            type = "/s";
        } else if (filename.endsWith("ppt") || filename.endsWith("pptx")) {
            type = "/p";
        } else if (filename.endsWith("pdf")) {
            type = "/f";
        } else {
            type = "/w";
        }

        DocTemplate docTemplate = new DocTemplate ();
        docTemplate.setName (filename.substring(0, filename.lastIndexOf(".")));
        docTemplate.setPath (url);
        docTemplateService.save(docTemplate);//保存

        String fileid = String.valueOf (System.currentTimeMillis());

        String redirectUrl = ApplicationProperties.domain + "/office"+type+"/" + fileid + "?";

        //// TODO: 注意：签名前，参数不要urlencode,要签名以后统一处理url编码，防止签名不过，带中文等字符容易导致签名不过，要注意签名与编成的顺序，最好不要带中文等特殊字符
        Map paramMap= new HashMap<String, String> ();
        paramMap.put("_w_userid", _w_userid);
        paramMap.put("_w_appid", ApplicationProperties.appid);
        paramMap.put ("_w_fname", url);
        paramMap.put ("_w_fileid", fileid);
        paramMap.put("_w_operateType", WebOfficeController.permission_write);   // 编辑权限
        String signature = WpsUtils.getSignature(paramMap, ApplicationProperties.appSecret);
        redirectUrl += WpsUtils.getUrlParam(paramMap) + "&_w_signature=" + signature;

        JSONObject jsonObject = new JSONObject();
        jsonObject.put ("redirect_url", redirectUrl);
        jsonObject.put("user_id", _w_userid);
        return jsonObject.toString ();
    }
    @RequestMapping(value = "/v1/3rd/file/online", method = RequestMethod.POST)
    public void online() {
    }

    @RequestMapping(value = "/v1/3rd/file/history", method = RequestMethod.POST)
    public Object history() {
        JSONObject jsonObject = new JSONObject();
        return jsonObject.toString();
    }

}

<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jeeplus.modules.monitor.mapper.ScheduleJobMapper">

    <sql id="scheduleJobColumns">
        a.id AS "id",
        a.name AS "name",
        a.t_group AS "group",
        a.expression AS "cronExpression",
        a.status AS "status",
        a.is_info AS "isInfo",
        a.classname AS "className",
        a.description AS "description",
        a.create_by AS "createBy.id",
        a.create_date AS "createDate",
        a.update_by AS "updateBy.id",
        a.update_date AS "updateDate",
        a.del_flag AS "delFlag"
    </sql>

    <sql id="scheduleJobJoins">
    </sql>


    <select id="get" resultType="ScheduleJob">
        SELECT
        <include refid="scheduleJobColumns"/>
        FROM sys_schedule a
        <include refid="scheduleJobJoins"/>
        WHERE a.id = #{id}
    </select>

    <select id="findList" resultType="ScheduleJob">
        SELECT
        <include refid="scheduleJobColumns"/>
        FROM sys_schedule a
        <include refid="scheduleJobJoins"/>
        <where>
            a.del_flag = #{DEL_FLAG_NORMAL}
            <if test="name != null and name != ''">
                AND a.name = #{name}
            </if>
        </where>
        <choose>
            <when test="page !=null and page.orderBy != null and page.orderBy != ''">
                ORDER BY ${page.orderBy}
            </when>
            <otherwise>
                ORDER BY a.update_date DESC
            </otherwise>
        </choose>
    </select>

    <select id="findAllList" resultType="ScheduleJob">
        SELECT
        <include refid="scheduleJobColumns"/>
        FROM sys_schedule a
        <include refid="scheduleJobJoins"/>
        <where>
            a.del_flag = #{DEL_FLAG_NORMAL}
        </where>
        <choose>
            <when test="page !=null and page.orderBy != null and page.orderBy != ''">
                ORDER BY ${page.orderBy}
            </when>
            <otherwise>
                ORDER BY a.update_date DESC
            </otherwise>
        </choose>
    </select>

    <insert id="insert">
        INSERT INTO sys_schedule(
        id,
        name,
        t_group,
        expression,
        status,
        is_info,
        classname,
        description,
        create_by,
        create_date,
        update_by,
        update_date,
        del_flag
        ) VALUES (
        #{id},
        #{name},
        #{group},
        #{cronExpression},
        #{status},
        #{isInfo},
        #{className},
        #{description},
        #{createBy.id},
        #{createDate},
        #{updateBy.id},
        #{updateDate},
        #{delFlag}
        )
    </insert>

    <update id="update">
        UPDATE sys_schedule SET
        name = #{name},
        t_group = #{group},
        expression = #{cronExpression},
        status = #{status},
        is_info = #{isInfo},
        classname = #{className},
        description = #{description},
        update_by = #{updateBy.id},
        update_date = #{updateDate}
        WHERE id = #{id}
    </update>


    <!--物理删除-->
    <update id="delete">
        DELETE FROM sys_schedule
        WHERE id = #{id}
    </update>

    <!--逻辑删除-->
    <update id="deleteByLogic">
        UPDATE sys_schedule SET
        del_flag = #{DEL_FLAG_DELETE}
        WHERE id = #{id}
    </update>


    <!-- 根据实体名称和字段名称和字段值获取唯一记录 -->
    <select id="findUniqueByProperty" resultType="ScheduleJob">
        select * FROM sys_schedule where ${propertyName} = #{value}
    </select>

</mapper>

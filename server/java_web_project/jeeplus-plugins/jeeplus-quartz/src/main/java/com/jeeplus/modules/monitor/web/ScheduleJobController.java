/**
 * Copyright &copy; 2015-2020 <a href="http://www.jeeplus.org/">JeePlus</a> All rights reserved.
 */
package com.jeeplus.modules.monitor.web;

import com.jeeplus.common.json.AjaxJson;
import com.jeeplus.common.utils.StringUtils;
import com.jeeplus.core.persistence.Page;
import com.jeeplus.core.web.BaseController;
import com.jeeplus.modules.monitor.entity.ScheduleJob;
import com.jeeplus.modules.monitor.service.ScheduleJobService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.quartz.CronExpression;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 定时任务Controller
 *
 * <AUTHOR>
 * @version 2017-02-04
 */
@RestController
@Api(tags = "定时任务", description = "定时任务")
@RequestMapping("/quartz/scheduleJob")
public class ScheduleJobController extends BaseController {

    @Autowired
    private ScheduleJobService scheduleJobService;

    @ModelAttribute
    public ScheduleJob get(@RequestParam(required = false) String id) {
        ScheduleJob entity = null;
        if (StringUtils.isNotBlank(id)) {
            entity = scheduleJobService.get(id);
        }
        if (entity == null) {
            entity = new ScheduleJob();
        }
        return entity;
    }


    /**
     * 定时任务列表数据
     */
    @ApiOperation(value = "列表数据", consumes = "application/form-data")
    @ApiImplicitParam(value = "任务名称", name = "name")
    @RequiresPermissions(value={"quartz:scheduleJob:list", "user"}, logical = Logical.OR)
    @PostMapping("list")
    public AjaxJson data(ScheduleJob scheduleJob, HttpServletRequest request, HttpServletResponse response, Model model) {
        Page<ScheduleJob> page = scheduleJobService.findPage(new Page<ScheduleJob>(request, response), scheduleJob);
        return AjaxJson.success().put("page", page);
    }

    /**
     * 查看，增加，编辑定时任务表单页面
     */
    @ApiOperation(value = "详情信息", consumes = "application/form-data")
    @ApiImplicitParam(value = "任务id", name = "id")
    @PostMapping("queryById")
    public AjaxJson queryById(ScheduleJob scheduleJob) {
        return AjaxJson.success().put("scheduleJob", scheduleJob);
    }

    /**
     * 保存定时任务
     */
    @ApiOperation(value = "信息保存", consumes = "application/form-data")
    @ApiImplicitParam(value = "任务id", name = "id")
    @RequiresPermissions(value = {"quartz:scheduleJob:add", "quartz:scheduleJob:edit", "user"}, logical = Logical.OR)
    @PostMapping("save")
    public AjaxJson save(ScheduleJob scheduleJob, Model model, RedirectAttributes redirectAttributes) throws Exception {
        /**
         * 后台hibernate-validation插件校验
         */
        String errMsg = beanValidator(scheduleJob);
        if (StringUtils.isNotBlank(errMsg)) {
            return AjaxJson.error(errMsg);
        }

        //验证cron表达式
        if (!CronExpression.isValidExpression(scheduleJob.getCronExpression())) {
            return AjaxJson.error("cron表达式不正确！");
        }

        scheduleJobService.save(scheduleJob);//保存

        return AjaxJson.success("保存定时任务成功!");
    }


    /**
     * 批量删除定时任务
     */
    @ApiOperation(value = "删除任务", consumes = "application/form-data")
    @ApiImplicitParam(value = "任务ids，多条记录已 , 拼接", name = "ids")
    @RequiresPermissions(value = {"user","quartz:scheduleJob:del"}, logical = Logical.OR)
    @PostMapping("delete")
    public AjaxJson deleteAll(String ids, RedirectAttributes redirectAttributes) {
        AjaxJson j = new AjaxJson();
        String idArray[] = ids.split(",");
        for (String id : idArray) {
            scheduleJobService.delete(scheduleJobService.get(id));
        }
        return AjaxJson.success("删除定时任务成功");
    }


    /**
     * 暂停任务
     */
    @ApiOperation(value = "暂停任务", consumes = "application/form-data")
    @ApiImplicitParam(value = "任务id", name = "id")
    @RequiresPermissions(value = {"user","quartz:scheduleJob:stop"}, logical = Logical.OR)
    @PostMapping("stop")
    public AjaxJson stop(ScheduleJob scheduleJob) {
        scheduleJobService.stopJob(scheduleJob);
        return AjaxJson.success("暂停成功!");
    }


    /**
     * 立即运行一次
     */
    @ApiOperation(value = "立即运行一次", consumes = "application/form-data")
    @ApiImplicitParam(value = "任务id", name = "id")
    @RequiresPermissions(value = {"user","quartz:scheduleJob:startNow"}, logical = Logical.OR)
    @PostMapping("startNow")
    public AjaxJson stratNow(ScheduleJob scheduleJob) {
        scheduleJobService.startNowJob(scheduleJob);
        return AjaxJson.success("运行成功");
    }

    /**
     * 恢复
     */
    @ApiOperation(value = "启动/恢复", consumes = "application/form-data")
    @ApiImplicitParam(value = "任务id", name = "id")
    @RequiresPermissions(value = {"user","quartz:scheduleJob:resume"}, logical = Logical.OR)
    @PostMapping("resume")
    public AjaxJson resume(ScheduleJob scheduleJob, RedirectAttributes redirectAttributes) {
        scheduleJobService.restartJob(scheduleJob);
        scheduleJobService.startNowJob(scheduleJob);//恢复之后，立即触发一次激活定时任务，不然定时任务有可能不会执行，这是bug的回避案，具体原因我没找到。
        return AjaxJson.success("恢复成功");
    }

    /**
     * 验证类任务类是否存在
     */
    @GetMapping("existsClass")
    public AjaxJson existsClass(String className) {
        Class job = null;
        try {
            job = Class.forName(className);
            return AjaxJson.success();
        } catch (ClassNotFoundException e1) {
            return AjaxJson.error("类不存在");
        }
    }

}

/**
 * Copyright &copy; 2015-2020 <a href="http://www.jeeplus.org/">JeePlus</a> All rights reserved.
 */
package com.jeeplus.modules.oa.entity;

import java.util.List;

import org.hibernate.validator.constraints.Length;

import com.google.common.collect.Lists;
import com.jeeplus.common.utils.Collections3;
import com.jeeplus.common.utils.IdGen;
import com.jeeplus.common.utils.StringUtils;
import com.jeeplus.modules.sys.entity.User;
import com.jeeplus.core.persistence.DataEntity;

/**
 * 通知通告Entity
 *
 * <AUTHOR>
 * @version 2017-05-16
 */
public class OaNotify extends DataEntity<OaNotify> {

    private static final long serialVersionUID = 1L;
    private String type;        // 类型
    private String title;        // 标题
    private String content;        // 类型
    private String files;        // 附件
    private String status;        // 状态

    private String readNum;        // 已读
    private String unReadNum;    // 未读

    private boolean isSelf;        // 是否只查询自己的通知

    private String readFlag;    // 本人阅读状态

    private List<OaNotifyRecord> oaNotifyRecordList = Lists.newArrayList();

    public OaNotify() {
        super();
    }

    public OaNotify(String id) {
        super(id);
    }

    @Length(min = 0, max = 200, message = "标题长度必须介于 0 和 200 之间")
    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    @Length(min = 0, max = 1, message = "类型长度必须介于 0 和 1 之间")
    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    @Length(min = 0, max = 1, message = "状态长度必须介于 0 和 1 之间")
    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    @Length(min = 0, max = 2000, message = "附件长度必须介于 0 和 2000 之间")
    public String getFiles() {
        return files;
    }

    public void setFiles(String files) {
        this.files = files;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getReadNum() {
        return readNum;
    }

    public void setReadNum(String readNum) {
        this.readNum = readNum;
    }

    public String getUnReadNum() {
        return unReadNum;
    }

    public void setUnReadNum(String unReadNum) {
        this.unReadNum = unReadNum;
    }

    public List<OaNotifyRecord> getOaNotifyRecordList() {
        return oaNotifyRecordList;
    }

    public void setOaNotifyRecordList(List<OaNotifyRecord> oaNotifyRecordList) {
        this.oaNotifyRecordList = oaNotifyRecordList;
    }

    /**
     * 获取通知发送记录用户ID
     *
     * @return
     */
    public String getOaNotifyRecordIds() {
        return Collections3.extractToString(oaNotifyRecordList, "user.id", ",");
    }

    /**
     * 设置通知发送记录用户ID
     *
     * @return
     */
    public void setOaNotifyRecordIds(String oaNotifyRecord) {
        this.oaNotifyRecordList = Lists.newArrayList();
        for (String id : StringUtils.split(oaNotifyRecord, ",")) {
            OaNotifyRecord entity = new OaNotifyRecord();
            entity.setId(IdGen.uuid());
            entity.setOaNotify(this);
            entity.setUser(new User(id));
            entity.setReadFlag("0");
            this.oaNotifyRecordList.add(entity);
        }
    }

    /**
     * 获取通知发送记录用户Name
     *
     * @return
     */
    public String getOaNotifyRecordNames() {
        return Collections3.extractToString(oaNotifyRecordList, "user.name", ",");
    }

    /**
     * 设置通知发送记录用户Name
     *
     * @return
     */
    public void setOaNotifyRecordNames(String oaNotifyRecord) {
        // 什么也不做
    }

    public boolean isSelf() {
        return isSelf;
    }

    public void setSelf(boolean isSelf) {
        this.isSelf = isSelf;
    }

    public String getReadFlag() {
        return readFlag;
    }

    public void setReadFlag(String readFlag) {
        this.readFlag = readFlag;
    }
}
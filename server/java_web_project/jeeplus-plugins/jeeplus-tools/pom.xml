<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>org.jeeplus</groupId>
        <artifactId>jeeplus</artifactId>
        <version>8.0</version>
        <relativePath>../../pom.xml</relativePath>
    </parent>

    <artifactId>jeeplus-tools</artifactId>
    <packaging>jar</packaging>

    <name>jeeplus-tools</name>
    <description>tools project for jeeplus</description>


    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <java.version>1.8</java.version>
        <lib.path>${basedir}/src/main/webapp/WEB-INF/lib</lib.path>
    </properties>

    <dependencies>
        <dependency>
            <groupId>org.jeeplus</groupId>
            <artifactId>jeeplus-core</artifactId>
            <version>${project.parent.version}</version>
        </dependency>
        <dependency>
            <groupId>org.jeeplus</groupId>
            <artifactId>jeeplus-admin</artifactId>
            <version>${project.parent.version}</version>
        </dependency>

        <!-- 条形码、二维码生成  -->
        <dependency>
            <groupId>QRCoder</groupId>
            <artifactId>QRCoder</artifactId>
            <version>1.0</version>
            <systemPath>${lib.path}/QRCode.jar</systemPath>
            <scope>system</scope>
        </dependency>

    </dependencies>


</project>

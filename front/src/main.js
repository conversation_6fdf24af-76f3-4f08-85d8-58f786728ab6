import Vue from 'vue'
import App from './App.vue'
import router from './router'
import store from './store'
import ElementUI from 'element-ui';
import 'element-ui/lib/theme-chalk/index.css';
import { VueAxios } from './utils/request'
import * as WPS from './static/jwps.es6'

Vue.use(VueAxios)
Vue.use(ElementUI);
Vue.prototype.wps = WPS;

Vue.config.productionTip = false

new Vue({
    router,
    store,
    render: h => h(App)
}).$mount('#app')
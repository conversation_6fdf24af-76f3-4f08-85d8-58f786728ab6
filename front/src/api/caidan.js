import request from '@/utils/request'

const api = {
    getList: '/api/YingYongCaiDan/getList',
    add: '/api/YingYongCaiDan/add',
    delete: '/api/YingYongCaiDan/del',
    update: '/api/YingYongCaiDan/up',
    jueseCaidan: '/api/YingYongCaiDan/getCaiDanByZhangHao',
    getListByYingYongId: '/api/YingYongCaiDan/getCaiDanByYingYong',
    yingYongTianJiaCaiDan: '/api/YingYongCaiDan/yingYongTianJiaCaiDan'
}

export function getList(parameter) {
    return request({
        url: api.getList,
        method: 'post',
        data: parameter
    })
}
export function add(parameter) {
    return request({
        url: api.add,
        method: 'post',
        data: parameter
    })
}
export function del(parameter) {
    return request({
        url: api.delete,
        method: 'post',
        data: parameter
    })
}
export function update(parameter) {
    return request({
        url: api.update,
        method: 'post',
        data: parameter
    })
}
export function jueseCaidan(parameter) {
    return request({
        url: api.jueseCaidan,
        method: 'post',
        data: parameter
    })
}
export function getListByYingYongId(parameter) {
    return request({
        url: api.getListByYingYongId,
        method: 'post',
        data: parameter
    })
}
export function yingYongTianJiaCaiDan(parameter) {
    return request({
        url: api.yingYongTianJiaCaiDan,
        method: 'post',
        data: parameter
    })
}

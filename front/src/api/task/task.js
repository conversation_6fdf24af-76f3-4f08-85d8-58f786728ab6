import request from '@/utils/request'
//  定时任务
const api = {
    startNow: '/law/quartz/scheduleJob/startNow',
    save: '/law/quartz/scheduleJob/save',
    delete: '/law/quartz/scheduleJob/delete',
    queryById: '/law/quartz/scheduleJob/queryById',
    resume: '/law/quartz/scheduleJob/resume',
    stop: '/law/quartz/scheduleJob/stop',
    getList: '/law/quartz/scheduleJob/list'
}

export function startNow(parameter) {
    return request({
        url: api.startNow,
        method: 'post',
        data: parameter,
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded' // 不进行header设置的默认格式
        },
    })
}

export function getList(parameter) {
    return request({
        url: api.getList,
        method: 'post',
        data: parameter,
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded' // 不进行header设置的默认格式
        },
    })
}

export function save(parameter) {
    return request({
        url: api.save,
        method: 'post',
        data: parameter,
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded' // 不进行header设置的默认格式
        },
    })
}

export function del(parameter) {
    return request({
        url: api.delete,
        method: 'post',
        data: parameter,
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded' // 不进行header设置的默认格式
        },
    })
}

export function queryById(parameter) {
    return request({
        url: api.queryById,
        method: 'post',
        data: parameter,
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded' // 不进行header设置的默认格式
        },
    })
}

export function resume(parameter) {
    return request({
        url: api.resume,
        method: 'post',
        data: parameter,
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded' // 不进行header设置的默认格式
        },
    })
}

export function stop(parameter) {
    return request({
        url: api.stop,
        method: 'post',
        data: parameter,
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded' // 不进行header设置的默认格式
        },
    })
}
import request from '@/utils/request'
// 财产保全接口
const api = {
    getList: '/law/case/casePropertyPreservation/list',
    add: '/law/case/casePropertyPreservation/save',
    delete: '/law/case/casePropertyPreservation/delete',
    queryById: '/law/case/casePropertyPreservation/queryById',
}

export function getPreservationList(parameter) {
    return request({
        url: api.getList,
        method: 'post',
        data: parameter,
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded' // 不进行header设置的默认格式
        },
    })
}
export function addPreservation(parameter) {
    return request({
        url: api.add,
        method: 'post',
        data: parameter
    })
}
export function delPreservation(parameter) {
    return request({
        url: api.delete,
        method: 'post',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded' // 不进行header设置的默认格式
        },
        data: parameter
    })
}

export function queryPreservationById(parameter) {
    return request({
        url: api.queryById,
        method: 'post',
        data: parameter,
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded' // 不进行header设置的默认格式
        },
    })
}

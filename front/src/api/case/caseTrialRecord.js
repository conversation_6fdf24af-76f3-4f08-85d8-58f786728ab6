import request from '@/utils/request'
// 庭审记录
const api = {
    getList: '/law/case/caseTrialRecord/list',
    add: '/law/case/caseTrialRecord/save',
    delete: '/law/case/caseTrialRecord/delete',
    queryById: '/law/case/caseTrialRecord/queryById',
}

export function getTrialList(parameter) {
    return request({
        url: api.getList,
        method: 'post',
        data: parameter,
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded' // 不进行header设置的默认格式
        },
    })
}
export function addTrial(parameter) {
    return request({
        url: api.add,
        method: 'post',
        data: parameter
    })
}
export function delTrial(parameter) {
    return request({
        url: api.delete,
        method: 'post',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded' // 不进行header设置的默认格式
        },
        data: parameter
    })
}

export function queryTrialById(parameter) {
    return request({
        url: api.queryById,
        method: 'post',
        data: parameter,
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded' // 不进行header设置的默认格式
        },
    })
}

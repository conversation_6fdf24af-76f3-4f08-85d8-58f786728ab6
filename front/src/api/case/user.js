import request from '@/utils/request'
// 案件策略接口
const api = {
    getList: '/law/sys/user/list',
    add: '/law/sys/user/save',
    delete: '/law/sys/user/delete',
    queryById: '/law/sys/user/queryById',
    check: '/law/sys/user/isCheckLoginName',
    savePwd: '/law/sys/user/savePwd',
    allBriefList: '/law/sys/user/allBriefList',
}

export function getList(parameter) {
    return request({
        url: api.getList,
        method: 'post',
        data: parameter,
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded' // 不进行header设置的默认格式
        },
    })
}
export function add(parameter) {
    return request({
        url: api.add,
        method: 'post',
        data: parameter
    })
}
export function del(parameter) {
    return request({
        url: api.delete,
        method: 'post',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded' // 不进行header设置的默认格式
        },
        data: parameter
    })
}

export function queryById(parameter) {
    return request({
        url: api.queryById,
        method: 'post',
        data: parameter,
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded' // 不进行header设置的默认格式
        },
    })
}

export function allBriefList(parameter) {
    return request({
        url: api.allBriefList,
        method: 'post',
        data: parameter,
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded' // 不进行header设置的默认格式
        },
    })
}
export function savePwd(parameter) {
    return request({
        url: api.savePwd,
        method: 'post',
        data: parameter,
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded' // 不进行header设置的默认格式
        },
    })
}
export function check(parameter) {
    return request({
        url: api.check,
        method: 'post',
        data: parameter,
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded' // 不进行header设置的默认格式
        },
    })
}
import request from '@/utils/request'
// 承办人接口
const api = {
    caseUserList: '/law/case/caseUser/userList',
    add: '/law/case/caseUser/save',
    delete: '/law/case/caseUser/delete',
    queryById: '/law/case/caseUndertakePerson/queryById',
    getList: '/law/case/caseUser/list',
}

export function getUserList(parameter) {
    return request({
        url: api.getList,
        method: 'post',
        data: parameter,
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded' // 不进行header设置的默认格式
        },
    })
}
export function addUser(parameter) {
    return request({
        url: api.add,
        method: 'post',
        data: parameter
    })
}
export function delUser(parameter) {
    return request({
        url: api.delete,
        method: 'post',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded' // 不进行header设置的默认格式
        },
        data: parameter
    })
}

export function caseUserList(parameter) {
    return request({
        url: api.caseUserList,
        method: 'post',
        data: parameter,
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded' // 不进行header设置的默认格式
        },
    })
}

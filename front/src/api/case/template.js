import request from '@/utils/request'
// 阶段模板接口
const api = {
    getList: '/law/lawcase/stageTemplate/list',
    add: '/law/lawcase/stageTemplate/save',
    delete: '/law/lawcase/stageTemplate/delete',
    queryById: '/law/lawcase/stageTemplate/queryById',
    uploadFile: '/law/lawcase/stageRecord/fileUpload',
	 fileList: '/law/lawcase/stageRecord/fileList',
	deleteFile: '/law/lawcase/stageRecord/deleteFile',
	 updateSort: '/law/lawcase/stageTemplate/updateSort',
	 updateStageRecordSort: '/law/lawcase/stageRecord/updateSort',
}

export function getTemplateList(parameter) {
    return request({
        url: api.getList,
        method: 'post',
        data: parameter,
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded' // 不进行header设置的默认格式
        },
    })
}
export function saveTemplate(parameter) {
    return request({
        url: api.add,
        method: 'post',
        data: parameter
    })
}
export function delTemplate(parameter) {
    return request({
        url: api.delete,
        method: 'post',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded' // 不进行header设置的默认格式
        },
        data: parameter
    })
}

export function queryTemplateById(parameter) {
    return request({
        url: api.queryById,
        method: 'post',
        data: parameter,
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded' // 不进行header设置的默认格式
        },
    })
}

export function uploadFile(parameter) {
    return request({
        url: api.uploadFile,
        method: 'post',
        data: parameter
    })
}

export function fileList(parameter) {
    return request({
        url: api.fileList,
        method: 'post',
        data: parameter
    })
}

export function deleteFile(parameter) {
    return request({
        url: api.deleteFile,
        method: 'post',
        data: parameter
    })
}

export function updateSort(parameter) {
    return request({
        url: api.updateSort,
        method: 'post',
        data: parameter
    })
}

export function updateStageRecordSort(parameter) {
    return request({
        url: api.updateStageRecordSort,
        method: 'post',
        data: parameter
    })
}

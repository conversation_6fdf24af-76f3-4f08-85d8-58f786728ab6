import request from '@/utils/request'
// 案件审理程序
const api = {
    getList: '/law/lawcase/caseProgram/treeData',
    add: '/law/lawcase/caseProgram/save',
    delete: '/law/lawcase/caseProgram/delete',
    queryById: '/law/lawcase/caseProgram/queryById',
    typeTreeData: '/law/lawcase/caseProgram/typeTreeData',  //根据案件类型获取数据
}

export function getList(parameter) {
    return request({
        url: api.getList,
        method: 'post',
        data: parameter,
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded' // 不进行header设置的默认格式
        },
    })
}
export function save(parameter) {
    return request({
        url: api.add,
        method: 'post',
        data: parameter
    })
}
export function del(parameter) {
    return request({
        url: api.delete,
        method: 'post',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded' // 不进行header设置的默认格式
        },
        data: parameter
    })
}

export function queryById(parameter) {
    return request({
        url: api.queryById,
        method: 'post',
        data: parameter,
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded' // 不进行header设置的默认格式
        },
    })
}

export function typeTreeData(parameter) {
    return request({
        url: api.typeTreeData,
        method: 'post',
        data: parameter,
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded' // 不进行header设置的默认格式
        },
    })
}
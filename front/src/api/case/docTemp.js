import request from '@/utils/request'
// 文档模板接口
const api = {
    getList: '/law/wps/docCategory/treeData',
    add: '/law/wps/docCategory/save',
    delete: '/law/wps/docCategory/delete',
    queryById: '/law/wps/docCategory/queryById',
    getDocList: '/law/wps/docTemplate/list',
    addDoc: '/law/wps/docTemplate/save',
    deleteDoc: '/law/wps/docTemplate/delete',
    queryDocById: '/law/wps/docTemplate/queryById',
    useDoc: '/law/lawcase/caseFile/templateCopy'
}

export function getCategory(parameter) {
    return request({
        url: api.getList,
        method: 'get',
        data: parameter,
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded' // 不进行header设置的默认格式
        },
    })
}
export function addCategory(parameter) {
    return request({
        url: api.add,
        method: 'post',
        data: parameter
    })
}
export function delCategory(parameter) {
    return request({
        url: api.delete,
        method: 'post',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded' // 不进行header设置的默认格式
        },
        data: parameter
    })
}

export function getCategoryById(parameter) {
    return request({
        url: api.queryById,
        method: 'post',
        data: parameter,
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded' // 不进行header设置的默认格式
        },
    })
}


export function getDocList(parameter) {
    return request({
        url: api.getDocList,
        method: 'post',
        data: parameter,
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded' // 不进行header设置的默认格式
        },
    })
}
export function addDoc(parameter) {
    return request({
        url: api.addDoc,
        method: 'post',
        data: parameter
    })
}
export function deleteDoc(parameter) {
    return request({
        url: api.deleteDoc,
        method: 'post',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded' // 不进行header设置的默认格式
        },
        data: parameter
    })
}

export function queryDocById(parameter) {
    return request({
        url: api.queryDocById,
        method: 'post',
        data: parameter,
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded' // 不进行header设置的默认格式
        },
    })
}

export function useDoc(parameter) {
    return request({
        url: api.useDoc,
        method: 'post',
        data: parameter,
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded' // 不进行header设置的默认格式
        },
    })
}


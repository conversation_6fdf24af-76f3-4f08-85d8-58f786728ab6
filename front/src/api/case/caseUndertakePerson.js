import request from '@/utils/request'
// 承办人接口
const api = {
    getList: '/law/case/caseUndertakePerson/list',
    add: '/law/case/caseUndertakePerson/save',
    delete: '/law/case/caseUndertakePerson/delete',
    queryById: '/law/case/caseUndertakePerson/queryById',
}

export function getUndertakeList(parameter) {
    return request({
        url: api.getList,
        method: 'post',
        data: parameter,
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded' // 不进行header设置的默认格式
        },
    })
}
export function addUndertake(parameter) {
    return request({
        url: api.add,
        method: 'post',
        data: parameter
    })
}
export function delUndertake(parameter) {
    return request({
        url: api.delete,
        method: 'post',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded' // 不进行header设置的默认格式
        },
        data: parameter
    })
}

export function queryUndertakeById(parameter) {
    return request({
        url: api.queryById,
        method: 'post',
        data: parameter,
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded' // 不进行header设置的默认格式
        },
    })
}

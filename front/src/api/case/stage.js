import request from '@/utils/request'
// 阶段接口
const api = {
    getList: '/law/lawcase/caseStage/list',
    add: '/law/lawcase/caseStage/save',
    delete: '/law/lawcase/caseStage/delete',
    queryById: '/law/lawcase/caseStage/queryById',
    changeSort: '/law/lawcase/caseStage/changeSort',
    saveStage: '/law/lawcase/caseStage/saveStage',
    setCurrent: '/law/lawcase/caseStage/setCurrent',
}

export function getStageList(parameter) {
    return request({
        url: api.getList,
        method: 'post',
        data: parameter,
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded' // 不进行header设置的默认格式
        },
    })
}
export function save(parameter) {
    return request({
        url: api.add,
        method: 'post',
        data: parameter
    })
}
export function delStage(parameter) {
    return request({
        url: api.delete,
        method: 'post',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded' // 不进行header设置的默认格式
        },
        data: parameter
    })
}

export function queryStageById(parameter) {
    return request({
        url: api.queryById,
        method: 'post',
        data: parameter,
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded' // 不进行header设置的默认格式
        },
    })
}

export function saveStage(parameter) {
    return request({
        url: api.saveStage,
        method: 'post',
        data: parameter,
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded' // 不进行header设置的默认格式
        },
    })
}
export function changeSort(parameter) {
    return request({
        url: api.changeSort,
        method: 'post',
        data: parameter,
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded' // 不进行header设置的默认格式
        },
    })
}
export function setCurrent(parameter) {
    return request({
        url: api.setCurrent,
        method: 'post',
        data: parameter,
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded' // 不进行header设置的默认格式
        },
    })
}
import request from '@/utils/request'
// 当事人
const api = {
    getList: '/law/case/caseConcernPerson/list',
    add: '/law/case/caseConcernPerson/save',
    delete: '/law/case/caseConcernPerson/delete',
    queryById: '/law/case/caseConcernPerson/queryById',
    getAllConcernPerson: '/law/case/caseConcernPerson/allList',
    export: '/law/case/caseConcernPerson/export'
}

export function getPersonList(parameter) {
    return request({
        url: api.getList,
        method: 'post',
        data: parameter,
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded' // 不进行header设置的默认格式
        },
    })
}
export function savePerson(parameter) {
    return request({
        url: api.add,
        method: 'post',
        data: parameter
    })
}
export function delPerson(parameter) {
    return request({
        url: api.delete,
        method: 'post',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded' // 不进行header设置的默认格式
        },
        data: parameter
    })
}

export function queryPerspnById(parameter) {
    return request({
        url: api.queryById,
        method: 'post',
        data: parameter,
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded' // 不进行header设置的默认格式
        },
    })
}


export function getAllConcernPerson(parameter) {
    return request({
        url: api.getAllConcernPerson,
        method: 'post',
        data: parameter,
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded' // 不进行header设置的默认格式
        },
    })
}


export function exportExl(parameter) {
    return request({
        url: api.export,
        method: 'get',
        params: {
            pageNo: parameter.pageNo,
            pageSize: parameter.pageSize
        }
    })
}
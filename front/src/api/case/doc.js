import request from '@/utils/request'
// 阶段模板接口
const api = {
    getList: '/law/lawcase/caseFileDirectory/treeData',
    add: '/law/lawcase/caseFileDirectory/save',
    delete: '/law/lawcase/caseFileDirectory/delete',
    deleteFile: '/law/lawcase/caseFile/delete',
    caseFileList: '/law/lawcase/caseFile/list',
    moveFile: '/law/lawcase/caseFile/move',
    saveFile: '/law/lawcase/caseFile/save',
    uploadFile: '/law/lawcase/caseFile/upload',
    uploadDir: '/law/lawcase/caseFile/uploadDir',
    getUrl: '/law/wpsoffice/url',
    createDoc: '/law/lawcase/caseFile/create',
}

export function getTreeData(parameter) {
    return request({
        url: api.getList,
        method: 'post',
        data: parameter,
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded' // 不进行header设置的默认格式
        },
    })
}
export function createDirectory(parameter) {
    return request({
        url: api.add,
        method: 'post',
        data: parameter
    })
}
export function delDirectory(parameter) {
    return request({
        url: api.delete,
        method: 'post',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded' // 不进行header设置的默认格式
        },
        data: parameter
    })
}
export function deleteFile(parameter) {
    return request({
        url: api.deleteFile,
        method: 'post',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded' // 不进行header设置的默认格式
        },
        data: parameter
    })
}
export function caseFileList(parameter) {
    return request({
        url: api.caseFileList,
        method: 'post',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded' // 不进行header设置的默认格式
        },
        data: parameter
    })
}
export function moveFile(parameter) {
    return request({
        url: api.moveFile,
        method: 'post',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded' // 不进行header设置的默认格式
        },
        data: parameter
    })
}
export function saveFile(parameter) {
    return request({
        url: api.saveFile,
        method: 'post',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded' // 不进行header设置的默认格式
        },
        data: parameter
    })
}
export function uploadFile(parameter) {
    return request({
        url: api.uploadFile,
        method: 'post',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded' // 不进行header设置的默认格式
        },
        data: parameter
    })
}

export function uploadDir(parameter) {
    return request({
        url: api.uploadDir,
        method: 'post',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded' // 不进行header设置的默认格式
        },
        data: parameter
    })
}


export function getUrl(parameter) {
    return request({
        url: api.getUrl,
        method: 'get',
        params: {
            '_w_fname': parameter._w_fname,
            '_w_fileid': parameter._w_fileid,
            'operateType': parameter.operateType,
        }
    })
}

export function createDoc(parameter) {
    return request({
        url: api.createDoc,
        method: 'post',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded' // 不进行header设置的默认格式
        },
        data: parameter
    })
}
import request from '@/utils/request'
// 案件策略接口
const api = {
    caseFinanceList: '/law/lawcase/financeFlowRecord/caseFinanceList',
    getList: '/law/lawcase/financeFlowRecord/list',
    edit: '/law/lawcase/financeFlowRecord/save',
    add: '/law/lawcase/financeFlowRecord/saveInfo',
    delete: '/law/lawcase/financeFlowRecord/delete',
    queryById: '/law/lawcase/financeFlowRecord/queryById',
    listByCase: '/law/lawcase/financeFlowRecord/listByCase',
    queryCaseFinance: '/law/lawcase/financeFlowRecord/queryCaseFinance',
}

export function listByCase(parameter) {
    return request({
        url: api.listByCase,
        method: 'post',
        data: parameter,
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded' // 不进行header设置的默认格式
        },
    })
}

export function queryCaseFinance(parameter) {
    return request({
        url: api.queryCaseFinance,
        method: 'post',
        data: parameter,
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded' // 不进行header设置的默认格式
        },
    })
}

export function caseFinanceList(parameter) {
    return request({
        url: api.caseFinanceList,
        method: 'post',
        data: parameter,
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded' // 不进行header设置的默认格式
        },
    })
}
export function getList(parameter) {
    return request({
        url: api.getList,
        method: 'post',
        data: parameter,
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded' // 不进行header设置的默认格式
        },
    })
}
export function save(parameter) {
    return request({
        url: api.add,
        method: 'post',
        data: parameter
    })
}
export function edit(parameter) {
    return request({
        url: api.edit,
        method: 'post',
        data: parameter
    })
}
export function del(parameter) {
    return request({
        url: api.delete,
        method: 'post',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded' // 不进行header设置的默认格式
        },
        data: parameter
    })
}

export function queryById(parameter) {
    return request({
        url: api.queryById,
        method: 'post',
        data: parameter,
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded' // 不进行header设置的默认格式
        },
    })
}
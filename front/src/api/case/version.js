import request from '@/utils/request'
// 案件策略接口
const api = {
    getList: '/law/lawcase/version/list',
    add: '/law/lawcase/version/save',
    delete: '/law/lawcase/version/delete',
    queryById: '/law/lawcase/version/queryById',
}

export function getList(parameter) {
    return request({
        url: api.getList,
        method: 'post',
        data: parameter,
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded' // 不进行header设置的默认格式
        },
    })
}
export function add(parameter) {
    return request({
        url: api.add,
        method: 'post',
        data: parameter
    })
}
export function del(parameter) {
    return request({
        url: api.delete,
        method: 'post',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded' // 不进行header设置的默认格式
        },
        data: parameter
    })
}

export function queryById(parameter) {
    return request({
        url: api.queryById,
        method: 'post',
        data: parameter,
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded' // 不进行header设置的默认格式
        },
    })
}
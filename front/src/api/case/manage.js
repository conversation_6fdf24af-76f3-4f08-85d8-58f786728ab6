import request from '@/utils/request'

const api = {
    getList: '/law/lawcase/case/list',
    add: '/law/lawcase/case/saveInfo',
    delete: '/law/lawcase/case/delete',
    update: '/law/lawcase/case/save',
    queryById: '/law/lawcase/case/queryById',
    getCaseProgram: '/law/lawcase/caseProgram/typeTreeData',
    detailInfo: '/law/lawcase/case/detailInfo',
    archiveDownload: '/law/lawcase/case/archiveDownload',
    settle: '/law/lawcase/case/settle',
    audit: '/law/lawcase/case/audit',
    editUnit: '/law/lawcase/case/saveAccept',
    submitAudit: '/law/lawcase/case/submitAudit',
    allCaseData: '/law/lawcase/case/allData', //所有审核已通过案件信息，无分页
    caseStop: '/law/lawcase/case/caseStop', //案件终止
    caseCauseData: '/law/lawcase/caseCause/treeData', //
	 caseModelTypeData: '/law/lawcase/caseCause/modelTypeTreeData', //
     caseModelTypeTemplateData: '/law/lawcase/stageTemplate/modelTypeTreeData', //
}

export function getList(parameter) {
    return request({
        url: api.getList,
        method: 'post',
        data: parameter
    })
}
export function add(parameter) {
    return request({
        url: api.add,
        method: 'post',
        data: parameter
    })
}
export function del(parameter) {
    return request({
        url: api.delete,
        method: 'post',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded' // 不进行header设置的默认格式
        },
        data: parameter
    })
}
export function update(parameter) {
    return request({
        url: api.update,
        method: 'post',
        data: parameter
    })
}
export function queryById(parameter) {
    return request({
        url: api.queryById,
        method: 'post',
        data: parameter
    })
}
export function detailInfo(parameter) {
    return request({
        url: api.detailInfo,
        method: 'post',
        data: parameter
    })
}
export function getCaseProgram(parameter) {
    return request({
        url: api.getCaseProgram,
        method: 'post',
        data: parameter,
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded' // 不进行header设置的默认格式
        },
    })
}

//归档
export function archiveDownload(parameter) {
    return request({
        url: api.archiveDownload,
        method: 'get',
        params: {
            ids: parameter.ids
        },
    })
}

//结案
export function settle(parameter) {
    return request({
        url: api.settle,
        method: 'post',
        data: parameter,
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded' // 不进行header设置的默认格式
        },
    })
}

//审核
export function audit(parameter) {
    return request({
        url: api.audit,
        method: 'post',
        data: parameter,
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded' // 不进行header设置的默认格式
        },
    })
}

//修改受理单位
export function editUnit(parameter) {
    return request({
        url: api.editUnit,
        method: 'post',
        data: parameter,
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded' // 不进行header设置的默认格式
        },
    })
}

//提交审核
export function submitAudit(parameter) {
    return request({
        url: api.submitAudit,
        method: 'post',
        data: parameter,
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded' // 不进行header设置的默认格式
        },
    })
}

//所有审核已通过案件信息，无分页
export function allCaseData(parameter) {
    return request({
        url: api.allCaseData,
        method: 'post',
        data: parameter,
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded' // 不进行header设置的默认格式
        },
    })
}


//案件终止
export function caseStop(parameter) {
    return request({
        url: api.caseStop,
        method: 'post',
        data: parameter,
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded' // 不进行header设置的默认格式
        },
    })
}

//案由
export function caseCauseData(parameter) {
    return request({
        url: api.caseCauseData,
        method: 'post',
        data: parameter,
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded' // 不进行header设置的默认格式
        },
    })
}

//模板类型
export function caseModelTypeData(parameter) {
    return request({
        url: api.caseModelTypeData,
        method: 'post',
        data: parameter,
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded' // 不进行header设置的默认格式
        },
    })
}
export function caseModelTypeTemplateData(parameter) {
    return request({
        url: api.caseModelTypeTemplateData,
        method: 'post',
        data: parameter,
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded' // 不进行header设置的默认格式
        },
    })
}



import request from '@/utils/request'
// 执行情况接口
const api = {
    getList: '/law/case/caseExecuteSituation/list',
    add: '/law/case/caseExecuteSituation/save',
    delete: '/law/case/caseExecuteSituation/delete',
    queryById: '/law/case/caseExecuteSituation/queryById',
}

export function getExecuteList(parameter) {
    return request({
        url: api.getList,
        method: 'post',
        data: parameter,
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded' // 不进行header设置的默认格式
        },
    })
}
export function addExecute(parameter) {
    return request({
        url: api.add,
        method: 'post',
        data: parameter
    })
}
export function delExecute(parameter) {
    return request({
        url: api.delete,
        method: 'post',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded' // 不进行header设置的默认格式
        },
        data: parameter
    })
}

export function queryExecuteById(parameter) {
    return request({
        url: api.queryById,
        method: 'post',
        data: parameter,
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded' // 不进行header设置的默认格式
        },
    })
}

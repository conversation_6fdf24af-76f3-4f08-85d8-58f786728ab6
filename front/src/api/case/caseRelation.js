import request from '@/utils/request'
// 案件策略接口
const api = {
    getList: '/law/case/caseRelation/list',
    add: '/law/case/caseRelation/save',
    delete: '/law/case/caseRelation/delete',
    caseList: '/law/case/caseRelation/caseList',
}

export function getRelateList(parameter) {
    return request({
        url: api.getList,
        method: 'post',
        data: parameter,
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded' // 不进行header设置的默认格式
        },
    })
}
export function saveRelate(parameter) {
    return request({
        url: api.add,
        method: 'post',
        data: parameter,
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded' // 不进行header设置的默认格式
        },
    })
}
export function delRelate(parameter) {
    return request({
        url: api.delete,
        method: 'post',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded' // 不进行header设置的默认格式
        },
        data: parameter
    })
}

export function getCaseList(parameter) {
    return request({
        url: api.caseList,
        method: 'post',
        data: parameter,
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded' // 不进行header设置的默认格式
        },
    })
}

import request from '@/utils/request'
// 案件策略接口
const api = {
    getList: '/law/case/caseHandleStrategy/list',
    add: '/law/case/caseHandleStrategy/save',
    delete: '/law/case/caseHandleStrategy/delete',
    queryById: '/law/case/caseHandleStrategy/queryById',
}

export function getStrategyList(parameter) {
    return request({
        url: api.getList,
        method: 'post',
        data: parameter,
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded' // 不进行header设置的默认格式
        },
    })
}
export function saveStrategy(parameter) {
    return request({
        url: api.add,
        method: 'post',
        data: parameter
    })
}
export function delStrategy(parameter) {
    return request({
        url: api.delete,
        method: 'post',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded' // 不进行header设置的默认格式
        },
        data: parameter
    })
}

export function queryStrategyById(parameter) {
    return request({
        url: api.queryById,
        method: 'post',
        data: parameter,
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded' // 不进行header设置的默认格式
        },
    })
}

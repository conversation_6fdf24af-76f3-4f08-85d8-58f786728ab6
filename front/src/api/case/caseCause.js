import request from '@/utils/request'
// 案由维护
const api = {
    getList: '/law/lawcase/caseCause/treeData',
    add: '/law/lawcase/caseCause/save',
    delete: '/law/lawcase/caseCause/delete',
    queryById: '/law/lawcase/caseCause/queryById',
}

export function getList(parameter) {
    return request({
        url: api.getList,
        method: 'post',
        data: parameter,
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded' // 不进行header设置的默认格式
        },
    })
}
export function save(parameter) {
    return request({
        url: api.add,
        method: 'post',
        data: parameter
    })
}
export function del(parameter) {
    return request({
        url: api.delete,
        method: 'post',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded' // 不进行header设置的默认格式
        },
        data: parameter
    })
}

export function queryById(parameter) {
    return request({
        url: api.queryById,
        method: 'post',
        data: parameter,
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded' // 不进行header设置的默认格式
        },
    })
}

import request from '@/utils/request'
// 阶段接口
const api = {
    caseList: '/law/lawcase/todoInfo/caseList',
    add: '/law/lawcase/todoInfo/save',
    delete: '/law/lawcase/todoInfo/delete',
    queryById: '/law/lawcase/todoInfo/queryById2',
    changeStatus: '/law/lawcase/todoInfo/changeStatus',
    customerList: '/law/lawcase/todoInfo/customerList',
    statisticList: '/law/lawcase/todoInfo/statisticList',
    fileList: '/law/lawcase/todoInfo/fileList',
    deleteFile: '/law/lawcase/todoInfo/deleteFile',
    fileUpload: '/law/lawcase/todoInfo/fileUpload',
    useTodoDoc: '/law/lawcase/todoInfo/templateCopy',
}

export function todoList(parameter) {
    return request({
        url: api.caseList,
        method: 'post',
        data: parameter,
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded' // 不进行header设置的默认格式
        },
    })
}
export function saveTodo(parameter) {
    return request({
        url: api.add,
        method: 'post',
        data: parameter
    })
}
export function delTodo(parameter) {
    return request({
        url: api.delete,
        method: 'post',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded' // 不进行header设置的默认格式
        },
        data: parameter
    })
}
export function deleteFile(parameter) {
    return request({
        url: api.deleteFile,
        method: 'post',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded' // 不进行header设置的默认格式
        },
        data: parameter
    })
}
export function fileUpload(parameter) {
    return request({
        url: api.fileUpload,
        method: 'post',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded' // 不进行header设置的默认格式
        },
        data: parameter
    })
}

export function queryTodoById(parameter) {
    return request({
        url: api.queryById,
        method: 'post',
        data: parameter,
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded' // 不进行header设置的默认格式
        },
    })
}

export function customerList(parameter) {
    return request({
        url: api.customerList,
        method: 'post',
        data: parameter,
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded' // 不进行header设置的默认格式
        },
    })
}
export function fileList(parameter) {
    return request({
        url: api.fileList,
        method: 'post',
        data: parameter,
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded' // 不进行header设置的默认格式
        },
    })
}
export function changeStatus(parameter) {
    return request({
        url: api.changeStatus,
        method: 'post',
        data: parameter,
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded' // 不进行header设置的默认格式
        },
    })
}
export function statisticList(parameter) {
    return request({
        url: api.statisticList,
        method: 'get',
        data: parameter,
    })
}
export function useTodoDoc(parameter) {
    return request({
        url: api.useTodoDoc,
        method: 'post',
        data: parameter,
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded' // 不进行header设置的默认格式
        },
    })
}
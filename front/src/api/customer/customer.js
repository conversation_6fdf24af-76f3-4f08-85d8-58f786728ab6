import request from '@/utils/request'
// 客户接口
const api = {
    getList: '/law/lawcase/customer/list',
    add: '/law/lawcase/customer/saveInfo',
    edit: '/law/lawcase/customer/save',
    delete: '/law/lawcase/customer/delete',
    queryById: '/law/lawcase/customer/queryById',
    allCustomerData: '/law/lawcase/customer/allData',
    industryData: '/law/lawcase/industry/treeData',
}

export function getList(parameter) {
    return request({
        url: api.getList,
        method: 'post',
        data: parameter,
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded' // 不进行header设置的默认格式
        },
    })
}
export function add(parameter) {
    return request({
        url: api.add,
        method: 'post',
        data: parameter
    })
}
export function deleteCustomer(parameter) {
    return request({
        url: api.delete,
        method: 'post',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded' // 不进行header设置的默认格式
        },
        data: parameter
    })
}

export function queryById(parameter) {
    return request({
        url: api.queryById,
        method: 'post',
        data: parameter,
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded' // 不进行header设置的默认格式
        },
    })
}

export function allCustomerData(parameter) {
    return request({
        url: api.allCustomerData,
        method: 'post',
        data: parameter,
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded' // 不进行header设置的默认格式
        },
    })
}

export function industryData(parameter) {
    return request({
        url: api.industryData,
        method: 'get',
        data: parameter
    })
}

export function edit(parameter) {
    return request({
        url: api.edit,
        method: 'post',
        data: parameter
    })
}
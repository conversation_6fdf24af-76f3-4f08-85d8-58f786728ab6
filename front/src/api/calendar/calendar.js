import request from '@/utils/request'
//  日历接口
const api = {
    getMonthList: '/law/lawcase/todoInfo/monthList',
    add: '/law/case/customerContacts/save',
    delete: '/law/case/customerContacts/delete',
    queryById: '/law/case/customerContacts/queryById',
    statisticList: '/law/lawcase/todoInfo/statisticList'
}

export function getMonthList(parameter) {
    return request({
        url: api.getMonthList,
        method: 'post',
        data: parameter,
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded' // 不进行header设置的默认格式
        },
    })
}

export function statisticList(parameter) {
    return request({
        url: api.statisticList,
        method: 'get',
        data: parameter,
    })
}
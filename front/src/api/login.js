import request from '@/utils/request'

const userApi = {
    Login: '/law/sys/login',
    Logout: '/law/sys/logout',
    RefreshToken: '/law/sys/refreshToken',
    getAllDict: '/law/sys/user/getAllDict',
    getUserInfo: '/law/app/sys/user/info',
    allBriefList: '/law/sys/user/allBriefList',
    upload: '/law/sys/file/webupload/upload',
    modifyPwd: '/law/sys/user/savePwd',
}

/**
 * login func
 * parameter: {
 *     username: '',
 *     password: '',
 *     remember_me: true,
 *     captcha: '12345'
 * }
 * @param parameter
 * @returns {*}
 */
export function login(parameter) {
    return request({
        url: userApi.Login,
        method: 'post',
        data: parameter
    })
}

export function logout(parameter) {
    return request({
        url: userApi.Logout,
        method: 'get',
        data: parameter
    })
}

export function refreshToken(parameter) {
    return request({
        url: userApi.RefreshToken,
        method: 'get',
        data: parameter
    })
}

export function getAllDict(parameter) {
    return request({
        url: userApi.getAllDict,
        method: 'get',
        data: parameter
    })
}

export function getUserInfo(parameter) {
    return request({
        url: userApi.getUserInfo,
        method: 'get',
        data: parameter
    })
}

export function allBriefList(parameter) {
    return request({
        url: userApi.allBriefList,
        method: 'post',
        data: parameter
    })
}

export function upload(parameter) {
    return request({
        url: userApi.upload,
        method: 'post',
        data: parameter,
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded' // 不进行header设置的默认格式
        },
    })
}

export function modifyPwd(parameter) {
    return request({
        url: userApi.modifyPwd,
        method: 'post',
        data: parameter,
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded' // 不进行header设置的默认格式
        },
    })
}
// file suffix
export const fileSuffix = [
    'xls', 'xlt', 'et', 'xlsx', 'xltx', 'csv', 'xlsm', 'xltm',
    'doc', 'dot', 'wps', 'wpt', 'docx', 'dotx', 'docm', 'dotm',
    'ppt', 'pptx', 'pptm', 'ppsx', 'ppsm', 'pps', 'potx', 'potm', 'dpt', 'dps',
    'pdf'
];

export const imgSuffix = [
    'bmp', 'jpg', 'jpeg', 'png', 'gif', 'webp'
];

// 增加一个名为 IsPicture 的函数作为
// String 构造函数的原型对象的一个方法。
String.prototype.IsPicture = function() {
    //判断是否是图片 - strFilter必须是小写列举
    var strFilter = ".jpeg|.gif|.jpg|.png|.bmp|.pic|.svg|"
    if (this.indexOf(".") > -1) {
        var p = this.lastIndexOf(".");
        var strPostfix = this.substring(p, this.length) + '|';
        strPostfix = strPostfix.toLowerCase();
        if (strFilter.indexOf(strPostfix) > -1) {
            return true;
        }
    }
    return false;
}
import axios from 'axios'
import store from '@/store'
import storage from 'store'
import { VueAxios } from './axios'
import { ACCESS_TOKEN } from '@/store/mutation-types'
import qs from 'qs'
import merge from 'lodash/merge'
import Vue from 'vue';
let v = new Vue();

// 创建 axios 实例
const request = axios.create({
    // API 请求的默认前缀
    baseURL: process.env.VUE_APP_API_BASE_URL,
    timeout: 60000 // 请求超时时间
})

// 异常拦截处理器
const errorHandler = (error) => {
    if (error.response) {
        const data = error.response.data
            // 从 localstorage 获取 token
        const token = storage.get('token')
        if (error.response.status === 403) {
            v.$notify.error({
                title: 'Forbidden',
                message: 'Authorization verification failed'
            });
        }
        if (error.response.status === 401) {
            v.$notify.error({
                title: 'Unauthorized',
                message: data.message
            });
            if (token) {
                store.dispatch('Logout').then(() => {
                    setTimeout(() => {
                        window.location.reload()
                    }, 1500)
                })
            }
        }
    }
    return Promise.reject(error)
}

// request interceptor
request.interceptors.request.use(config => {
    // const token = storage.get(ACCESS_TOKEN)
    const token = storage.get('token')
        // 如果 token 存在
        // 让每个请求携带自定义 token 请根据实际情况自行修改
    if (token) {
        config.headers['token'] = token
    }

    if (process.env.NODE_ENV == 'development') {
        // config.baseURL = 'http://law.291w.com'
        // config.baseURL = 'http://law.ngrok.24k.fun'
    }

    // const type = config.method
    // const defaults = {}
    // const arrayFormat = config.headers.arrayFormat || 'indices'
    // if (type === 'post' && config.headers['Content-Type'] === 'application/x-www-form-urlencoded; charset=utf-8') {
    //     // post请求参数处理
    //     config.data = qs.stringify(config.data, { allowDots: true, arrayFormat: arrayFormat })

    // } else if (type === 'get') {
    //     // get请求参数处理
    //     config.params = qs.stringify(config.params, { allowDots: true, arrayFormat: arrayFormat })
    //     config.params = qs.parse(config.params)
    //     config.params = merge(defaults, config.params)

    // }

    return config
}, errorHandler)

// response interceptor
request.interceptors.response.use((response) => {
        return response.data
    }, errorHandler)
    // request.interceptors.response.use((response) => {
    //     if (response.data == null && response.config.responseType === 'json' && response.request.responseText != null) {
    //         try {
    //             // eslint-disable-next-line no-param-reassign
    //             response.data = JSON.parse(response.request.responseText)
    //         } catch (e) {
    //             // ignored
    //         }
    //     }
    //     return response.data
    // }, errorHandler)

const installer = {
    vm: {},
    install(Vue) {
        Vue.use(VueAxios, request)
    }
}

export default request

export {
    installer as VueAxios,
    request as axios
}

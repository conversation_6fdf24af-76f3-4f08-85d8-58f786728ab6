import storage from "store";
import moment from 'moment';
const createObjectURL = object => {
    return window.URL ? window.URL.createObjectURL(object) : window.webkitURL.createObjectURL(object);
};

export const formatParams = (url, params, fileName, fileType) => {
    if (!params) {
        return '';
    }
    let temp = '';
    for (const [k, v] of Object.entries(params)) {
        temp += `&${k}=${v}`;
    }
    // const baseUrl = sessionStorage.getItem('BASE_URL');
    const baseUrl = process.env.VUE_APP_API_BASE_URL
    const serviceUrl = `${baseUrl ? baseUrl : ''}${url}?${temp.substr(1, temp.length)}`;
    const xhr = new XMLHttpRequest();
    const formData = new FormData();
    xhr.open('get', serviceUrl); // url填写后台的接口地址，如果是post，在formData append参数（参考原文地址）
    xhr.setRequestHeader('token', `${storage.get('token')}`);
    xhr.responseType = 'blob';
    xhr.onload = function(e) {
        if (this.status === 200) {
            const blob = this.response;
            // xls文件名称
            const filename = `${fileName ? fileName : moment().format('YYYY年MM月DD日 hh:mm:ss')}.${fileType?fileType:'xlsx'}`;
            if (window.navigator.msSaveOrOpenBlob) {
                navigator.msSaveBlob(blob, filename);
            } else {
                const a = document.createElement('a');
                const url = createObjectURL(blob);
                a.href = url;
                a.download = filename;
                document.body.appendChild(a);
                a.click();
                window.URL.revokeObjectURL(url);
            }
        }
    };
    xhr.send(formData);
};

export default formatParams;
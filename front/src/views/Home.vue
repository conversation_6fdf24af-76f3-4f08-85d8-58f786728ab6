<template>
  <div class="main">
    <el-container>
      <el-header>
        <div class="top-menu">
          <div class="menu-links">
            <img
              src="../assets/logo-img.png"
              alt="logo"
              class="top-logo-img cursor"
            />
            <el-dropdown trigger="click" @command="handleCommand">
              <span class="el-dropdown-link" style="color: #fff">
                {{ user.name }}
                <!-- 管理员 -->
                <i class="el-icon-arrow-down el-icon--right"></i>
              </span>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item command="modify" v-if="user.loginName!='test01'"
                  ><span>修改密码</span>
                </el-dropdown-item>
                <el-dropdown-item command="logout"
                  ><span>退出登录</span>
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </div>
        </div>
      </el-header>
      <el-row class="tac">
        <el-col :span="2" style="height: 100%">
          <el-menu
            :default-active="$route.path"
            class="el-menu-vertical-demo"
            router
            @select="handleSelect"
          >
            <el-menu-item index="/calendar">
              <i class="el-icon-menu"></i>
              <span slot="title">日历</span>
            </el-menu-item>
            <el-menu-item index="/case">
              <i class="el-icon-s-order"></i>
              <span slot="title">案件</span>
            </el-menu-item>
			<el-menu-item
			  index="/caseConflict" 
			>
			  <i class="el-icon-menu"></i>
			  <span slot="title">冲突审查</span>
			</el-menu-item>
            <!-- <el-menu-item index="/client">
              <i class="el-icon-s-custom"></i>
              <span slot="title">客户</span>
            </el-menu-item> -->
            <el-menu-item
              index="/caseCause"
              v-if="user.type.indexOf('2') > -1 || user.type.indexOf('1') > -1"
            >
              <i class="el-icon-setting"></i>
              <span slot="title">案由管理</span>
            </el-menu-item>
			<el-menu-item v-if="user.loginName=='test01'"
			  index="/nocase" 
			>
			  <i class="el-icon-menu"></i>
			  <span slot="title">案由管理</span>
			</el-menu-item>
            <el-menu-item
              index="/caseProgram"
              v-if="user.type.indexOf('2') > -1 || user.type.indexOf('1') > -1"
            >
              <i class="el-icon-menu"></i>
              <span slot="title">审理程序</span>
            </el-menu-item>
			<el-menu-item v-if="user.loginName=='test01'"
			  index="/noprogram" 
			>
			  <i class="el-icon-menu"></i>
			  <span slot="title">审理程序</span>
			</el-menu-item>
            <el-menu-item
              index="/caseReview"
              v-if="user.type.indexOf('2') > -1 || user.type.indexOf('1') > -1"
            >
              <i class="el-icon-s-check"></i>
              <span slot="title">案件审核</span>
            </el-menu-item> 
			<el-menu-item v-if="user.loginName=='test01'"
			  index="/nocheck" 
			>
			  <i class="el-icon-menu"></i>
			  <span slot="title">案件审核</span>
			</el-menu-item>
			
			
			
			<el-menu-item
			  index="/manageTemplate"
			  v-if="user.type.indexOf('2') > -1 || user.type.indexOf('1') > -1 	  ||  user.loginName=='test01'"" 
			>
			  <i class="el-icon-user-solid"></i>
			  <span slot="title">模板管理</span>
			</el-menu-item>
			
            <el-menu-item
              index="/user"
              v-if="user.type.indexOf('2') > -1 || user.type.indexOf('1') > -1"
            >
              <i class="el-icon-user-solid"></i>
              <span slot="title">用户管理</span>
            </el-menu-item>
			<el-menu-item v-if="user.loginName=='test01'"
			  index="/nouser" 
			>
			  <i class="el-icon-menu"></i>
			  <span slot="title">用户管理</span>
			</el-menu-item>
            <el-menu-item
              index="/version"
              v-if="user.type.indexOf('2') > -1 || user.type.indexOf('1') > -1"
            >
              <i class="el-icon-link"></i>
              <span slot="title">版本管理</span>
            </el-menu-item>
            <el-menu-item
              index="/cost"  v-if="user.loginName!='test01'"
            >
              <i class="el-icon-s-finance"></i>
              <span slot="title">收费情况</span>
            </el-menu-item>
			<el-menu-item v-if="user.loginName=='test01'"
			  index="/nocost" 
			>
			  <i class="el-icon-menu"></i>
			  <span slot="title">收费情况</span>
			</el-menu-item>
             <el-menu-item v-if="user.loginName!='test01'"
              index="/finance"
            >
              <i class="el-icon-s-order"></i>
              <span slot="title">财务管控</span>
            </el-menu-item>
			<el-menu-item v-if="user.loginName=='test01'"
			  index="/nofinance" 
			>
			  <i class="el-icon-menu"></i>
			  <span slot="title">财务管控</span>
			</el-menu-item>
			
            <el-menu-item
              index="/timingTask"
              v-if="user.type.indexOf('2') > -1 || user.type.indexOf('1') > -1"
            >
              <i class="el-icon-time"></i>
              <span slot="title">定时任务</span>
            </el-menu-item>
          </el-menu>
        </el-col>
        <el-main>
          <router-view></router-view>
        </el-main>
      </el-row>
    </el-container>
    <ModifyPassword
      ref="modifyForm"
      :visible="visible"
      @cancel="handleCancel"
      @ok="handleOk"
    />
  </div>
</template>

<script>
// @ is an alias to /src
import { logout, modifyPwd } from "@/api/login";
import { mapActions } from "vuex";
import storage from "store";
import ModifyPassword from "@/components/ModifyPassword";
export default {
  name: "Home",
  components: {
    ModifyPassword,
  },
  data() {
    return {
      user: storage.get("user"),
      visible: false,
    };
  },
  methods: {
    ...mapActions(["Logout"]),

    logout() {
      this.Logout().then((res) => {
        if (res.success) {
          this.$router.replace({ path: "/" });
        } else {
          this.$message.error(res.msg);
        }
      });
    },
    handleSelect(key, keyPath) {},
    handleCommand(e) {
      if (e === "logout") {
        this.$confirm("确认退出?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }).then(() => {
          this.logout();
        });
      } else {
        this.visible = true;
      }
    },
    handleOk() {
      let form = this.$refs.modifyForm;
      form.$refs.ruleForm.validate((valid) => {
        if (valid) {
          let formData = new FormData();
          formData.append("newPassword", form.ruleForm.checkPass);
          formData.append("oldPassword", form.ruleForm.originalpwd);
          modifyPwd(formData).then((res) => {
            if (res.success) {
              this.visible = false;
              this.$message.success("修改成功");
            } else {
              this.$message.error(res.msg);
            }
          });
        } else {
          return;
        }
      });
    },
    handleCancel() {
      this.visible = false;
    },
  },
};
</script>
<style lang="less" scoped>
.main,
.el-container,
.tac {
  height: 100%;
}
.el-menu-vertical-demo {
  height: 100%;
}
.el-main{
  padding-right: 40px!important;
}
.main .el-header {
  // position: fixed;
  // top: 0;
  width: 100%;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  padding: 0 20px;
  background-color: #323232;
  z-index: 99;
  background-image: url("../assets/header-bg.jpg");
  background-repeat: no-repeat;
  background-size: 100% 100%;
}
.top-menu {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  height: 60px;
  color: #9fa5b9;
  .menu-links {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-flex: 1;
    -ms-flex: 1;
    flex: 1;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    justify-content: space-between;
    color: #c4cadd;
    .top-logo-img {
      width: 60px;
      // height: 26px;
      margin-left: 15px;
    }
  }
}
</style>

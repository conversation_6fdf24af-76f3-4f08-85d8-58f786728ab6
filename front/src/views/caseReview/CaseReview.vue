<template>
  <div class="table-box">
    <h3 class="title">案件审核</h3>
    <el-divider></el-divider>
    <el-table
      v-loading="loading"
      ref="singleTable"
      :data="tableData"
      @current-change="handleCurrentChange"
      :row-class-name="tableRowClassName"
      style="width: 100%"
    >
      <el-table-column property="lawCase.name" label="案件名称">
      </el-table-column>
      <el-table-column
        align="left"
        :formatter="typeFormat"
        label="类型"
        prop="lawCase.type"
      >
      </el-table-column>
      <el-table-column property="lawCase.caseProgram.name" label="审理程序">
        <!-- <template slot-scope="scope">
            <span>123</span>
          </template> -->
      </el-table-column>
      <el-table-column property="lawCase.caseCause.name" label="案由">
      </el-table-column>
      <el-table-column property="lawCase.entrustPersonNames" label="委托人">
      </el-table-column>
      <el-table-column property="lawCase.concernPersonNames" label="其他当事人">
      </el-table-column>
      <el-table-column property="lawCase.stageName" label="办案流程">
      </el-table-column>
      <el-table-column property="lawCase.stageName" label="是否已审核">
        <template slot-scope="scope">
          <span v-if="scope.row.lawCase.auditStatus === '0'">审核拒绝</span>
          <span v-if="scope.row.lawCase.auditStatus === '1'">待审核</span>
          <span v-if="scope.row.lawCase.auditStatus === '2'">审核通过</span>
          <span v-if="scope.row.lawCase.auditStatus === '3'">待提交</span>
        </template>
      </el-table-column>
      <el-table-column property="todo" label="待办事项">
        <template slot-scope="scope">
          <div v-if="scope.row.todoInfoList.length === 1">
            <span class="mr10">{{ scope.row.todoInfoList[0].startDate }}</span>
            <span>{{ scope.row.todoInfoList[0].title }}</span>
          </div>
          <div v-if="scope.row.todoInfoList.length > 1">
            <el-tooltip placement="top">
              <div slot="content">
                <div
                  v-for="(item, index) in scope.row.todoInfoList"
                  :key="item.id"
                >
                  <span class="mr10">{{ item.startDate }}</span>
                  <span>{{ item.name }}</span>
                </div>
              </div>
              <span>{{ scope.row.todoInfoList.length }}条</span>
            </el-tooltip>
          </div>
        </template>
      </el-table-column>
      <el-table-column property="lawCase.acceptUnitArea" label="受理单位">
      </el-table-column>
      <!-- <el-table-column label="操作" fixed="right">
        <template slot-scope="scope">
          <div @click.stop=""></div>
        </template>
      </el-table-column> -->
       <el-table-column label="操作">
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="handleCurrentChange(scope.row)"
            >审核</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <div class="block" style="margin-top: 15px">
      <el-pagination
        background
        align="center"
        @size-change="handleSizeChange"
        @current-change="handleCurrentSizeChange"
        :current-page="currentPage"
        :page-sizes="[1, 5, 10, 20]"
        :page-size="pageSize"
        layout="prev, pager, next"
        :total="total"
      >
      </el-pagination>
    </div>
  </div>
</template>

<script>
import {
  getList
} from "@/api/case/manage";
import storage from "store";
const dictList = storage.get("dictList");
export default {
  name: "caseReview",
  data() {
    return {
      tableData: [],
      total: 0,
      currentPage: 1, // 当前页码
      pageSize: 10, // 每页的数据条数
      caseTypeList: dictList.case_type,

    };
  },
  created() {
    this.getCaseList();
  },
  methods: {
    getCaseList() {
      console.log(this.formLabelAlign);
      this.loading = true;
      let formData = new FormData();
      formData.append("pageNo", this.currentPage);
      formData.append("pageSize", this.pageSize);
      formData.append("auditStatus", '1');
      getList(formData).then((res) => {
        this.loading = false;
        this.tableData = res.page.list;
        this.total = res.page.count;
      });
    },
     //每页条数改变时触发 选择一页显示多少行
    handleSizeChange(val) {
      this.currentPage = 1;
      this.pageSize = val;
    },
    //当前页改变时触发 跳转其他页
    handleCurrentSizeChange(val) {
      this.currentPage = val;
      this.getCaseList();
    },
     typeFormat(type) {
      const typeList = dictList.case_type;
      if (type.lawCase.type) {
        let tp = typeList.filter((item) => type.lawCase.type === item.value);
        return tp[0].label;
      } else {
        return "";
      }
    },
      // 表格行背景颜色
    tableRowClassName({ row, rowIndex }) {
      if (row.lawCase.auditStatus === "0" || row.lawCase.auditStatus === "3") {
        return "nopass-bg";
      } else if (row.lawCase.auditStatus === "2") {
        return "pass-bg";
      } else if (row.lawCase.auditStatus === "1") {
        return "to-audit-bg";
      }
      return "";
    },
    handleCurrentChange(e) {
      storage.set("caseInfo", e.lawCase);
      this.$router.push({ path: "/case/detail", query: { id: e.lawCase.id } });
    },
     filterTag(value, row) {
      console.log(row);
      return row.lawCase.type === value;
    },
  },
};
</script>

<style lang="less" scoped>

.table-box {
  background: #fff;
  padding: 20px;
  color: #111;
  margin-top: 30px;
  /deep/.el-table__row {
    color: #111;
  }
  /deep/.pass-bg {
    background: rgba(216, 252, 216, 0.3);
  }
  /deep/.nopass-bg {
    background: rgba(250, 220, 220, 0.3);
  }
  /deep/ .to-audit-bg {
    background: rgba(248, 248, 222, 0.3);
  }
  .footer-box {
    padding: 20px;
  }
}
</style>
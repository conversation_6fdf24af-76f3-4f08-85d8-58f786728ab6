<template>
  <el-dialog
    :title="model ? '编辑用户' : '新增用户'"
    :visible="visible"
    width="600px"
    :before-close="handleCancel"
    :close-on-press-escape="false"
    :close-on-click-modal="false"
  >
    <el-form
      :model="ruleForm"
      :rules="rules"
      ref="ruleForm"
      label-width="100px"
      class="ruleForm-box"
    >
      <el-row>
        <el-col :span="24">
          <el-form-item label="账号" prop="loginName">
            <el-input
              placeholder="请输入账号"
              v-model="ruleForm.loginName"
              type="text"
            >
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24" v-if="!model">
          <el-form-item label="密码" prop="newPassword">
            <el-input
              placeholder="请输入密码"
              v-model="ruleForm.newPassword"
              type="text"
            >
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24" v-else>
          <el-form-item label="新密码">
            <el-input
              placeholder="请输入新密码"
              v-model="ruleForm.newPassword"
              type="text"
            >
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="用户名称" prop="name">
            <el-input
              placeholder="请输入用户名称"
              v-model="ruleForm.name"
              type="text"
            >
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="用户类型" prop="type">
            <el-select
              v-model="ruleForm.type"
              placeholder="请选择用户类型"
              :disabled="disabled"
              multiple
            >
              <el-option
                :label="item.label"
                :value="item.value"
                v-for="item in caseTypeList"
                :key="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="手机号" prop="mobile">
            <el-input
              placeholder="请输入手机号"
              v-model="ruleForm.mobile"
              type="text"
            >
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="是否可登录" prop="loginFlag">
            <el-select
              style="width: 100%"
              v-model="ruleForm.loginFlag"
              placeholder="请选择"
            >
              <el-option
                :label="item.label"
                :value="item.value"
                v-for="(item, index) in yes_no"
                :key="index"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="备注" prop="remarks">
            <el-input
              placeholder="请输入备注"
              v-model="ruleForm.remarks"
              type="text"
            >
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleCancel">取 消</el-button>
      <el-button type="primary" @click="handleOk('ruleForm')" :loading="loading"
        >提交</el-button
      >
    </span>
  </el-dialog>
</template>

<script>
import { isEmpty, cloneDeep } from "lodash";
import storage from "store";

export default {
  name: "CreateStage",
  props: {
    visible: {
      type: Boolean,
      required: true,
    },
    loading: {
      type: Boolean,
      default: () => false,
    },
    model: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    const dictList = storage.get("dictList");
    return {
      caseTypeList: dictList.user_type,
      yes_no: dictList.yes_no,
      ruleForm: {
        name: "",
        type: [],
        loginFlag: "1",
        remarks: "",
        loginName: "",
        mobile: "",
      },
      rules: {
        name: [{ required: true, message: "请输入用户名称", trigger: "blur" }],
        newPassword: [
          { required: true, message: "请输入密码", trigger: "blur" },
        ],
        loginName: [{ required: true, message: "请输入账号", trigger: "blur" }],
        type: [
          { required: true, message: "请选择用户类型", trigger: "change" },
        ],
      },
      disabled: false,
    };
  },
  mounted() {
    this.$watch(
      "visible",
      (e) => {
        if (e) {
          if (this.model) {
            this.ruleForm = JSON.parse(JSON.stringify(this.model));
            console.log(this.ruleForm);
            this.ruleForm.type = this.ruleForm.type.split(",");
          }
        } else {
          this.ruleForm = {
            name: "",
            type: [],
            loginFlag: "1",
            remarks: "",
            loginName: "",
            mobile: "",
            newPassword: "",
          };
        }
      },
      {
        deep: true,
        immediate: true,
      }
    );
  },
  methods: {
    handleOk() {
      this.$emit("ok", this.ruleForm);
    },
    handleCancel() {
      this.$emit("cancel");
    },
  },
};
</script>

<style>
</style>
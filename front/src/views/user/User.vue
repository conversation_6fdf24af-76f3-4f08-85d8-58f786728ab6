<template>
  <div>
    <div class="block">
      <h3 class="title">用户管理</h3>
      <el-button
        type="primary"
        icon="el-icon-circle-plus-outline"
        @click="() => append('add')"
      >
        新增用户
      </el-button>
      <el-divider></el-divider>
      <el-table :data="tableData" style="width: 100%">
        <el-table-column prop="loginName" label="账号"> </el-table-column>
        <el-table-column prop="name" label="姓名"> </el-table-column>
        <el-table-column prop="mobile" label="手机号"> </el-table-column>
        <el-table-column prop="loginFlag" label="是否可登录">
          <template slot-scope="scope">
            <span>{{ scope.row.loginFlag === "1" ? "是" : "否" }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="type" label="用户类型">
          <template slot-scope="scope">
            <span>{{ scope.row.typeName }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="remarks" label="备注"> </el-table-column>
        <el-table-column fixed="right" label="操作" width="100">
          <template slot-scope="scope">
            <el-button
              @click="append('edit', scope.row)"
              type="text"
              size="small"
              >编辑</el-button
            >
            <el-button type="text" size="small" @click="remove(scope.row)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <div class="block" style="margin-top: 15px">
        <el-pagination
          background
          align="center"
          @size-change="handleSizeChange"
          @current-change="handleCurrentSizeChange"
          :current-page="currentPage"
          :page-sizes="[1, 5, 10, 20]"
          :page-size="pageSize"
          layout="prev, pager, next"
          :total="total"
        >
        </el-pagination>
      </div>
    </div>
    <CreateModal
      ref="createForm"
      :visible="visible"
      :model="model"
      :loading="loading"
      @cancel="handleCancel"
      @ok="handleOk"
    />
  </div>
</template>

<script>
import {
  getList,
  add,
  queryById,
  del,
  allBriefList,
  savePwd,
  check,
} from "@/api/case/user";
import CreateModal from "./modal/CreateModal.vue";
export default {
  name: "User",
  components: {
    CreateModal,
  },
  data() {
    return {
      tableData: [],
      visible: false,
      model: null,
      type: "",
      currentPage: 1, // 当前页码
      pageSize: 10, // 每页的数据条数
      total: 0,
      loading: false,
    };
  },
  mounted() {
    this.getData();
  },
  methods: {
    //每页条数改变时触发 选择一页显示多少行
    handleSizeChange(val) {
      this.currentPage = 1;
      this.pageSize = val;
    },
    //当前页改变时触发 跳转其他页
    handleCurrentSizeChange(val) {
      this.currentPage = val;
      this.getData();
    },
    getData() {
      let formData = new FormData();
      formData.append("pageNo", this.currentPage);
      formData.append("pageSize", this.pageSize);
      getList(formData).then((res) => {
        this.tableData = res.page.list;
        this.tableData.map(item=>{
          item.typeName = ''
          if(item.type.indexOf('1') > -1){
            item.typeName += '管理员，'
          }
          if(item.type.indexOf('2') > -1){
            item.typeName += '主任，'
          }
          if(item.type.indexOf('3') > -1){
            item.typeName += '律师，'
          }
        })
        this.total = res.page.count;
      });
    },
    handleCancel() {
      this.visible = false;
    },
    handleOk() {
      console.log(this.$refs.createForm);
      this.$refs.createForm.$refs.ruleForm.validate((valid) => {
        if (valid) {
          let data = this.$refs.createForm.ruleForm;
          let formData = new FormData();
          formData.append("loginName", data.loginName);
          formData.append("oldLoginName", data.id ? data.loginName : "");
          this.loading = true;
          check(formData).then((ret) => {
            if (ret.result) {
              formData.append("name", data.name);
              formData.append("newPassword", data.newPassword);
              formData.append("mobile", data.mobile);
              formData.append("loginFlag", data.loginFlag);
              formData.append("remarks", data.remarks);
              formData.append("type", data.type);
              data.id && formData.append("id", data.id);
              add(formData).then((res) => {
                this.loading = false;
                this.visible = false;
                if (res.success) {
                  this.$message({
                    showClose: true,
                    message: res.msg,
                    type: "success",
                  });
                  this.getData();
                } else {
                  this.$message.error(res.msg);
                }
              });
            } else {
              this.loading = false;
              this.$message.error("该账号已存在，请重新输入");
            }
          });
          return;
        } else {
          return false;
        }
      });
    },
    append(type, data) {
      if (type === "add") {
        this.model = null;
      } else {
        this.model = data;
      }
      this.visible = true;
    },
    remove(data) {
      this.$confirm("确定删除该用户?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          let param = new FormData();
          param.append("ids", data.id);
          del(param).then((res) => {
            if (res.success) {
              this.getData();
              this.$message({
                type: "success",
                message: "删除成功!",
              });
            } else {
              this.$message.error(res.msg);
            }
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
  },
};
</script>

<style lang="less" scoped>
.block {
  padding: 20px;
  background: #fff;
  .title {
    padding: 20px 0;
  }
  /deep/.el-tree-node__content {
    padding: 5px 0;
  }
  .custom-tree-node {
    width: 50%;
    display: flex;
    //   justify-content: space-between;
    .label {
      margin-right: 30px;
    }
  }
}
</style>
<template>
  <el-dialog
    :title="model ? '编辑定时任务' : '新增定时任务'"
    :visible="visible"
    width="600px"
    :before-close="handleCancel"
    :close-on-press-escape="false"
    :close-on-click-modal="false"
  >
    <el-form
      :model="ruleForm"
      :rules="rules"
      ref="ruleForm"
      label-width="130px"
      class="ruleForm-box"
    >
      <el-row>
        <el-col :span="24">
          <el-form-item label="任务名" prop="name">
            <el-input
              placeholder="请输入任务名"
              v-model="ruleForm.name"
              type="text"
            >
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="任务组" prop="group">
            <el-select
              v-model="ruleForm.group"
              placeholder="请选择任务组"
            >
              <el-option
                :label="item.label"
                :value="item.value"
                v-for="item in groupList"
                :key="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="定时规则" prop="cronExpression">
            <el-input
              placeholder="请输入定时规则"
              v-model="ruleForm.cronExpression"
              type="text"
            >
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="是否启用" prop="status">
            <el-select
              v-model="ruleForm.status"
              placeholder="请选择启用状态"
            >
              <el-option
                :label="item.label"
                :value="item.value"
                v-for="item in statusList"
                :key="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
         <el-col :span="24">
          <el-form-item label="任务类" prop="className">
            <el-input
              placeholder="请输入任务类"
              v-model="ruleForm.className"
              type="text"
            >
            </el-input>
          </el-form-item>
        </el-col>
        
        <el-col :span="24">
          <el-form-item label="描述" prop="description">
            <el-input
              placeholder="请输入描述"
              v-model="ruleForm.description"
              type="textarea"
            >
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleCancel">取 消</el-button>
      <el-button type="primary" @click="handleOk('ruleForm')">提交</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { isEmpty, cloneDeep } from "lodash";
import storage from "store";

export default {
  name: "CreateStage",
  props: {
    visible: {
      type: Boolean,
      required: true,
    },
    type: {
      type: String,
    },
    loading: {
      type: Boolean,
      default: () => false,
    },
    model: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    const dictList = storage.get("dictList");
    const baseUrl = process.env.VUE_APP_API_BASE_URL;
    return {
      groupList: dictList.schedule_task_group,
      statusList: dictList.yes_no,
      task_infoList: dictList.schedule_task_info,
      ruleForm: {
        number: "",
        cronExpression: '0/5 * * * * ?',
        group: '',
        status: "0",
        className: '',
        description: ''
      },
      rules: {
        number: [{ required: true, message: "请输入版本号", trigger: "blur" }],
        fileList: [{ required: true, message: "请上传安装包", trigger: "change" }],
      }
    };
  },
  mounted() {
    this.$watch(
      "visible",
      (e) => {
        if (e) {
          if (this.model) {
            this.ruleForm = JSON.parse(JSON.stringify(this.model));
          }
        } else {
          this.disabled = false;
          this.ruleForm = {
            number: "",
            cronExpression: '0/5 * * * * ?',
            group: '',
            status: "0",
            className: 'com.jeeplus.modules.lawcase.task.TodoInfoTask',
            description: ''
          };
        }
      },
      {
        deep: true,
        immediate: true,
      }
    );
  },
  methods: {
    handleOk() {
      this.$emit("ok", this.ruleForm);
    },
    handleCancel() {
      this.$emit("cancel");
    }
  },
};
</script>

<style>
</style>
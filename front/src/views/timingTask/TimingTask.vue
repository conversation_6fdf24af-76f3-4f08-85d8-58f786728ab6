<template>
  <div class="block">
    <h3 class="title">定时任务</h3>
    <el-button
      type="primary"
      icon="el-icon-circle-plus-outline"
      @click="() => append('add')"
    >
      新增
    </el-button>
    <el-divider></el-divider>
    <el-table
      v-loading="loading"
      ref="singleTable"
      :data="tableData"
      style="width: 100%"
    >
      <el-table-column property="name" label="任务名"> </el-table-column>
      <el-table-column property="group" label="任务组">
        <template slot-scope="scope">
          <span>{{ formartGroup(scope.row.group) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        property="cronExpression"
        label="定时规则"
      ></el-table-column>
      <el-table-column label="是否启用"  width="100">
        <template slot-scope="scope">
          <span>{{ scope.row.status === "1" ? "是" : "否" }}</span>
        </template>
      </el-table-column>
      <el-table-column property="className" label="任务类"></el-table-column>
      <el-table-column label="操作" width="250">
        <template slot-scope="scope">
          <el-button @click="append('edit', scope.row)" type="text" size="small"
            >编辑</el-button
          >
          <el-button type="text" size="small" @click="remove(scope.row)"
            >删除</el-button
          >
          <el-tag @click="runOnce(scope.row)" style="margin-left: 10px">立即运行一次</el-tag>
          <el-tag
            type="success"
            style="margin-left: 10px"
            v-if="scope.row.status === '0'"
            @click="onStart(scope.row)"
            >启动</el-tag
          >
          <el-tag
            type="danger"
            style="margin-left: 10px"
            v-if="scope.row.status === '1'"
            @click="onStop(scope.row)"
            >暂停</el-tag
          >
        </template>
      </el-table-column>
    </el-table>
    <div class="block" style="margin-top: 15px">
      <el-pagination
        background
        align="center"
        @size-change="handleSizeChange"
        @current-change="handleCurrentSizeChange"
        :current-page="currentPage"
        :page-sizes="[1, 5, 10, 20]"
        :page-size="pageSize"
        layout="prev, pager, next"
        :total="total"
      >
      </el-pagination>
    </div>
    <CreateModal
      ref="createForm"
      :visible="visible"
      :model="model"
      @cancel="handleCancel"
      @ok="handleOk"
    />
  </div>
</template>

<script>
import { getList, save, del, resume, stop, startNow } from "@/api/task/task";
import CreateModal from "./modal/CreateModal.vue";
import storage from "store";
import moment from "moment";
export default {
  name: "version",
  components: {
    CreateModal,
  },
  data() {
    const dictList = storage.get("dictList");
    return {
      groupList: dictList.schedule_task_group,
      tableData: [],
      total: 0,
      currentPage: 1, // 当前页码
      pageSize: 10, // 每页的数据条数
      visible: false,
      model: null,
    };
  },
  created() {
    this.getTaskList();
  },
  methods: {
    formartGroup(group){
      return this.groupList.filter(item=>item.value === group)[0].label
    },
    getTaskList() {
      this.loading = true;
      let formData = new FormData();
      formData.append("pageNo", this.currentPage);
      formData.append("pageSize", this.pageSize);
      getList(formData).then((res) => {
        this.loading = false;
        this.tableData = res.page.list;
        this.total = res.page.count;
      });
    },
    //每页条数改变时触发 选择一页显示多少行
    handleSizeChange(val) {
      this.currentPage = 1;
      this.pageSize = val;
    },
    //当前页改变时触发 跳转其他页
    handleCurrentSizeChange(val) {
      this.currentPage = val;
      this.getCaseList();
    },
    append(type, data) {
      this.type = type;
      if (type === "add") {
        this.model = null;
      } else {
        this.model = data;
      }
      this.visible = true;
    },
    handleCancel() {
      this.visible = false;
    },
    handleOk() {
      this.$refs.createForm.$refs.ruleForm.validate((valid) => {
        if (valid) {
          let data = this.$refs.createForm.ruleForm;
          let formData = new FormData();
          formData.append("name", data.name);
          formData.append("group", data.group);
          formData.append("cronExpression", data.cronExpression);
          formData.append("status", data.status);
          formData.append("className", data.className);
          formData.append("description", data.description);
          formData.append("id", data.id || "");
          save(formData).then((res) => {
            this.visible = false;
            if (res.success) {
              this.$message({
                showClose: true,
                message: res.msg,
                type: "success",
              });
              this.getTaskList();
            } else {
              this.$message.error(res.msg);
            }
          });
        } else {
          return false;
        }
      });
    },
    remove(data) {
      this.$confirm("确定删除该任务?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          let param = new FormData();
          param.append("ids", data.id);
          del(param).then((res) => {
            if (res.success) {
              this.getTaskList();
              this.$message({
                type: "success",
                message: "删除成功!",
              });
            } else {
              this.$message.error(res.msg);
            }
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    onStart(data) {
      let param = new FormData();
      param.append("id", data.id);
      resume(param).then((res) => {
        if (res.success) {
          this.getTaskList();
          this.$message({
            type: "success",
            message: "启动成功!",
          });
        } else {
          this.$message.error(res.msg);
        }
      });
    },
    onStop(data) {
      let param = new FormData();
      param.append("id", data.id);
      stop(param).then((res) => {
        if (res.success) {
          this.getTaskList();
          this.$message({
            type: "success",
            message: "暂停成功!",
          });
        } else {
          this.$message.error(res.msg);
        }
      });
    },
    runOnce() {
      let param = new FormData();
      param.append("id", data.id);
      startNow(param).then((res) => {
        if (res.success) {
          this.getTaskList();
          this.$message({
            type: "success",
            message: "运行成功!",
          });
        } else {
          this.$message.error(res.msg);
        }
      });
    },
  },
};
</script>

<style lang="less" scoped>
.block {
  padding: 20px;
  background: #fff;
  .title {
    padding: 20px 0;
  }
  /deep/.el-tree-node__content {
    padding: 5px 0;
  }
  .custom-tree-node {
    width: 50%;
    display: flex;
    //   justify-content: space-between;
    .label {
      margin-right: 30px;
    }
  }
}
</style>
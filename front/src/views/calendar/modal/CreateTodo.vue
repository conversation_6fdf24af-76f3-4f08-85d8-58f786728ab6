<template>
  <el-dialog
    :title="todoId ? '编辑待办' : '新增待办'"
    :visible="visible"
    width="600px"
    :before-close="handleCancel"
    :close-on-press-escape="false"
    :close-on-click-modal="false"
  >
    <el-form
      :model="ruleForm"
      ref="ruleForm"
      class="ruleForm-box"
      v-loading="loading"
    >
      <el-row :gutter="20" class="flex align-center">
        <el-col :span="20">
          <el-input
            placeholder="请输入工作摘要"
            v-model.number="ruleForm.name"
            type="textarea"
            maxlength="100"
            show-word-limit
            readonly
          >
          </el-input>
        </el-col>
        <el-col :span="4">
          <el-checkbox v-model="ruleForm.checked" disabled>设为已办</el-checkbox>
        </el-col>
      </el-row>
      <el-row class="mg10">
        <el-col :span="24">
          <div class="mg10 form-label">
            <span>工作详情</span>
          </div>
          <el-input
            type="textarea"
            :autosize="{ minRows: 4 }"
            placeholder="请输入内容"
            v-model="ruleForm.content"
            maxlength="500"
            show-word-limit
            readonly
          >
          </el-input>
        </el-col>
      </el-row>
      <el-row class="mg10 flex align-center" :gutter="10">
        <el-col :span="12">
          <div class="block flex align-center">
            <div class="mg10 form-label">
              <i class="el-icon-time mr10"></i>
              <span>开始:</span>
            </div>
            <el-date-picker
              v-model="ruleForm.startDate"
              type="datetime"
              placeholder="选择开始时间"
              value-format="yyyy-MM-dd HH:mm:ss"
              disabled
            >
            </el-date-picker>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="block flex align-center">
            <div class="mg10 form-label">
              <i class="el-icon-time mr10"></i>
              <span>结束:</span>
            </div>
            <el-date-picker
              v-model="ruleForm.endDate"
              type="datetime"
              placeholder="选择结束时间"
              value-format="yyyy-MM-dd HH:mm:ss"
              disabled
            >
            </el-date-picker>
          </div>
        </el-col>
        <!-- <el-col :span="4">
          <el-checkbox v-model="ruleForm.allDay">全天</el-checkbox>
        </el-col> -->
      </el-row>
      <el-row :gutter="10">
        <el-col :span="12">
          <div class="block flex align-center">
            <div class="mg10 form-label">
              <i class="el-icon-bell mr10"></i>
              <span>提醒:</span>
            </div>
            <el-date-picker
              v-model="ruleForm.remindDate"
              type="datetime"
              placeholder="选择提醒时间"
              value-format="yyyy-MM-dd HH:mm:ss"
              disabled
            >
            </el-date-picker>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="block flex align-center">
            <div class="mg10 form-label">
              <i class="el-icon-time mr10"></i>
              <span>耗时:</span>
            </div>
            <el-input placeholder="请输入" v-model="ruleForm.consumeTime" readonly>
              <template slot="append">分钟</template>
            </el-input>
          </div>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <!-- <div class="block flex align-center">
            <div class="mg10 form-label cursor" @click="uploadFile">
              <i class="el-icon-paperclip mr10"></i>
              <span>点击上传附件</span>
            </div>
          </div> -->
          <el-upload
            class="upload-demo"
            :action="uploadUrl"
            :on-success="handleUpload"
            :on-remove="handleRemove"
            :data="{ uploadPath: '/lawcase/todoFile/' }"
            multiple
            :limit="5"
            :headers="header"
            :file-list="ruleForm.fileList"
          >
            <!-- <div class="mg10 form-label cursor">
              <i class="el-icon-paperclip mr10"></i>
              <span>点击上传附件</span>
            </div> -->
          </el-upload>
        </el-col>
      </el-row>
      <el-row class="mg10" :gutter="20">
        <el-col :span="16">
          <div class="block align-center">
            <div class="mg10">关联案件/项目/客户</div>
            <el-select
              style="width: 30%"
              v-model="ruleForm.relevanceType"
              placeholder="请选择"
              @change="selectRelevanceType($event)"
              disabled
            >
              <el-option
                :label="item.label"
                :value="item.value"
                v-for="item in todo_relevance_type"
                :key="item.id"
              ></el-option>
            </el-select>
            <el-select
              style="width: 70%"
              v-model="ruleForm.client"
              placeholder="请选择客户"
              v-if="ruleForm.relevanceType === '1'"
              disabled
            >
              <el-option
                :label="item.customer.name"
                :value="item.customer.id"
                v-for="item in customerList"
                :key="item.id"
              ></el-option>
            </el-select>
            <el-select
              style="width: 70%"
              v-model="ruleForm.caseId"
              placeholder="请选择案件"
              v-else
              disabled
              @change="selectCase"
            >
              <el-option
                :label="item.name"
                :value="item.id"
                v-for="item in allCaseList"
                :key="item.id"
              ></el-option>
            </el-select>
          </div>
        </el-col>
        <el-col :span="8" v-if="ruleForm.relevanceType === '2'">
          <div class="block align-center">
            <div class="mg10">案件阶段</div>
            <el-select
              style="width: 100%"
              v-model="ruleForm.stage.id"
              placeholder="请选择"
              @change="handleChange"
              disabled
            >
              <el-option
                :label="item.name"
                :value="item.id"
                v-for="item in stageList"
                :key="item.id"
              ></el-option>
            </el-select>
          </div>
        </el-col>
      </el-row>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleCancel">取 消</el-button>
      <!-- <el-button type="primary" @click="handleOk('ruleForm')">提交</el-button>
      <el-button type="warning" @click="handleDelete" v-if="todoId"
        >删除</el-button
      > -->
    </span>
  </el-dialog>
</template>

<script>
import dayjs from "dayjs";
import storage from "store";
import { allCaseData } from "@/api/case/manage";
import { getList } from "@/api/customer/customer";
import { queryTodoById } from "@/api/case/todo";
import { getStageList } from "@/api/case/stage";

export default {
  name: "CopyCase",
  props: {
    visible: {
      type: Boolean,
      required: true,
    },
    todoId: {
      type: String,
    },
    selectStage: {
      type: Object,
    },
    date: {
      type: String,
    },
  },
  data() {
    const dictList = storage.get("dictList");
    const baseUrl = process.env.VUE_APP_API_BASE_URL;

    return {
      todo_relevance_type: dictList.todo_relevance_type,
      ruleForm: {
        stage: {
          id: "",
          name: "",
        },
        endDate: "",
        startDate: dayjs(new Date()).format("YYYY-MM-DD HH:mm:ss"),
        content: "",
        name: "",
        consumeTime: "",
        checked: false,
        fileList: [],
        remindDate: "",
        caseId: "",
        relevanceType: "0",
        client: "",
        hostUser: {},
        parent: {},
      },

      allCaseList: [],
      customerList: [],
      header: {
        token: storage.get("token"),
      },
      uploadUrl: `${baseUrl ? baseUrl : ""}/law/sys/file/webupload/upload`,
      disbaled: true,
      stageList: [],
      loading: false,
    };
  },
  mounted() {
    this.id = this.$route.query.id;
    this.$watch("todoId", (e) => {
      if (e) {
        this.getTodoById();
      } else {
        this.ruleForm = {
          stage: {
            id: "",
            name: "",
          },
          endDate: "",
          startDate: dayjs(new Date()).format("YYYY-MM-DD HH:mm:ss"),
          content: "",
          name: "",
          consumeTime: "",
          checked: false,
          fileList: [],
          remindDate: "",
          caseId: "",
          relevanceType: "0",
          client: "",
          hostUser: {},
          parent: {},
          relevanceId: ''
        };
      }
    });
    this.$watch("visible", () => {
      this.getAllCaseList();
      this.getCustomerList();

      if (this.visible && !this.todoId) {
        this.ruleForm.startDate = this.date;
      }
    });
  },
  methods: {
    getTodoById() {
      this.loading = true;
      let formData = new FormData();
      formData.append("id", this.todoId);
      queryTodoById(formData).then((res) => {
        this.loading = false;
        this.ruleForm = res.todoInfo;
        this.ruleForm.checked = this.ruleForm.status==='2'?true:false
        if (this.ruleForm.relevanceType === "0") {
          this.disbaled = true;
        } else if(this.ruleForm.relevanceType === "1"){
          this.ruleForm.client = res.todoInfo.relevanceId
          this.disbaled = false;
        }else{
          this.ruleForm.caseId = res.todoInfo.relevanceId
          this.disbaled = false;
          this.selectCase(res.todoInfo.relevanceId)
        }
      });
    },
    selectRelevanceType(e) {
      if (this.ruleForm.relevanceType === "2") {
        this.ruleForm.client = "";
        this.ruleForm.stage = {
          id: "",
          name: "",
        };
        this.disbaled = false;
      } else if (this.ruleForm.relevanceType === "1") {
        this.disbaled = false;
        this.ruleForm.case = "";
      } else {
        this.disbaled = true;
        this.ruleForm.case = "";
        this.ruleForm.client = "";
      }
    },
    //选择案件 获取待办列表
    selectCase(e) {
      let formData = new FormData();
      formData.append("lawCase.id", e);
      getStageList(formData).then((res) => {
        this.stageList = res.page.list;
      });
    },
    // 上传文件
    handleUpload(response, file, fileList) {
      this.handleFileChange(response, fileList);
    },
    handleFileChange(response, fileList) {
      if (response.success || response.status === "success") {
        if (fileList.length) {
          this.ruleForm.fileList = [];
          fileList.map((item) => {
            this.ruleForm.fileList.push({
              createDate: item.createDate || "",
              name: item.name,
              path: item.path ? item.path : item.response.url,
            });
          });
        } else {
          this.ruleForm.fileList = [];
        }
      } else {
        this.$message.error("上传失败");
      }
    },
    handleRemove(file, fileList) {
      this.handleFileChange(file, fileList);
    },
    // 获取所有客户
    getCustomerList() {
      getList().then((res) => {
        this.customerList = res.page.list;
      });
    },
    // 获取所有通过审核案件列表
    getAllCaseList() {
      allCaseData().then((res) => {
        this.allCaseList = res.data;
      });
    },
    handleChange(e) {
      this.stageList.map((item) => {
        if (item.id === e) {
          this.ruleForm.stage.name = item.name;
        }
      });
    },
    handleOk(formName) {
      this.$emit("ok");
      // this.ruleForm = {
      //   stage: {
      //     id: "",
      //     name: "",
      //   },
      //   endDate: "",
      //   startDate: dayjs(new Date()).format("YYYY-MM-DD HH:mm:ss"),
      //   content: "",
      //   name: "",
      //   consumeTime: "",
      //   checked: false,
      //   parent: "",
      //   fileList: []
      // };
    },
    handleDelete() {
      this.$emit("delete");
    },
    handleCancel() {
      this.$emit("cancel");
    },
  },
};
</script>

<style lang="less" scoped>
.form-label {
  min-width: 70px;
  color: #888;
}
</style>
<template>
  <div class="flex" style="background: #fff">
    <div class="left">
      <div class="flex justify-between">
        <div class="title">待办清单</div>
        <!-- <div class="cursor icon-wrap">
          <i class="el-icon-s-fold" style="font-size: 20px"></i>
          <span>收起</span>
        </div> -->
      </div>
      <div class="todo-second">
        <span class="txt">已过期</span>
        <i
          :class="['el-icon-arrow-down cursor', !isOpen ? 'rotate-down' : '']"
          style="font-size: 18px"
          @click="toggleOpen"
        ></i>
      </div>
      <div v-if="isOpen">
        <div class="" v-for="(item, index) in overList" :key="item.id" @click="handleShowTodoModal('edit', item)">
          <div class="list-item">
            <div class="right-title text-ellipsis">{{ item.name }}</div>
            <!-- <div class="right-txt  text-ellipsis">{{item.name}}</div> -->
            <div class="right-time text-ellipsis">{{ item.startDate }}</div>
          </div>
        </div>
      </div>
      <div class="todo-second">
        <span class="txt">待计划</span>
        <!-- <i class="el-icon-plus cursor" style="font-size: 18px"></i> -->
      </div>
      <div>
        <div class="" v-for="(item, index) in planList" :key="item.id" @click="handleShowTodoModal('edit', item)">
          <div class="list-item">
            <div class="right-title text-ellipsis">{{ item.name }}</div>
          </div>
        </div>
      </div>
    </div>
    <div class="right">
      <div class="flex justify-center align-center mg10">
        <i class="el-icon-caret-left mr20 cursor" @click="handlePrev"></i>
        <h3 class="mr20">{{ currentMonth }}</h3>
        <i class="el-icon-caret-right" @click="handleNext"></i>
      </div>

      <full-calendar
        :events="events"
        :config="config"
        ref="calendar"
        @changeMonth="changeMonth"
      ></full-calendar>
      <CreateTodo
        ref="todoForm"
        :visible="todoVisible"
        @cancel="handleTodoCancel"
        @ok="handleTodoOk"
        @delete="handleDeleteTodo"
        :date="date"
        :todoId="todoId"
      />
    </div>
  </div>
</template>

<script>
import { getAllDict } from "@/api/login";
import { getMonthList, statisticList } from "@/api/calendar/calendar";
import storage from "store";
import { FullCalendar } from "vue-full-calendar";
import CreateTodo from "./modal/CreateTodo.vue";
import "fullcalendar/dist/fullcalendar.css";
import moment from "moment";

import {
  todoList,
  saveTodo,
  delTodo,
  deleteFile,
  queryTodoById,
  customerList,
  fileList,
  changeStatus,
  fileUpload,
} from "@/api/case/todo";

export default {
  name: "Calendar",
  components: {
    FullCalendar,
    CreateTodo,
  },
  data() {
    return {
      visiable: true,
      config: {
        locale: "zh-cn",
        defaultView: "month",
        header: false,
        editable: false, // 禁止拖动
        /* 设置按钮文字 */
        buttonText: {
          today: "今天/本周",
        },
        aspectRatio: 2.4,
        eventClick: this.eventClick, //点击事件
        dayClick: this.dayClick, //点击日程表上面某一天
        changeMonth: this.changeMonth,
      },
      events: [],
      todoVisible: false,
      selectStage: null,
      currentMonth: moment(new Date()).format("YYYY年MM月"),
      date: "",
      todoId: "",
      isExpanded: false,
      overList: [],
      planList: [],
      isOpen: false,
    };
  },
  mounted() {
    this.getAllDict();
    this.getMonthList();
    this.getStatisticList();
  },
  methods: {
    toggleOpen() {
      this.isOpen = !this.isOpen;
    },
    // 获取统计列表
    getStatisticList() {
      statisticList().then((res) => {
        this.overList = res.overdueData;
        this.planList = res.planData;
      });
    },
    changeMonth(start, end, current) {
      console.log(
        "changeMonth",
        start.format(),
        end.format(),
        current.format()
      );
    },
    // 前一个月
    handlePrev() {
      this.$refs.calendar.fireMethod("prev");
      this.getMonthList();
    },
    // 下一个月
    handleNext() {
      this.$refs.calendar.fireMethod("next");
      this.getMonthList();
    },
    // 获取月度待办列表
    getMonthList() {
      let viewDate = this.$refs.calendar.fireMethod("getView").dateProfile.date;
      this.currentMonth = moment(viewDate).format("YYYY年MM月");
      let startDate = moment(viewDate).startOf("month").format("YYYY-MM-DD");
      let endDate = moment(viewDate).endOf("month").format("YYYY-MM-DD");
      let formData = new FormData();
      formData.append("queryStartDate", startDate);
      formData.append("queryEndDate", endDate);
      getMonthList(formData).then((res) => {
        this.events = res.data;
        this.events.map((item) => {
          item.title = item.name;
          item.start = item.remindDate;
          item.end = item.endDate;
        });
      });
    },
    getAllDict() {
      getAllDict().then((res) => {
        storage.set("dictList", res.dictList);
      });
    },
    // 点击事件
    eventClick(event, jsEvent, pos) {
      this.handleShowTodoModal("edit", event);
    },
    // 点击当天
    dayClick(day, jsEvent) {
      this.date = moment(day).format("YYYY-MM-DD 09:00:00");
      this.handleShowTodoModal("add");
    },
    // 显示新增待办弹框
    handleShowTodoModal(type, data) {
      this.todoType = type;
      if (type === "edit") {
        this.selectStage = null;
        this.todoId = data.id;
      } else if (type === "add") {
        this.selectStage = null;
        this.todoId = "";
      }
      this.todoVisible = true;
    },
    // 确认新增待办
    handleTodoOk() {
      let data = this.$refs.todoForm.ruleForm;
      const {
        name,
        consumeTime,
        content,
        startDate,
        endDate,
        stage,
        checked,
        fileList,
        remindDate,
        relevanceType,
        caseId,
        client,
        parent,
        hostUser,
      } = data;
      let relevanceId;
      if (relevanceType === "0") {
        relevanceId = "";
      } else if (relevanceType === "1") {
        relevanceId = client;
      } else {
        relevanceId = caseId;
      }

      const param = {
        id: data.id,
        parent,
        name,
        consumeTime,
        content,
        startDate,
        endDate,
        status: checked ? "2" : "1",
        relevanceType,
        relevanceId,
        fileList,
        remindDate,
        stage: relevanceType !== "0" ? stage : "",
        hostUser,
      };
      if (!name) {
        return this.$message.error("请输入工作摘要");
      }
      if (!startDate) {
        return this.$message.error("请选择开始时间");
      }
      if (relevanceType === "1" && !relevanceId) {
        return this.$message.error("请选择客户");
      }
      if (relevanceType === "2" && !caseId) {
        return this.$message.error("请选择案件");
      }
      if (relevanceType === "2" && !stage.id) {
        return this.$message.error("请选择案件阶段");
      }

      saveTodo(param).then((res) => {
        if (res.success) {
          this.$message({
            showClose: true,
            message: res.msg,
            type: "success",
          });
          this.getMonthList();
        } else {
          this.$message.error(res.msg);
        }
      });
      this.todoVisible = false;
    },
    // 删除待办记录
    handleDeleteTodo() {
      this.$confirm("此操作将永久删除该待办记录, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          let formData = new FormData();
          formData.append("ids", this.todoId);
          delTodo(formData).then((res) => {
            this.todoVisible = false;
            if (res.success) {
              this.getMonthList();
              this.$message({
                type: "success",
                message: "删除成功!",
              });
            }
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    // 关闭新增待办弹框
    handleTodoCancel() {
      this.todoVisible = false;
    },
  },
};
</script>

<style lang="less" scoped>
/deep/.fc-day-grid-container {
  height: 80vh !important;
}
/deep/.fc-basic-view .fc-body .fc-row {
  min-height: 8em;
}
.left {
  width: 16%;
  border-right: 1px solid #e1e4ee;
  padding: 20px;
  .title {
    color: #303443;
    font-size: 16px;
  }
  .icon-wrap {
    color: #9da3b2;
  }
  .el-icon-arrow-down {
    cursor: pointer;
    color: #858896;
    font-size: 9px;
    -webkit-transition: all 0.3s;
    transition: all 0.3s;
    -webkit-transform: scale(0.8);
    transform: scale(0.8);
  }
  .rotate-down {
    transform: rotate(180deg) !important;
  }
  .todo-second {
    height: 28px;
    line-height: 28px;
    border-radius: 6px;
    background: #f2f4f9;
    padding: 0 10px;
    font-size: 12px;
    color: #858896;
    margin-top: 20px;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
}
.list-item {
    padding: 0 10px;
    cursor: pointer;
    margin-top: 20px;
}
.list-item .right-title {
  font-size: 15px;
  color: #303443;
  line-height: 1;
}
.list-item  .right-time {
  color: #c2c4d3;
  font-size: 12px;
  padding-top: 8px;
}
.right {
  flex: 1;
}
</style>
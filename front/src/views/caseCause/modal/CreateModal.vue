<template>
  <el-dialog
    :title="model ? '编辑案由' : '新增案由'"
    :visible="visible"
    width="600px"
    :before-close="handleCancel"
    :close-on-press-escape="false"
    :close-on-click-modal="false"
  >
    <el-form
      :model="ruleForm"
      :rules="rules"
      ref="ruleForm"
      label-width="130px"
      class="ruleForm-box"
    >
      <el-row>
        <el-col :span="24">
          <el-form-item label="案由名称" prop="name">
            <el-input
              placeholder="请输入案由名称"
              v-model="ruleForm.name"
              type="text"
            >
            </el-input>
          </el-form-item>
        </el-col>
         <el-col :span="24">
          <el-form-item label="排序" prop="sort">
            <el-input
              placeholder="请输入排序"
              v-model="ruleForm.sort"
              type="number"
            >
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleCancel">取 消</el-button>
      <el-button type="primary" @click="handleOk('ruleForm')">提交</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { isEmpty, cloneDeep } from "lodash";
export default {
  name: "CreateStage",
  props: {
    visible: {
      type: Boolean,
      required: true,
    },
    type: {
      type: String,
    },
    loading: {
      type: Boolean,
      default: () => false,
    },
    model: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      ruleForm: {
        name: "",
        sort: ''
      },
      rules: {
        name: [{ required: true, message: "请输入案由名称", trigger: "blur" }],
      },
    };
  },
  mounted() {
    this.$watch(
      "visible",
      (e) => {
        if (e) {
          if (this.model) {
            this.ruleForm = JSON.parse(JSON.stringify(this.model));
            if (this.type === "addChild") {
              this.ruleForm.name = "";
            }
          }
        } else {
          this.ruleForm = {
            name: "",
          };
        }
      },
      {
        deep: true,
        immediate: true,
      }
    );
  },
  methods: {
    handleOk() {
      this.$emit("ok", this.ruleForm);
    },
    handleCancel() {
      this.$emit("cancel");
    },
  },
};
</script>

<style>
</style>
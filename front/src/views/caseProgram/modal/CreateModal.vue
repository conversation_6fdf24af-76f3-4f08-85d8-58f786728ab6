<template>
  <el-dialog
    :title="model ? '编辑审理程序' : '新增审理程序'"
    :visible="visible"
    width="600px"
    :before-close="handleCancel"
    :close-on-press-escape="false"
    :close-on-click-modal="false"
  >
    <el-form
      :model="ruleForm"
      :rules="rules"
      ref="ruleForm"
      label-width="130px"
      class="ruleForm-box"
    >
      <el-row>
        <el-col :span="24">
          <el-form-item label="审理程序名称" prop="name">
            <el-input
              placeholder="请输入审理程序名称"
              v-model="ruleForm.name"
              type="text"
            >
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="案件类型" prop="type">
            <el-select
              v-model="ruleForm.type"
              placeholder="请选择案件类型"
              :disabled="disabled"
            >
              <el-option
                :label="item.label"
                :value="item.value"
                v-for="item in caseTypeList"
                :key="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="排序" prop="sort">
            <el-input
              placeholder="请输入排序"
              v-model="ruleForm.sort"
              type="number"
            >
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleCancel">取 消</el-button>
      <el-button type="primary" @click="handleOk('ruleForm')">提交</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { isEmpty, cloneDeep } from "lodash";
import storage from "store";

export default {
  name: "CreateStage",
  props: {
    visible: {
      type: Boolean,
      required: true,
    },
    type: {
      type: String,
    },
    loading: {
      type: Boolean,
      default: () => false,
    },
    model: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    const dictList = storage.get("dictList");
    return {
      caseTypeList: dictList.case_type,
      ruleForm: {
        name: "",
        type: "",
        sort: ''
      },
      rules: {
        name: [
          { required: true, message: "请输入审理程序名称", trigger: "blur" },
        ],
        type: [
          { required: true, message: "请选择案件类型", trigger: "change" },
        ],
      },
      disabled: false,
    };
  },
  mounted() {
    this.$watch(
      "visible",
      (e) => {
        if (e) {
          if (this.model) {
            this.ruleForm = JSON.parse(JSON.stringify(this.model));
            if (this.type === "addChild") {
              this.ruleForm.name = "";
              this.disabled = true;
            } else {
              this.disabled = false;
            }
            if(this.ruleForm.parentId !== '0'){
              this.disabled = true;
            }
          }
        } else {
          this.disabled = false;
          this.ruleForm = {
            name: "",
          };
        }
      },
      {
        deep: true,
        immediate: true,
      }
    );
  },
  methods: {
    handleOk() {
      this.$emit("ok", this.ruleForm);
    },
    handleCancel() {
      this.$emit("cancel");
    },
  },
};
</script>

<style>
</style>
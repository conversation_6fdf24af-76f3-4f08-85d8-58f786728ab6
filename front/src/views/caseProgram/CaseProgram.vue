<template>
  <div>
    <div class="block">
      <h3 class="title">审理程序管理</h3>
      <el-button
        type="primary"
        icon="el-icon-circle-plus-outline"
        @click="() => append('add')"
      >
        新增审理程序
      </el-button>
      <el-divider></el-divider>
      <el-tree
        :data="treeData"
        show-checkbox
        node-key="id"
        default-expand-all
        :expand-on-click-node="false"
        :loading="loading"
      >
        <div class="custom-tree-node" slot-scope="{ node, data }">
          <span class="label">{{ data.name }}</span>
          <span>
            <el-button
              type="text"
              size="mini"
              @click="() => append('addChild', data)"
            >
              新增
            </el-button>
            <el-button
              type="text"
              size="mini"
              @click="() => append('edit', data)"
            >
              编辑
            </el-button>
            <el-button type="text" size="mini" @click="remove(data)">
              删除
            </el-button>
          </span>
        </div>
      </el-tree>
    </div>
    <CreateModal
      ref="createForm"
      :visible="visible"
      :model="model"
      :type="type"
      @cancel="handleCancel"
      @ok="handleOk"
    />
  </div>
</template>

<script>
import { getList, save, queryById, del } from "@/api/case/caseProgram";
import CreateModal from "./modal/CreateModal.vue";
export default {
  name: "CaseProgram",
  components: {
    CreateModal,
  },
  data() {
    return {
      treeData: [],
      visible: false,
      model: null,
      type: "",
      loading: false
    };
  },
  mounted() {
    this.getData();
  },
  methods: {
    getData() {
      this.loading = true
      getList().then((res) => {
      this.loading = false
        this.treeData = res.treeData;
      });
    },
    handleCancel() {
      this.visible = false;
    },
    handleOk() {
      this.$refs.createForm.$refs.ruleForm.validate((valid) => {
        if (valid) {
          let data = this.$refs.createForm.ruleForm;
          let formData = new FormData();
          formData.append("name", data.name);
          formData.append("sort", data.sort);
          formData.append("type", data.type);
          if (this.type === "edit") {
            formData.append("id", data.id);
          }
          if (this.type === "addChild") {
            formData.append("parent.id", data.id);
          } else {
            formData.append("parent.id", data.parentId);
          }
          save(formData).then((res) => {
            this.visible = false;
            if (res.success) {
              this.$message({
                showClose: true,
                message: res.msg,
                type: "success",
              });
              this.getData();
            } else {
              this.$message.error(res.msg);
            }
          });
        } else {
          return false;
        }
      });
    },
    append(type, data) {
      this.type = type;
      if (type === "add") {
        this.model = null;
      } else {
        this.model = data;
      }
      this.visible = true;
    },
    remove(data) {
      this.$confirm("确定删除该审理程序?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          let param = new FormData();
          param.append("ids", data.id);
          del(param).then((res) => {
            if (res.success) {
              this.getData();
              this.$message({
                type: "success",
                message: "删除成功!",
              });
            } else {
              this.$message.error(res.msg);
            }
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
  },
};
</script>

<style lang="less" scoped>
.block {
  padding: 20px;
  background: #fff;
  .title {
    padding: 20px 0;
  }
  /deep/.el-tree-node__content {
    padding: 5px 0;
  }
  .custom-tree-node {
    width: 50%;
    display: flex;
    //   justify-content: space-between;
    .label {
      margin-right: 30px;
    }
  }
}
</style>
<template>
  <div class="block">
    <h3 class="title">案件费用</h3>
    <el-divider></el-divider>
    <el-table
      v-loading="loading"
      ref="singleTable"
      :data="tableData"
      style="width: 100%"
      @current-change="handleCurrentChange"
    >
      <el-table-column property="lawCase.name" label="案件名称">
      </el-table-column>
      <el-table-column
        property="lawCase.hostUser.name"
        label="办案律师"
      ></el-table-column>
      <el-table-column
        property="lawCase.entrustDate"
        label="案件创办日期"
      ></el-table-column>
      <el-table-column
        property="lawCase.contractMoney"
        label="应收金额"
      ></el-table-column>
      <el-table-column
        property="receiveMoney"
        label="已收费用"
      ></el-table-column>
      <el-table-column
        property="expenditureMoney"
        label="支出费用"
      ></el-table-column>
    </el-table>
    <div class="block" style="margin-top: 15px">
      <el-pagination
        background
        align="center"
        @size-change="handleSizeChange"
        @current-change="handleCurrentSizeChange"
        :current-page="currentPage"
        :page-sizes="[1, 5, 10, 20]"
        :page-size="pageSize"
        layout="prev, pager, next"
        :total="total"
      >
      </el-pagination>
    </div>
  </div>
</template>

<script>
import { caseFinanceList } from "@/api/case/cost";
import CreateModal from "./modal/CreateModal.vue";
import storage from "store";
import moment from "moment";
export default {
  name: "version",
  components: {
    CreateModal,
  },
  data() {
    return {
      tableData: [],
      total: 0,
      currentPage: 1, // 当前页码
      pageSize: 10, // 每页的数据条数
      visible: false,
      model: null,
    };
  },
  created() {
    this.getCaseFinanceList();
  },
  methods: {
    handleCurrentChange(e) {
      console.log(e)
      this.$router.push({ path: "/caseFinance", query: { id: e.lawCase.id, name: e.lawCase.name } });
    },
    getCaseFinanceList() {
      this.loading = true;
      let formData = new FormData();
      formData.append("pageNo", this.currentPage);
      formData.append("pageSize", this.pageSize);
      caseFinanceList(formData).then((res) => {
        this.loading = false;
        this.tableData = res.page.list;
        this.total = res.page.count;
      });
    },
    //每页条数改变时触发 选择一页显示多少行
    handleSizeChange(val) {
      this.currentPage = 1;
      this.pageSize = val;
    },
    //当前页改变时触发 跳转其他页
    handleCurrentSizeChange(val) {
      this.currentPage = val;
      this.getCaseFinanceList();
    },
  },
};
</script>

<style lang="less" scoped>
.block {
  padding: 20px;
  background: #fff;
  .title {
    padding: 20px 0;
  }
  /deep/.el-tree-node__content {
    padding: 5px 0;
  }
  .custom-tree-node {
    width: 50%;
    display: flex;
    //   justify-content: space-between;
    .label {
      margin-right: 30px;
    }
  }
}
</style>

<template>
  <el-dialog
    :title="model ? '编辑收款信息' : '新增收款'"
    :visible="visible"
    width="70%"
    :before-close="handleCancel"
    :close-on-press-escape="false"
    :close-on-click-modal="false"
  >
    <el-form
      :model="ruleForm"
      :rules="rules"
      ref="ruleForm"
      label-width="100px"
      class="ruleForm-box"
    >
      <el-row>
        <el-col :span="24">
          <el-form-item label="款项名称" prop="name">
            <el-input
              placeholder="请输入款项名称"
              v-model="ruleForm.name"
              type="text"
            >
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="应收金额" prop="receivableMoney">
            <el-input
              placeholder="请输入应收金额"
              v-model.number="ruleForm.receivableMoney"
            >
              <template slot="append">元</template>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="关联案件" prop="lawCase.id">
            <el-select
              v-model="ruleForm.lawCase.id"
              placeholder="请选择关联案件"
              @change="handleSelectCase"
            >
              <el-option
                :label="item.name"
                :value="item.id"
                v-for="item in allCaseList"
                :key="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        
      </el-row>
      <el-row>
        <h3 style="margin: 20px 0">收款记录</h3>
      </el-row>
      <div v-if="model">
        <el-row>
          <el-col :span="12">
            <el-form-item label="实收金额" prop="money">
              <el-input
                placeholder="请输入实收金额"
                v-model.number="ruleForm.money"
              >
                <template slot="append">元</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="收款日期" prop="happenDate">
              <el-date-picker
                v-model="ruleForm.happenDate"
                type="date"
                placeholder="选择收款日期"
                value-format="yyyy-MM-dd HH:mm:ss"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="24">
          <el-form-item label="上传附件">
            <el-upload
              class="upload-demo"
              :action="uploadUrl"
              :on-success="(response, file, fileList)=>handleUpload(response, file, fileList)"
              :on-remove="(file, fileList)=>handleRemove(file, fileList)"
              :data="uploadPath"
              multiple
              :headers="header"
              :on-preview="preview"
              :file-list="ruleForm.fileList"
            >
              <div class="form-label cursor primary">
                <i class="el-icon-paperclip mr10"></i>
                <span>点击上传费用附件</span>
              </div>
            </el-upload>
          </el-form-item>
        </el-col>
        </el-row>
      </div>
      <div v-else>
        <el-row v-for="(item, index) in ruleForm.flowRecordList" :key="index">
          <el-col :span="12">
            <el-form-item label="实收金额" prop="money">
              <el-input
                placeholder="请输入实收金额"
                v-model.number="item.money"
              >
                <template slot="append">元</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="收款日期" prop="happenDate">
              <el-date-picker
                v-model="item.happenDate"
                type="date"
                placeholder="选择收款日期"
                value-format="yyyy-MM-dd HH:mm:ss"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="24">
          <el-form-item label="上传附件">
            <el-upload
              class="upload-demo"
              :action="uploadUrl"
              :on-success="(response, file, fileList)=>handleUpload(response, file, fileList,index)"
              :on-remove="(file, fileList)=>handleRemove(file, fileList,index)"
              :data="uploadPath"
              multiple
              :headers="header"
              :on-preview="preview"
              :file-list="ruleForm.fileList"
            >
              <div class="form-label cursor primary">
                <i class="el-icon-paperclip mr10"></i>
                <span>点击上传费用附件</span>
              </div>
            </el-upload>
          </el-form-item>
        </el-col>
          <div style="text-align: right">
            <i
              v-if="index === 0"
              class="el-icon-circle-plus-outline cursor primary"
              style="font-size: 14px"
              @click="handleRecord('add')"
              >添加记录</i
            >
            <i
              v-else
              class="el-icon-remove-outline cursor"
              style="font-size: 14px; color: red"
              @click="handleRecord('del', index)"
              >删除记录</i
            >
          </div>
        </el-row>
      </div>
    </el-form>

    <span slot="footer" class="dialog-footer">
      <el-button @click="handleCancel">取 消</el-button>
      <el-button type="primary" @click="handleOk('ruleForm')">提交</el-button>
    </span>
    <el-dialog
      title="查看图片"
      :visible.sync="imgDialogVisible"
      width="60%"
      append-to-body
    >
      <img :src="imgUrl" alt="" style="width: 100%" />
    </el-dialog>
  </el-dialog>
</template>

<script>
import { isEmpty, cloneDeep } from "lodash";
import storage from "store";
import { allCaseData } from "@/api/case/manage";
import { imgSuffix } from "../../../utils/common-data";
import { getUrl } from "@/api/case/doc";


export default {
  name: "CreateStage",
  props: {
    visible: {
      type: Boolean,
      required: true,
    },
    type: {
      type: String,
    },
    loading: {
      type: Boolean,
      default: () => false,
    },
    model: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    const dictList = storage.get("dictList");
    const baseUrl = process.env.VUE_APP_API_BASE_URL;
    return {
      allCaseList: [],
      uploadUrl: `${baseUrl ? baseUrl : ""}/law/sys/file/webupload/upload`,
      uploadPath: { uploadPath: "/lawcase/financeFlowFile/" },
      imgDialogVisible: false,
      imgUrl: "",
      header: {
        token: storage.get("token"),
      },
      upIndex: 0,
      caseTypeList: dictList.case_type,
      ruleForm: {
        number: "",
        fileList: [],
        content: "",
        isEnable: false,
        lawCase: {
          id: "",
        },
        flowRecordList: [
          {
            happenDate: "", // 收款日期/发生日期
            happenUser: {
              // 发生人
              id: "",
              name: "",
            },
            id: "",
            invoiceDate: "", // 开票日期
            invoiceMoney: 0, // 开票金额
            money: 0, // 金额/实收金额
            reimbursementStatus: "", // 报销状态
          fileJsonStr: []

          },
        ],
      },
      rules: {
        number: [{ required: true, message: "请输入版本号", trigger: "blur" }],
        fileList: [
          { required: true, message: "请上传安装包", trigger: "change" },
        ],
      },
      disabled: false,
    };
  },
  mounted() {
    this.$watch(
      "visible",
      (e) => {
        if (e) {
          this.getAllCaseList();
          if (this.model) {
            this.ruleForm = JSON.parse(JSON.stringify(this.model));
            // this.ruleForm.flowRecordList = [
            //   {
            //     happenDate: this.ruleForm.happenDate, // 收款日期/发生日期
            //     money: this.ruleForm.money, // 金额/实收金额
            //     fileJsonStr: []

            //   },
            // ];
          }
        } else {
          this.disabled = false;
          this.ruleForm = {
            number: "",
            fileList: [],
            content: "",
            isEnable: false,
            lawCase: {
              id: "",
            },
            flowRecordList: [
              {
                happenDate: "", // 收款日期/发生日期
                happenUser: {
                  // 发生人
                  id: "",
                  name: "",
                },
                id: "",
                money: 0, // 金额/实收金额\
                fileJsonStr: []

              },
            ],
          };
        }
      },
      {
        deep: true,
        immediate: true,
      }
    );
  },
  methods: {
    preview(item) {
      console.log(item);
      if (item.name.IsPicture()) {
        this.imgDialogVisible = true;
        this.imgUrl = item.fullPath;
        return;
      }
      let params = {
        _w_fname: item.path,
        _w_fileid: item.id,
        operateType: "write",
      };
      getUrl(params).then((res) => {
        if (res) {
          // 跳转 使用sessionStorage，避免关键信息在ip中暴露
          // 使用push会停留当前页面，故不采纳
          // params 传递参数，子组件无法渲染iframe组件，故不采纳
          sessionStorage.wpsUrl = res.wpsUrl;
          sessionStorage.token = res.token;
          const jump = this.$router.resolve({ name: "viewFile" });
          window.open(jump.href, "_blank");
        } else {
          this.$message.error("请求错误！");
        }
      });
    },
     // 上传文件
    handleUpload(response, file, fileList, index) {
      if (this.model) {
        if (response.success) {
          console.log(fileList)
          this.$message.success("上传成功");
          this.ruleForm.fileJsonStr = [];
          fileList.map((item) => {
            this.ruleForm.fileJsonStr.push({
              "name": item.response?item.response.name:item.name, // 附件名称
              "path": item.response?item.response.url:item.path, // 附件保存路径
              "fullPath": item.response?item.response.fullUrl:item.fullPath, // 附件访问全路径
              "fileType": item.response?item.response.suffix:item.fileType  // 附件后缀
            });
          });
          console.log(this.ruleForm)
        } else {
          this.$message.error("上传失败");
        }
      } else {
        this.handleFileChange(response, fileList, index);
      }
    },
    handleFileChange(response, fileList, index) {
      console.log(index)
      if (response.success || response.status === "success") {
        if (fileList.length) {
          this.ruleForm.flowRecordList[index].fileJsonStr = [];
          fileList.map((item) => {
            this.ruleForm.flowRecordList[index].fileJsonStr.push({
               "name": item.response?item.response.name:item.name, // 附件名称
              "path": item.response?item.response.url:item.path, // 附件保存路径
              "fullPath": item.response?item.response.fullUrl:item.fullPath, // 附件访问全路径
              "fileType": item.response?item.response.suffix:item.fileType  // 附件后缀
            });
          });
        } else {
          this.ruleForm.flowRecordList[index].fileJsonStr = [];
        }
      } else {
        this.$message.error("上传失败");
      }
    },
    handleRemove(file, fileList, index) {
      console.log(index);
      console.log(fileList);
      if (this.model) {
        if (fileList.length) {
          this.ruleForm.fileJsonStr = [];
          fileList.map((item) => {
            this.ruleForm.fileJsonStr.push({
              "name": item.response?item.response.name:item.name, // 附件名称
              "path": item.response?item.response.url:item.path, // 附件保存路径
              "fullPath": item.response?item.response.fullUrl:item.fullPath, // 附件访问全路径
              "fileType": item.response?item.response.suffix:item.fileType  // 附件后缀
            });
          });
        } else {
          this.ruleForm.fileJsonStr = [];
        }
      } else {
        this.handleFileChange(file, fileList, index);
      }
    },
    handleSelectCase(e) {
      this.allCaseList.map((item) => {
        if (item.id === e) {
          this.ruleForm.lawCase.name = item.name;
        }
      });
    },
    handleRecord(type, index) {
      if (type === "add") {
        this.ruleForm.flowRecordList.push({
          happenDate: "", // 收款日期/发生日期
          id: "",
          money: 0, // 金额/实收金额\
          fileJsonStr: []
        });
      } else {
        this.ruleForm.flowRecordList.splice(index, 1);
      }
    },
    getAllCaseList() {
      allCaseData().then((res) => {
        this.allCaseList = res.data;
      });
    },
    handleOk() {
      this.$emit("ok", this.ruleForm);
    },
    handleCancel() {
      this.$emit("cancel");
    }
  },
};
</script>

<style>
</style>
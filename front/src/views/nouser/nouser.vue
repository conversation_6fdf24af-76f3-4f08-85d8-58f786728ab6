<template>
  <div class="login qr-login"  > 
    <div  style="text-align: center;margin: 0px auto;font-size: 30px;color: red;">
		体验账号暂无权限
    </div>
    
  </div>
</template>

<script> 

export default {
  name: "initAdmin",
  components: {},
  data() {
    return {
     }
  },created(){
	 
  },
  methods: {
     
  },
};
</script>
<style lang="less" scoped>
.login {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  height: 100vh; 
  background-repeat: no-repeat;
  background-size: cover;
  overflow: hidden;

  .qr-login-wrap {
    background: #fff;
    border-radius: 10px;
    box-shadow: 14px 17px 21px 0 rgb(94 98 193 / 7%);
    margin: 0 auto;
    position: relative;
    overflow: hidden;
  }

  .login-form {
    width: 600px;
    min-width: 500px;
    position: relative;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    padding-left: 40px;
    border-left: 1px solid hsla(0, 0%, 100%, 0.1);
  }

  .login .login-form .user-input {
    width: 100%;
  }

  .qr-logo {
    display: block;
    width: 232px;
    height: 73px;
    margin: 50px auto 0;
  }

  .ruleForm-box {
    padding-left: 50px;
	padding-top: 20px;
    padding-right: 100px;
  }

  .forgot-cell {
    text-align: right;
    cursor: pointer;
  }

  .bind_code {
    position: relative;
  }

  .code_btn {
    width: 45%;
    position: absolute;
    right: -3px;
    font-size: 11px;
    font-weight: 500;
    font-family: PingFang SC-Medium, PingFang SC;
    color: #1b3dd1;
    line-height: 15px;
    cursor: pointer;
    padding-left: 10px;
    border: 1px solid #1b3dd1;
  }
}
</style>

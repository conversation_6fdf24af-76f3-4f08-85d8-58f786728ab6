<template>
  <div class="new-common-detail">
    <div class="common-item" style="flex: 1 1 0%; overflow-x: hidden">
      <div class="common-top">
        <div class="top-title">文书目录</div>
      </div>

      <div class="" v-if="treeData.length">
        <div class="base-folder cursor">
          <div class="left-wrap" @click="showAll">
            <i class="el-icon-tickets mr10"></i>
            <span>全部</span>
          </div>
          <div class="right-wrap">
            <el-tooltip
              class="item"
              effect="dark"
              content="新建"
              placement="top"
            >
              <i
                class="el-icon-folder-add"
                @click="handleCreateDirectory('add')"
              ></i>
            </el-tooltip>
          </div>
        </div>
        <div style="margin-left: 20px">
          <el-tree
            :data="treeData"
            node-key="id"
            ref="treeNodes"
            :expand-on-click-node="false"
            @node-click="handleNodeClick"
          >
            <div
              class="custom-tree-node flex justify-between"
              slot-scope="{ node, data }"
            >
              <div>
                <img
                  src="../../../assets/red-file.png"
                  alt=""
                  class="folder-img"
                />
                <span>{{ data.name }}</span>
              </div>
              <div class="node-btn">
                <el-tooltip
                  class="item"
                  effect="dark"
                  content="新建"
                  placement="top"
                >
                  <i
                    class="el-icon-folder-add"
                    @click.stop="handleCreateDirectory('child', data)"
                  ></i>
                </el-tooltip>
                <el-tooltip
                  class="item"
                  effect="dark"
                  content="编辑"
                  placement="top"
                >
                  <i
                    class="el-icon-edit"
                    @click.stop="handleCreateDirectory('edit', data)"
                  ></i>
                </el-tooltip>
                <el-tooltip
                  class="item"
                  effect="dark"
                  content="删除"
                  placement="top"
                >
                  <i
                    class="el-icon-delete"
                    @click.stop="handleDeleteDirectory(data)"
                  ></i>
                </el-tooltip>
              </div>
            </div>
          </el-tree>
        </div>
      </div>
      <div class="common-content" v-else>
        <div style="text-align: center" v-if="user.loginName!='test01'">
          <el-button
            plain
            class="fast-create-btn"
            @click="handleCreateDirectory('add')"
            >创建文件夹</el-button
          >
        </div>
        <!-- <div style="text-align: center; margin-top: 20px">
          <el-button plain class="fast-create-btn">一键生成目录</el-button>
        </div>
        <div style="text-align: center; margin-top: 20px">
          <el-button plain class="fast-create-btn">复制其他案件目录</el-button>
        </div> -->
      </div>
    </div>
    <div class="common-item" style="flex: 3 1 0%">
      <!-- <div class="file-top-operate">
        <div style="width: 70%">
          <el-input placeholder="请输入文件名称模糊搜索" v-model="input4">
            <i slot="prefix" class="el-input__icon el-icon-search"></i>
          </el-input>
        </div>
        <div class="right-icon">
          <i class="el-icon-sort mr20 cursor"></i>
          <i class="el-icon-menu cursor"></i>
        </div>
      </div> -->
      <div class="new-file-path">
        <span class="mr10 cursor" @click="showAll">全部</span>
        <span
          v-for="(item, index) in breadcrumb"
          :key="item.id"
          @click="handleNodeClick(item)"
        >
          <i class="el-icon-arrow-right mr10"></i>
          <span
            :class="[
              'mr10',
              index + 1 === breadcrumb.length ? 'cur-folder' : 'cursor',
            ]"
            >{{ item.name }}</span
          >
        </span>
      </div>
      <div class="file-common-content">
        <div class="detail-file-block">
          <div class="detail-file-table">
            <div class="file-header">
              <el-checkbox v-model="allChecked" @change="handleAllChecked"
                >文件名</el-checkbox
              >
            </div>
            <div
              class="block-item-list"
              v-if="fileList.length || directoryList.length"
            >
              <!-- 目录列表 -->
              <div class="file-content">
                <div class="content-right">
                  <div
                    class="content-item"
                    v-for="(item, index) in directoryList"
                    :key="item.id"
                    @click="handleNodeClick(item)"
                  >
                    <div class="item-name text-ellipsis">
                      <img
                        src="../../../assets/red-file.png"
                        alt=""
                        class="word-img mr10"
                        style="margin-left: 30px"
                      />
                      <div class="file-name-wrap text-ellipsis">
                        <div class="file-name text-ellipsis">
                          {{ item.name }}
                        </div>
                      </div>
                    </div>
                    <el-dropdown   v-if="user.loginName!='test01'"
                      trigger="hover"
                      @command="handleCommand($event, item)"
                    >
                      <i class="el-icon-more mr20"></i>
                      <el-dropdown-menu slot="dropdown">
                        <el-dropdown-item command="move">移动</el-dropdown-item>
                        <el-dropdown-item command="modify"
                          >修改</el-dropdown-item
                        >
                        <el-dropdown-item command="delete"
                          >删除</el-dropdown-item
                        >
                      </el-dropdown-menu>
                    </el-dropdown>
                  </div>
                </div>
              </div>
              <!-- 文件列表 -->
              <div class="file-content">
                <div class="content-right">
                  <div
                    class="content-item"
                    v-for="(item, index) in fileList"
                    :key="item.id"
                  >
                    <el-checkbox
                      v-model="item.checked"
                      style="margin-left: 20px"
                      @change="handleSelectFile($event, index)"
                    ></el-checkbox>
                    <div
                      class="item-name text-ellipsis"
                      @click="handleOpen(item)"
                    >
                      <i
                        class="iconfont file-blue icon-jpg1"
                        v-if="item.fileType === 'jpg'"
                      ></i>
                      <i
                        class="iconfont file-blue icon-png1"
                        v-if="item.fileType === 'png'"
                      ></i>
                      <i
                        class="iconfont file-blue icon-txt1"
                        v-if="item.fileType === 'txt'"
                      ></i>
                      <i
                        class="iconfont file-blue icon-w"
                        v-if="item.fileType === 'doc' || item.fileType === 'docx'"
                      ></i>
                      <i
                        class="iconfont file-blue icon-p"
                        v-if="item.fileType === 'pptx'"
                      ></i>
                      <i
                        class="iconfont file-blue icon-s"
                        v-if="item.fileType === 'xlsx' || item.fileType === 'xls'"
                      ></i>
                      <div class="file-name-wrap text-ellipsis">
                        <div class="file-name text-ellipsis">
                          {{ item.name }}
                        </div>
                        <div class="file-time">{{ item.createDate }}</div>
                      </div>
                    </div>
                    <div class="item-user text-ellipsis">上传人：保常津</div>
                    <el-dropdown
                      trigger="hover"
                      @command="handleCommand($event, item)"
                    >
                      <i class="el-icon-more mr20"></i>
                      <el-dropdown-menu slot="dropdown">
                        <el-dropdown-item command="moveFile"
                          >移动</el-dropdown-item
                        >
                        <el-dropdown-item command="modifyFile"
                          >修改文件名</el-dropdown-item
                        >
                        <el-dropdown-item command="download"
                          >下载</el-dropdown-item
                        >
                        <el-dropdown-item command="deleteFile"
                          >删除</el-dropdown-item
                        >
                      </el-dropdown-menu>
                    </el-dropdown>
                  </div>
                </div>
              </div>
            </div>
            <div v-else>
              <NoData />
            </div>
          </div>
        </div>
      </div>
    </div>
    <div   v-if="user.loginName!='test01'"
      class="common-item detail-file-item"
      style="width: 285px; border-right: none"
    >
      <el-upload
        class="upload-demo"
        drag
        :action="uploadUrl"
        :headers="header"
        :data="{
          'lawCase.id': lawCaseId,
          'fileDirectory.id': currentDirectoryId,
        }"
			:before-upload="beforeUpload"
        :on-success="handleUpload"
        :show-file-list="false"
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
      </el-upload>
      <div style="text-align: center; margin-top: 20px; position: relative">
        <el-button plain class="fast-create-btn">上传文件夹</el-button>
        <input
          type="file"
          name="file"
          webkitdirectory
          @change="uploadFolder($event)"
          class="uploader"
        />
      </div>

      <!-- <el-button plain class="fast-create-btn">新建</el-button> -->
      <el-dropdown trigger="click" @command="createFile" style="width: 100%">
        <div style="text-align: center; margin-top: 20px">
          <el-button plain class="fast-create-btn">新建</el-button>
        </div>

        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item command="w"
            ><svg class="icon" aria-hidden="true">
              <use xlink:href="#icon-word1" /></svg
            ><span style="text-align: center">文字文档</span></el-dropdown-item
          >
          <el-dropdown-item command="s"
            ><svg class="icon" aria-hidden="true">
              <use xlink:href="#icon-excel1" /></svg
            ><span style="text-align: center">表格文档</span></el-dropdown-item
          >
          <el-dropdown-item command="p">
            <svg class="icon" aria-hidden="true">
              <use xlink:href="#icon-ppt4" />
            </svg>
            <span style="text-align: center">演示文档</span></el-dropdown-item
          >
        </el-dropdown-menu>
      </el-dropdown>
      <div style="text-align: center; margin-top: 20px">
        <el-button plain class="fast-create-btn" @click="handleShowDocModal"
          >从模板创建</el-button
        >
      </div>
      <div class="batch-wrap">
        <div style="text-align: center; margin-top: 20px">
          <el-button
            plain
            icon="el-icon-rank"
            :disabled="disabled"
            @click="batchMove"
            >批量移动</el-button
          >
        </div>
        <div style="text-align: center; margin-top: 20px">
          <el-button
            plain
            icon="el-icon-download"
            :disabled="disabled"
            @click="batchDownload"
            >批量下载</el-button
          >
        </div>
        <div style="text-align: center; margin-top: 20px">
          <el-button
            plain
            icon="el-icon-delete"
            :disabled="disabled"
            @click="batchDelete"
            >批量删除</el-button
          >
        </div>
      </div>
      <div>
        <div class="tip-txt">提示：</div>
        <div>点击文件名可在线预览或编辑</div>
      </div>
    </div>
    <CreateDirectory
      ref="directoryForm"
      :visible="directoryVisible"
      :model="directoryModalMdl"
      @cancel="handleDirectoryCancel"
      @ok="handleDirectoryOk"
      :type="type"
    />
    <ModifyFIleName
      ref="FileNameForm"
      :visible="fileNameVisible"
      :model="fileNameModalMdl"
      @cancel="handleFileNameCancel"
      @ok="handleFileNameOk"
    />
    <MoveFile
      ref="moveFileForm"
      :visible="moveFileVisible"
      @cancel="handleMoveFileCancel"
      @ok="handleMoveFileOk"
    />
    <DocTemplate
      ref="docTempForm"
      :visible="docTempVisible"
      @cancel="handleDocTempCancel"
      @ok="handleDocTempOk"
      :directoryId="currentDirectoryId"
      @useDoc="
        docTempVisible = false;
        getFileList();
      "
    />
    <el-dialog title="查看图片" :visible.sync="dialogImgVisible">
      <img :src="imgSrc" alt="" class="prev-img" />
    </el-dialog>
  </div>
</template>

<script>
import NoData from "@/components/NoData";
import {
  getTreeData,
  createDirectory,
  delDirectory,
  caseFileList,
  deleteFile,
  moveFile,
  saveFile,
  uploadDir,
  getUrl,
  createDoc,
} from "@/api/case/doc";
import CreateDirectory from "./modal/CreateDirectory";
import ModifyFIleName from "./modal/ModifyFIleName";
import MoveFile from "./modal/MoveFile";
import DocTemplate from "./modal/DocTemplate";
import storage from "store";
import "../../../static/iconfont.css";
import { fileSuffix, imgSuffix } from "../../../utils/common-data";

const baseUrl = process.env.VUE_APP_API_BASE_URL;

export default {
  name: "CaseDoc",
  components: {
    NoData,
    CreateDirectory,
    ModifyFIleName,
    MoveFile,
    DocTemplate,
  },
  data() {
    return {
      fileList: [],
      treeData: [],
      allChecked: false,
      input4: "",
      breadcrumb: [],
      directoryModalMdl: null,
      directoryVisible: false,
      fileNameModalMdl: null,
      fileNameVisible: false,
      moveFileVisible: false,
      currentDirectoryId: "",
      directoryList: [],
      uploadUrl: `${baseUrl ? baseUrl : ""}/law/lawcase/caseFile/upload`,
      header: {
        token: storage.get("token"),
      },
      lawCaseId: "",
      type: "",
      disabled: true,
      docTempVisible: false,
      dialogImgVisible: false,
	    user: storage.get("user"),
      imgSrc: "",
    };
  },
  mounted() {
    this.lawCaseId = this.$route.query.id;
    this.getDirectory();
    this.getFileList();
    this.getDirectoryById();
  },
  methods: {
    handleCommand(command, mdl, index) {
		if(this.user.loginName=='test01'){
			 return this.$message.error("体验者不能操作");
		}
		
      switch (command) {
        case "move":
          this.handleShowMoveFileModal("dir", mdl);
          break;
        case "delete":
          this.handleDeleteDirectory(mdl);
          break;
        case "modify":
          this.handleCreateDirectory("edit", mdl);
          break;
        case "deleteFile":
          this.handleDeleteFile(mdl.id);
          break;
        case "download":
          this.downloadFile(mdl);
          break;
        case "modifyFile":
          this.handleModifyFileName(mdl);
          break;
        case "moveFile":
          this.handleShowMoveFileModal("file", mdl.id);
          break;
        default:
          break;
      }
    },
    // 选择文件
    handleSelectFile(e, index) {
		if(this.user.loginName=='test01'){
			 return this.$message.error("体验者不能操作");
		}
      this.fileList[index].checked = e;
      this.$forceUpdate();
      this.selectFileList = this.fileList.filter((item) => item.checked);
      this.disabled = this.selectFileList.length ? false : true;
      if (this.selectFileList.length === this.fileList.length) {
        this.allChecked = true;
      } else {
        this.allChecked = false;
      }
    },
    //文件全选
    handleAllChecked() {
      if (this.allChecked) {
        this.selectFileList = this.fileList.filter(
          (item) => (item.checked = true)
        );
        this.disabled = false;
      } else {
        this.selectFileList = this.fileList.filter(
          (item) => (item.checked = false)
        );
        this.disabled = true;
      }
    },
    createFile(template) {
		if(this.user.loginName=='test01'){
			 return this.$message.error("体验者不能操作");
		}
      let formData = new FormData();
      formData.append("type", template);
      formData.append("fileDirectoryId", this.fileDirectoryId || "");
      formData.append("lawCaseId", this.lawCaseId);
      createDoc(formData)
        .then((res) => {
          this.getFileList();
          if (res.success) {
            this.handleOpen(res);
          } else {
            this.$message.error("请求错误！");
          }
        })
        .catch(() => {
          this.$message.error("请求错误！");
        });
    },
    //批量下载文件
    batchDownload() {
      this.selectFileList.map((item) => {
        this.downloadFile(item);
      });
    },
    // 下载文件
    downloadFile(mdl) {
      const url = `${baseUrl ? baseUrl : ""}/law${mdl.path}`;
      this.download(url, mdl.name);
    },
    download(url, filename) {
      return fetch(url).then((res) =>
        res.blob().then((blob) => {
          let a = document.createElement("a");
          let url = window.URL.createObjectURL(blob);
          a.href = url;
          a.download = filename;
          a.click();
          window.URL.revokeObjectURL(url);
        })
      );
    },
	beforeUpload(file) {
	  const isLt2M = file.size / 1024 / 1024 < 60;
	  if (!isLt2M) {
	    this.$message.error('上传文件大小不能超过60MB!');
	  }
	  return isLt2M;
	},
    // 上传文件
    handleUpload(response, file, fileList) {
      if (response.success) {
        this.$message.success("上传成功");
        this.getFileList();
      } else {
        this.$message.error("上传失败");
      }
    },
    //批量删除文件
    batchDelete() {
      let ids = this.selectFileList.map((item) => item.id).join(",");
      this.handleDeleteFile(ids);
    },
    // 删除文件
    handleDeleteFile(id) {
		if(this.user.loginName=='test01'){
			 return this.$message.error("体验者不能操作");
		}
      this.$confirm("确定删除该文件?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          let formdata = new FormData();
          formdata.append("ids", id);
          deleteFile(formdata).then((res) => {
            if (res.success) {
              this.$message({
                showClose: true,
                message: res.msg,
                type: "success",
              });
              this.getFileList();
              this.disabled = true;
            } else {
              this.$message.error(res.msg);
            }
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    //打开文件
    handleOpen(item) {
		if(this.user.loginName=='test01'){
			 return this.$message.error("体验者不能操作");
		}
      console.log(item)
      let type = ''
      if(item.fileType){
        type = item.fileType
      }else{
        type = item.name.split('.')[1]
      }
      let result1 = fileSuffix.some((child) => {
        return type === child;
      });
      let result2 = imgSuffix.some((child) => {
        return type === child;
      });
      if (result1) {
        let params = {
          _w_fname: item.path || item.url,
          _w_fileid: item.id || new Date().getTime(),
          operateType: "write",
        };
        getUrl(params).then((res) => {
          if (res) {
            // 跳转 使用sessionStorage，避免关键信息在ip中暴露
            // 使用push会停留当前页面，故不采纳
            // params 传递参数，子组件无法渲染iframe组件，故不采纳
            sessionStorage.wpsUrl = res.wpsUrl;
            sessionStorage.token = res.token;
            const jump = this.$router.resolve({ name: "viewFile" });
            window.open(jump.href, "_blank");
          } else {
            this.$message.error("请求错误！");
          }
        });
      } else if (result2) {
        this.dialogImgVisible = true;
        this.imgSrc = `${baseUrl ? baseUrl : ""}/law${item.path}`;
      } else{
        this.$message.error("该文件不支持预览，请下载后打开！")
      }
    },
    // 获取目录树
    getDirectory() {
      let formdata = new FormData();
      formdata.append("lawCaseId", this.lawCaseId);
      getTreeData(formdata).then((res) => {
        this.treeData = res.treeData;
      });
    },
    // 获取指定目录下的文件夹
    getDirectoryById() {
      let formdata = new FormData();
      formdata.append("lawCaseId", this.lawCaseId);
      formdata.append("directoryId", this.currentDirectoryId);
      getTreeData(formdata).then((res) => {
        this.directoryList = res.treeData;
      });
    },

    //显示移动文件的弹框
    handleShowMoveFileModal(type, data) {
      this.moveFileVisible = true;
      this.moveType = type;
      if (type === "file") {
        this.fileId = data;
      } else {
        this.dirInfo = data;
      }
    },
    // 批量移动文件
    batchMove() {
      let ids = this.selectFileList.map((item) => item.id).join(",");
      this.handleShowMoveFileModal("file", ids);
    },
    //确认移动文件或目录
    handleMoveFileOk(e) {
      if (this.moveType === "file") {
        let formdata = new FormData();
        formdata.append("caseId", this.lawCaseId);
        formdata.append(" ids", this.fileId);
        formdata.append("dirId", e);
        moveFile(formdata).then((res) => {
          this.moveFileVisible = false;
          this.getFileList();
          this.$message.success(res.msg);
        });
      } else {
        this.handleMoveDir(e);
      }
    },
    // 关闭移动文件弹框
    handleMoveFileCancel() {
      this.moveFileVisible = false;
    },
    // 显示创建目录弹框
    handleCreateDirectory(type, data) {
      this.directoryVisible = true;
      this.type = type;
      if (type === "edit" || type === "child") {
        this.directoryModalMdl = data;
      } else {
        this.directoryModalMdl = null;
      }
    },
    //移动目录
    handleMoveDir(e) {
      let formdata = new FormData();
      formdata.append("lawCase.id", this.lawCaseId);
      formdata.append("name", this.dirInfo.name);
      formdata.append("parent.id", e);
      formdata.append("id", this.dirInfo.id);
      createDirectory(formdata).then((res) => {
        this.moveFileVisible = false;

        if (res.success) {
          this.$message({
            showClose: true,
            message: res.msg,
            type: "success",
          });
          this.getFileList();
          this.getDirectory();
          this.getDirectoryById();
        } else {
          this.$message.error(res.msg);
        }
      });
    },
    // 创建目录
    handleDirectoryOk() {
      const data = this.$refs.directoryForm.ruleForm;
      this.$refs.directoryForm.$refs.ruleForm.validate((valid) => {
        if (valid) {
          let formdata = new FormData();
          formdata.append("lawCase.id", this.lawCaseId);
          formdata.append("name", data.name);
          formdata.append(
            "parent.id",
            this.type === "child" ? this.directoryModalMdl.id : ""
          );
          formdata.append(
            "id",
            this.type === "edit" ? this.directoryModalMdl.id : ""
          );
          createDirectory(formdata).then((res) => {
            this.directoryVisible = false;
            if (res.success) {
              this.$message({
                showClose: true,
                message: res.msg,
                type: "success",
              });
              this.getDirectory();
              this.getDirectoryById();
            } else {
              this.$message.error(res.msg);
            }
          });
        } else {
          return false;
        }
      });
    },
    // 删除目录
    handleDeleteDirectory(data) {
      this.$confirm(
        "此操作将删除该目录及其下面的全部文件，请慎重操作！删除前，您可以将目录下面的文件，批量转移到指定文件夹下面。",
        "提示",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      )
        .then(() => {
          let formdata = new FormData();
          formdata.append("ids", data.id);
          delDirectory(formdata).then((res) => {
            if (res.success) {
              this.$message({
                showClose: true,
                message: res.msg,
                type: "success",
              });
              this.getDirectory();
              this.getDirectoryById();
              this.getFileList();
            } else {
              this.$message.error(res.msg);
            }
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    //显示文档模板弹框
    handleShowDocModal() {
      this.docTempVisible = true;
    },
    handleDocTempCancel() {
      this.docTempVisible = false;
    },
    //关闭文档模板弹框
    handleDocTempOk() {
      this.docTempVisible = false;
    },
    handleDirectoryCancel() {
      this.directoryVisible = false;
    },
    handleModifyFileName(mdl) {
      this.fileNameVisible = true;
      this.fileNameModalMdl = mdl;
    },
    handleFileNameCancel() {
      this.fileNameVisible = false;
    },
    // 修改文件名称
    handleFileNameOk() {
      const data = this.$refs.FileNameForm.ruleForm;
      this.$refs.FileNameForm.$refs.ruleForm.validate((valid) => {
        if (valid) {
          let formdata = new FormData();
          formdata.append("id", data.id);
          formdata.append("name", data.name);
          saveFile(formdata).then((res) => {
            this.fileNameVisible = false;
            if (res.success) {
              this.$message({
                showClose: true,
                message: res.msg,
                type: "success",
              });
              this.getFileList();
            } else {
              this.$message.error(res.msg);
            }
          });
        } else {
          return;
        }
      });
    },
    // 上传文件夹
    uploadFolder(e) {
      let formData = new FormData();
      formData.append("lawCaseId", this.lawCaseId);
      formData.append("fileDirectoryId", this.currentDirectoryId);
      e.target.files.forEach((item) => {
        formData.append("file", item);
      });
      uploadDir(formData).then((res) => {
        if (res.success) {
          this.$message.success("上传成功");
          this.getDirectory();
          this.getDirectoryById();
          this.getFileList();
        }
      });
    },
    // 目录树点击事件
    handleNodeClick(data, e) {
      this.currentDirectoryId = data.id;
      this.getFileList();
      this.directoryList = data.children || [];
      const result = this.treeFindPath(
        this.treeData,
        (node) => node.id === data.id
      );

      this.$refs.treeNodes.setCurrentKey(data.id);
      this.breadcrumb = result;
    },
    showAll() {
      this.currentDirectoryId = "";
      this.breadcrumb = [];
      this.directoryList = this.treeData;
      this.getFileList();
    },
    getFileList() {
      this.allChecked = false;
      let formData = new FormData();
      formData.append("lawCase.id", this.lawCaseId);
      formData.append("fileDirectory.id", this.currentDirectoryId);
      formData.append("pageSize", -1);
      caseFileList(formData).then((res) => {
        this.fileList = res.page.list;
        this.fileList.map((item) => {
          item.checked = false;
        });
      });
    },
    treeFindPath(tree, func, path = []) {
      if (!tree) return [];
      for (const data of tree) {
        path.push(data);
        if (func(data)) return path;
        if (data.children) {
          const findChildren = this.treeFindPath(data.children, func, path);
          if (findChildren.length) return findChildren;
        }
        path.pop();
      }
      return [];
    },
  },
};
</script>

<style scoped lang="less">
.new-common-detail {
  box-sizing: border-box;
  display: flex;
  height: calc(100vh - 169px);
  background: rgb(255, 255, 255);
  margin-top: 20px;
  .common-item {
    box-sizing: border-box;
    border-right: 1px solid #e1e4ee;
    font-size: 14px;
    .common-top {
      box-sizing: border-box;
      height: 55px;
      display: flex;
      align-items: center;
      border-bottom: 1px solid #e1e4ee;
      padding: 0 20px;
      .top-title {
        font-size: 16px;
        color: #666;
        font-weight: 700;
      }
    }
    .common-content {
      padding: 140px 0px 0px;
      box-sizing: border-box;
      height: calc(100vh - 225px);
      overflow-y: auto;
      .fast-create-btn {
        color: #409eff;
        border: 1px dashed #409eff;
        width: 80%;
      }
    }
    .file-top-operate {
      flex: 1 1 0%;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 20px;
      /deep/ .el-input__inner {
        border-radius: 30px;
        height: 34px;
        line-height: 34px;
      }
      .right-icon {
        i {
          font-size: 22px;
        }
      }
    }
    .new-file-path {
      height: 40px;
      display: flex;
      align-items: center;
      padding: 0 25px;
      color: #999;
      .cur-folder {
        color: #606266;
      }
    }
    .file-common-content {
      box-sizing: border-box;
      padding: 0;
      height: calc(100% - 100px);
      overflow-y: auto;
      .file-header {
        display: flex;
        align-items: center;
        box-sizing: border-box;
        height: 42px;
        border-top: 1px solid #e1e4ee;
        border-bottom: 1px solid #e1e4ee;
        font-size: 15px;
        color: #999;
        padding: 0 20px;
      }
    }
    .base-folder {
      padding: 0 20px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 33px;
      i {
        font-size: 20px;
      }
      .right-wrap i {
        display: none;
      }
      &:hover {
        background: #f5f7fa;
      }
      &:hover .right-wrap i {
        display: block;
      }
    }
  }
  .detail-file-item {
    padding: 20px;
    /deep/.el-upload-dragger {
      width: 240px;
      height: 88px;
      .el-icon-upload {
        margin: 0;
        font-size: 36px;
      }
      .el-upload__text {
        font-size: 13px;
      }
    }
    .fast-create-btn {
      color: #409eff;
      border: 1px solid #409eff;
      width: 80%;
      border-radius: 30px;
    }
    .batch-wrap {
      border-top: 1px solid #e1e4ee;
      border-bottom: 1px solid #e1e4ee;
      padding: 20px;
      margin-top: 40px;
      .el-button {
        border-radius: 0;
        border: none;
        border-bottom: 1px solid #e1e4ee;
      }
    }
  }
  .custom-tree-node {
    width: 100%;
    margin-right: 5px;
    i {
      margin-left: 10px;
      font-size: 16px;
    }
    .node-btn {
      display: none;
    }
    .folder-img {
      width: 16px;
      height: 16px;
      margin-right: 10px;
    }
    &:hover .node-btn {
      display: block;
    }
  }
}
.word-img {
  width: 30px;
  height: 30px;
}
.file-content {
  .file-blue {
    font-size: 30px;
    color: #a0bdf8;
    margin-right: 10px;
  }
  .icon-w {
    color: #4991f2 !important;
  }
  .icon-s {
    color: #1fbb7d !important;
  }
  .icon-p {
    color: #f46d43 !important;
  }
  .content-item {
    box-sizing: border-box;
    display: flex;
    align-items: center;
    cursor: pointer;
    &:hover {
      background: #f4f5fa;
    }
    .item-name {
      padding-left: 20px;
      height: 65px;
      flex: 1;
      display: flex;
      align-items: center;
      .file-name {
        font-size: 14px;
        color: #333;
      }
      .file-time {
        font-size: 12px;
        color: #b7b7b7;
        padding-top: 5px;
      }
    }
    .item-user {
      height: 65px;
      flex: 1;
      font-size: 12px;
      color: #9fa5b9;
      display: flex;
      align-items: center;
    }
  }
}
.uploader {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  z-index: 999;
}
.icon {
  width: 1.15em;
  height: 1.15em;
  vertical-align: -0.15em;
  fill: currentColor;
  overflow: hidden;
  margin: 0 8px 0 0;
}
.tip-txt {
  font-size: 14px;
  color: #ccc;
  margin-top: 30px;
}
.prev-img {
  width: 100%;
}
</style>
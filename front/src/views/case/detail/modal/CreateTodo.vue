<template>
  <el-dialog
    :title="model ? '编辑待办' : '新增待办'"
    :visible="visible"
    width="600px"
    :before-close="handleCancel"
    :close-on-press-escape="false"
    :close-on-click-modal="false"
  >
    <el-form
      :model="ruleForm"
      :rules="rules"
      ref="ruleForm"
      class="ruleForm-box"
    >
      <el-row :gutter="20" class="flex align-center">
        <el-col :span="24">
          <el-input
            placeholder="请输入工作摘要"
            v-model="ruleForm.name"
            type="textarea"
            maxlength="200"
            show-word-limit
          >
          </el-input>
        </el-col>
        <!-- <el-col :span="4">
          <el-checkbox v-model="ruleForm.checked">设为已办</el-checkbox>
        </el-col> -->
      </el-row>
      <el-row class="mg10">
        <el-col :span="24">
          <div class="mg10 form-label">
            <span>工作详情</span>
          </div>
          <el-input
            :class="[ruleForm.content?'yes':'no']"
            type="textarea"
            :autosize="{ minRows: 4 }"
            placeholder="请输入内容"
            v-model="ruleForm.content"
            maxlength="500"
            show-word-limit
          >
          </el-input>
        </el-col>
      </el-row>
      <el-row class="mg10 flex align-center" :gutter="10">
        <el-col :span="12">
          <div class="block flex align-center">
            <div class="mg10 form-label">
              <i class="el-icon-time mr10"></i>
              <span>开始:</span>
            </div>
            <el-date-picker
              v-model="ruleForm.startDate"
              type="datetime"
              placeholder="选择开始时间"
              value-format="yyyy-MM-dd HH:mm:ss"
            >
            </el-date-picker>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="block flex align-center">
            <div class="mg10 form-label">
              <i class="el-icon-bell mr10"></i>
              <span>提醒:</span>
            </div>
            <el-date-picker
              v-model="ruleForm.remindDate"
              type="datetime"
              placeholder="选择提醒时间"
              value-format="yyyy-MM-dd HH:mm:ss"
            >
            </el-date-picker>
          </div>
        </el-col>
        <!-- <el-col :span="12">
          <div class="block flex align-center">
            <div class="mg10 form-label">
              <i class="el-icon-time mr10"></i>
              <span>结束:</span>
            </div>
            <el-date-picker
              v-model="ruleForm.endDate"
              type="datetime"
              placeholder="选择结束时间"
              value-format="yyyy-MM-dd HH:mm:ss"
            >
            </el-date-picker>
          </div>
        </el-col> -->
        <!-- <el-col :span="4">
          <el-checkbox v-model="ruleForm.allDay">全天</el-checkbox>
        </el-col> -->
      </el-row>
      <el-row :gutter="10">
        
        <!-- <el-col :span="12">
          <div class="block flex align-center">
            <div class="mg10 form-label">
              <i class="el-icon-time mr10"></i>
              <span>耗时:</span>
            </div>
            <el-input placeholder="请输入" v-model="ruleForm.consumeTime">
              <template slot="append">分钟</template>
            </el-input>
          </div>
        </el-col> -->
      </el-row>
      <el-row class="mg10" v-show="false">
        <el-col :span="12">
          <div class="block flex align-center">
            <div class="mg10 form-label">
              <i class="el-icon-sort mr10"></i>
              <span>排序:</span>
            </div>
            <el-input placeholder="请输入排序" v-model.number="ruleForm.sort">
            </el-input>
          </div>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <!-- <div class="block flex align-center">
            <div class="mg10 form-label cursor" @click="uploadFile">
              <i class="el-icon-paperclip mr10"></i>
              <span>点击上传附件</span>
            </div>
          </div> -->
          <!-- <el-upload
            class="upload-demo"
            :action="uploadUrl"
            :on-success="handleUpload"
            :on-remove="handleRemove"
            :data="uploadPath"
            multiple
            :headers="header"
            :on-preview="preview"
            :file-list="ruleForm.fileList"
          >
            <div class="mg10 form-label cursor">
              <i class="el-icon-paperclip mr10"></i>
              <span>点击上传附件</span>
            </div>
          </el-upload> -->
        </el-col>
      </el-row>
      <el-row class="mg10" :gutter="20">
        <el-col :span="16">
          <div class="block align-center">
            <div class="mg10">关联案件/项目/客户</div>
            <el-select
              style="width: 30%"
              :value="relevanceType"
              placeholder="请选择"
              :disabled="!isCalendar"
              @change="selectRelevanceType($event)"
            >
              <el-option
                :label="item.label"
                :value="item.value"
                v-for="item in todo_relevance_type"
                :key="item.id"
              ></el-option>
            </el-select>
            <el-select
              style="width: 70%"
              v-model="ruleForm.client"
              placeholder="请选择"
              :disabled="!isCalendar || relevanceType === '1'"
            >
              <el-option
                :label="relevanceType === '2' ? item.name : item.customer.name"
                :value="relevanceType === '2' ? item.id : item.customer.id"
                v-for="item in allCaseList"
                :key="item.id"
              ></el-option>
            </el-select>
          </div>
        </el-col>
        <el-col :span="8" v-if="relevanceType === '2'">
          <div class="block align-center">
            <div class="mg10">案件阶段</div>
            <el-select
              style="width: 100%"
              v-model="ruleForm.stage.id"
              placeholder="请选择"
              @change="handleChange"
              :disabled="stageDisabled"
            >
              <el-option
                :label="item.name"
                :value="item.id"
                v-for="item in stageList"
                :key="item.id"
              ></el-option>
            </el-select>
          </div>
        </el-col>
      </el-row>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleCancel">取 消</el-button>
      <el-button type="primary" @click="handleOk('ruleForm')">提交</el-button>
    </span>
    <el-dialog
      title="查看图片"
      :visible.sync="imgDialogVisible"
      width="60%"
      append-to-body
    >
      <img :src="imgUrl" alt="" style="width: 100%" />
    </el-dialog>
  </el-dialog>
</template>

<script>
import dayjs from "dayjs";
import storage from "store";
import { allCaseData } from "@/api/case/manage";
import { getList } from "@/api/customer/customer";
import { deleteFile } from "@/api/case/todo";
import { getUrl } from "@/api/case/doc";
const baseUrl = process.env.VUE_APP_API_BASE_URL;

export default {
  name: "CopyCase",
  props: {
    visible: {
      type: Boolean,
      required: true,
    },
    loading: {
      type: Boolean,
      default: () => false,
    },
    model: {
      type: Object,
      default: () => null,
    },
    stageList: {
      type: Array,
      // required: true,
    },
    selectStage: {
      type: Object,
    },
    relevanceType: {
      type: String,
    },
    isCalendar: {
      type: Boolean,
      default: true,
    },
    date: {
      type: String,
    },
  },
  data() {
    const dictList = storage.get("dictList");

    return {
      uploadPath: {},
      todo_relevance_type: dictList.todo_relevance_type,
      ruleForm: {
        stage: {
          id: "",
          name: "",
        },
        endDate: "",
        startDate: dayjs(new Date()).format("YYYY-MM-DD HH:mm:ss"),
        content: "",
        name: "",
        consumeTime: "",
        sort: 0,
        checked: false,
        fileList: [],
        remindDate: "",
        hostUser: {},
      },
      rules: {
        number1: [
          { required: true, message: "请输入复制数量", trigger: "blur" },
          { type: "number", message: "数量必须为数字值" },
        ],
        startDate: [
          { required: true, message: "请选择开始时间", trigger: "change" },
        ],
      },
      isShowInput: false,
      stageDisabled: false,
      allCaseList: [],
      customerList: [],
      header: {
        token: storage.get("token"),
      },
      uploadUrl: `${baseUrl ? baseUrl : ""}/law/sys/file/webupload/upload`,
      imgDialogVisible: false,
      imgUrl: "",
    };
  },
  mounted() {
    this.id = this.$route.query.id;

    // 当 model 发生改变时，为表单设置值
    this.$watch("model", (e) => {
      if (e) {
        this.ruleForm = JSON.parse(JSON.stringify(e)); 
        this.ruleForm.checked = this.ruleForm.status === "2" ? true : false;
      } else {
        this.ruleForm = {
          stage: {
            id: "",
            name: "",
          },
          endDate: "",
          startDate: dayjs(new Date()).format("YYYY-MM-DD HH:mm:ss"),
          content: "",
          name: "",
          consumeTime: "",
          sort: 0,
          checked: false,
          parent: "",
          fileList: [],
          remindDate: "",
          hostUser: {},
        };
      }
    });
    this.$watch("visible", () => {
      this.ruleForm.client = this.id; 
      if (this.visible) {
        console.log(this.model);
        this.uploadPath = this.model
          ? { "todoInfo.id": this.model.id }
          : { uploadPath: "/lawcase/todoFile/" };
        this.uploadUrl = this.model
          ? `${baseUrl ? baseUrl : ""}/law/lawcase/todoInfo/fileUpload`
          : `${baseUrl ? baseUrl : ""}/law/sys/file/webupload/upload`;
        if (this.selectStage) {
          this.stageDisabled = true;
          this.ruleForm.stage = this.selectStage;
        } else {
          this.stageDisabled = false;
        }
        if (this.relevanceType === "2") {
          this.getAllCaseList();
        } else if (this.relevanceType === "1") {
          this.getCustomerList();
        }
        if (this.visible && this.date) {
          this.ruleForm.startDate = this.date;
        }
      }else{
		 this.ruleForm = {
		   stage: {
		     id: "",
		     name: "",
		   },
		   endDate: "",
		   startDate: dayjs(new Date()).format("YYYY-MM-DD HH:mm:ss"),
		   content: "",
		   name: "",
		   consumeTime: "",
		   sort: 0,
		   checked: false,
		   parent: "",
		   fileList: [],
		   remindDate: "",
		   hostUser: {},
		 };
	  }
    });
  },
  methods: {
    selectRelevanceType(e) {
      this.$emit("changeRelevanceType", e);
    },
    preview(item) {
      console.log(item);
      if (item.name.IsPicture()) {
        this.imgDialogVisible = true;
        this.imgUrl = item.fullPath;
        return;
      }
      let params = {
        _w_fname: item.path,
        _w_fileid: item.id,
        operateType: "write",
      };
      getUrl(params).then((res) => {
        if (res) {
          // 跳转 使用sessionStorage，避免关键信息在ip中暴露
          // 使用push会停留当前页面，故不采纳
          // params 传递参数，子组件无法渲染iframe组件，故不采纳
          sessionStorage.wpsUrl = res.wpsUrl;
          sessionStorage.token = res.token;
          const jump = this.$router.resolve({ name: "viewFile" });
          window.open(jump.href, "_blank");
        } else {
          this.$message.error("请求错误！");
        }
      });
    },
    // 上传文件
    handleUpload(response, file, fileList) {
      console.log(fileList);
      if (this.model) {
        if (response.success) {
          this.$message.success("上传成功");
          this.ruleForm.fileList = [];
          fileList.map((item) => {
            this.ruleForm.fileList.push({
              createDate: item.createDate || "",
              name: item.name,
              path: item.path ? item.path : item.response.url,
            });
          });
          this.$emit("changeFile");
        } else {
          this.$message.error("上传失败");
        }
      } else {
        this.handleFileChange(response, fileList);
      }
    },
    handleFileChange(response, fileList) {
      if (response.success || response.status === "success") {
        if (fileList.length) {
          this.ruleForm.fileList = [];
          fileList.map((item) => {
            this.ruleForm.fileList.push({
              createDate: item.createDate || "",
              name: item.name,
              path: item.path ? item.path : item.response.url,
            });
          });
        } else {
          this.ruleForm.fileList = [];
        }
      } else {
        this.$message.error("上传失败");
      }
    },
    handleRemove(file, fileList) {
      console.log(fileList);
      if (this.model) {
        let formData = new FormData();
        formData.append("ids", file.id);
        deleteFile(formData).then((res) => {
          if (res.success) {
            this.$message.success("删除成功");
            this.ruleForm.fileList = fileList;
            this.$emit("changeFile");
          } else {
            this.$message.error("删除失败");
          }
        });
      } else {
        this.handleFileChange(file, fileList);
      }
    },
    // 获取所有客户
    getCustomerList() {
      getList().then((res) => {
        this.allCaseList = res.page.list;
      });
    },
    // 获取所有通过审核案件列表
    getAllCaseList() {
      allCaseData().then((res) => {
        this.allCaseList = res.data;
      });
    },
    handleChange(e) {
      this.stageList.map((item) => {
        if (item.id === e) {
          this.ruleForm.stage.name = item.name;
        }
      });
    },
    handleOk(formName) {
      this.$emit("ok");
    },
    handleCancel() {
      this.$emit("cancel");
    },
  },
};
</script>

<style lang="less" scoped>
.form-label {
  min-width: 70px;
  color: #888;
}
/deep/ .yes{
   textarea{
    background-color:#f0f9eb;
  }
}
/deep/ .no{
   textarea{
    background-color:#fff6f7;
  }
}
</style>
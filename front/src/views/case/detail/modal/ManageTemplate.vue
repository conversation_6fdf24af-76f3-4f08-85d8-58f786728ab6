<template>
  <el-dialog
    :visible="visible"
    width="60%"
    :before-close="handleCancel"
    :close-on-press-escape="false"
    :close-on-click-modal="false"
  >
    <div>
      <div class="flex align-center">
        <el-input
          placeholder="请输入内容"
          v-model="searchKey"
          style="width: 60%"
          class="mr20"
        >
          <i slot="prefix" class="el-input__icon el-icon-search"></i>
        </el-input>
	   
        <i
          class="el-icon-plus primary cursor"
          v-if="1==2"
          @click="handleShowTempModal('add')"
        >
          添加</i
        >
      </div>
      <div v-if="activeTab === 0">
        <div v-if="templateList.length" class="content-wrap" :key="1">
          <div
            :class="[
              'template-content',
              templateIndex === index ? 'gray-template' : '',
            ]"
            v-for="(item, index) in templateList"
            :key="item.id"
            @click="handleSelect(index)"
          >
            <div class="icon-wrap">
              <i
                class="el-icon-success primary"
                style="font-size: 20px"
                v-if="templateIndex === index"
              ></i>
              <div class="circle cursor" v-else></div>
            </div>
            <div class="content-info">
              <div class="stage-name">
                <div class="name-txt">{{ item.name }}({{item.caseModelName}})</div>
                <el-tooltip
                  class="item"
                  effect="dark"
                  content="查看"
				    
                  placement="top"
                >
                  <i
                    class="el-icon-edit cursor mr20"
                    @click.stop="handleShowTempModal('edit', item)"
                  >
                  </i>
                </el-tooltip>
                <el-tooltip
                  class="item"
                  effect="dark"
				    v-if="1==2"
                  content="删除"
                  placement="top"
                >
                  <i
                    class="el-icon-delete cursor"
                    @click.stop="handleDeleteTemp(item)"
                  >
                  </i>
                </el-tooltip>
              </div>
              <div class="stage-content">
                {{ item.stageNames }}
              </div>
            </div>
          </div>
        </div>
        <div v-else>
          <NoData text="还没有模板，点“添加”可创建自己的阶段模板~" />
        </div>
      </div>
      <div v-else>
        <div v-if="templateList.length" class="content-wrap" :key="2">
          <div
            :class="[
              'template-content',
              templateIndex === index ? 'gray-template' : '',
            ]"
            v-for="(item, index) in templateList"
            :key="item.id"
            @click="handleSelect(index)"
          >
            <div class="icon-wrap">
              <i
                class="el-icon-success primary"
                style="font-size: 20px"
                v-if="templateIndex === index"
              ></i>
              <div class="circle cursor" v-else></div>
            </div>
            <div class="content-info">
              <div class="stage-name">
                <div class="name-txt">{{ item.name }}</div>
                <div
                  v-if="
                    user.type.indexOf('2') > -1 || user.type.indexOf('1') > -1
                  "
                >
                  <el-tooltip
                    class="item"
                    effect="dark"
                    content="查看"
                    placement="top"
                  >
                    <i
                      class="el-icon-edit cursor mr20"
                      @click="handleShowTempModal('edit', item)"
                    >
                    </i>
                  </el-tooltip>
                  <el-tooltip
                    class="item"
                    effect="dark"
                    content="删除"
                    placement="top"
                     v-if="1==2"
                  >
                    <i
                      class="el-icon-delete cursor"
                      @click.stop="handleDeleteTemp(item)"
                    >
                    </i>
                  </el-tooltip>
                </div>
              </div>
              <div class="stage-content">
                {{ item.stageNames }}
              </div>
            </div>
          </div>
        </div>
        <div v-else>
          <NoData />
        </div>
      </div>
    </div>
    <div class="flex space-between no-title-header" slot="title">
      <div class="flex">
        <div
          v-for="(item, index) in tabList"
          :key="index"
          :class="[
            'header-item',
            activeTab === index ? 'header-item-active' : '',
          ]"
          @click="tabChange(index)"
        >
          {{ item }}
        </div>
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <!-- <el-button
        @click="
          () => {
            $emit('cancel');
          }
        "
        >取 消</el-button
      > -->
      <el-button type="primary" @click="handleOk">应用</el-button>
    </span>
    <el-dialog
      width="80%"
      title="设置阶段模板"
      :visible.sync="innerVisible"
      append-to-body
      class="new-template"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
    >
      <el-form
        :model="ruleForm"
        :rules="rules"
        ref="ruleForm"
        label-width="80px"
        class="ruleForm-box"
      >
        <el-row>
          <el-col :span="12">
            <el-form-item label="模板名称" prop="name">
              <el-input
                placeholder="请输入模板名称"
                v-model.number="ruleForm.name"
                type="text"
              >
              </el-input>
            </el-form-item>
          </el-col>
		  <el-col :span="12">
		   
		    <el-form-item label="模板类型" prop="caseModelType">
		      <treeselect
		        placeholder="请选择类型" 
		        v-model="ruleForm.caseModelType"
		        :options="caseModelTypeList"
		        :normalizer="normalizer"
		      />
		    </el-form-item>
		  </el-col>
        </el-row>
      </el-form>
      <el-row class="template-content">
        <el-col :span="8" class="content-left">
          <div class="left-header">阶段</div>
          <div class="left-stage">
            <el-input
              placeholder="请输入阶段名称，按enter新建"
              v-model="stageName"
              @keyup.enter.native="enterName"
              class="mg10"
            >
            </el-input>
            <div class="wrapper">
              <div
                :class="[
                  'stage-item',
                  index === stageIndex ? 'stage-active' : '',
                ]"
                v-for="(item, index) in stageList"
                :key="item.id"
                @click="handleStage(index)"
              >
                <div class="item-txt">
                  <input
                    class="item-input cursor"
                    type="text"
                    v-model="item.name"
                    :readonly="!item.isEdit"
                    :ref="'inputRef' + index"
                    @blur="handleBlur(index)"
                  />
                </div>
                <div class="iconwrap">
                  <i
                    class="el-icon-edit cursor mr10"
                    @click="handleEditStage(index)"
                  >
                  </i>
                  <i
                    class="el-icon-delete cursor"
                    @click.stop="handleDeleteStage(index)"
                  >
                  </i>
                </div>
                <div class="item-num">
                  {{ item.stageRecordList.length }}条记录
                </div>
              </div>
            </div>
          </div>
        </el-col>
        <el-col :span="16" class="content-right" v-if="stageIndex != null">
          <div v-if="activeTab === 0">
            <div class="right-title">
              <div class="title-txt" v-if="stageList.length">
                【{{ stageList[stageIndex].name }}】记录
              </div>
              <i class="el-icon-plus primary cursor"   v-if="1==2" @click="handleAddRecord">
                添加记录</i
              >
            </div>
            <div class="right-table">
              <div class="table-header">
                <div class="item-title">工作摘要</div>
                <div class="item-detail">工作详情</div>
                <div class="item-operate">操作</div>
              </div>
              <div
                class="table-content"
                v-for="(item, index) in stageList[stageIndex].stageRecordList"
                :key="index"
              >
                <div class="item-title">
                  <el-input
                    type="textarea"
                    placeholder="请输入内容"
                    v-model="item.name"
                    autosize
                  ></el-input>
                </div>
                <div class="item-detail">
                  <el-input
                    type="textarea"
                    placeholder="请输入内容"
                    v-model="item.content"
                    autosize
                  ></el-input>
                </div>
                <div class="item-operate">
                  <i
                    class="el-icon-delete cursor"
                    @click="handleDeleteRecord(index)"
                  ></i>
                </div>
              </div>
            </div>
          </div>
          <div v-else>
            <div class="right-title">
              <div class="title-txt" v-if="stageList.length">
                【{{ stageList[stageIndex].name }}】记录
              </div>
              <i class="el-icon-plus primary cursor" @click="handleAddRecord"   v-if="1==2">
                添加记录</i
              >
            </div>
            <input ref="uploadInput" v-show="false" @change="fileChange" type="file" />
            <el-tree
              :data="stageList[stageIndex].stageRecordList"
              node-key="id"
              :ref="'tree' + stageIndex"
              :props="defaultProps"
              icon-class="el-icon-plus"
            >
              <div :class="['custom-tree-node']" slot-scope="{ node, data }">
                <div class="flex justify-between align-center mg20">
                  <div
                    class=""
                    style="
                      width: calc(70%);
                      white-space: normal;
                      word-break: break-all;
                      word-wrap: break-word;
                    "
                  >
                    <span>{{ data.name }} <span  v-if="data.nums>0" style="color: red;font-size: 8px;">(有附件)</span></span>
                  </div>
                  <div class="node-btn">
                    <el-tooltip
                        class="item"
                        effect="dark"
						  v-if="1==2"
                        content="下移"
                        placement="top">
                      <i class="el-icon-bottom" @click.stop="handleUpAndDown(0, data, node)"></i>
                    </el-tooltip>
                    <el-tooltip
                        class="item"
                        effect="dark"
                        content="上移"
						  v-if="1==2"
                        placement="top">
                      <i class="el-icon-top" @click.stop="handleUpAndDown(1, data, node)"></i>
                    </el-tooltip>
                    <el-tooltip
                        class="item"
                        effect="dark"
                        content="上传附件"
						  v-if="1==2"
                        placement="top">
                      <i class="el-icon-upload2" @click.stop="uploadFile(data)"></i>
                    </el-tooltip>
					<el-tooltip
					    class="item"
					    effect="dark"
					    content="查看附件"
					    placement="top">
						<i  v-if="data.nums>0" class="el-icon-s-flag" @click.stop="showFile(data)"></i>
					  <i  v-else class="el-icon-upload" @click.stop="showFile(data)"></i>
					</el-tooltip>
                    <el-tooltip
                      class="item"
                      effect="dark"
                      content="新增记录"
					    v-if="1==2"
                      placement="top"
                    >
                      <i
                        class="el-icon-plus"
                        @click.stop="handleAddTreeRecord(data)"
                      ></i>
                    </el-tooltip>
                    <el-tooltip
                      class="item"
                      effect="dark"
                      content="编辑记录"
					    v-if="1==2"
                      placement="top"
                    >
                      <i
                        class="el-icon-edit"
                        @click.stop="handleEditRecord(data)"
                      ></i>
                    </el-tooltip>
                    <el-tooltip
                      class="item"
                      effect="dark"
					    v-if="1==2"
                      content="删除"
                      placement="top"
                    >
                      <i
                        class="el-icon-delete"
                        @click.stop="handleDeleteTreeRecord(data, stageIndex)"
                      ></i>
                    </el-tooltip>
                  </div>
                </div>
                <div class="item-p">{{ data.content }}</div>
              </div>
            </el-tree>
          </div>
        </el-col>
      </el-row>

      <span slot="footer" class="dialog-footer">
        <!-- <el-button type="primary" @click="handleOk">保存并应用</el-button> -->
        <el-button type="primary" @click="handleSaveTemplate">保存</el-button>
      </span>
      <el-dialog
        width="80%"
        :title="recordType === 'add' ? '新增记录' : '修改记录'"
        :visible.sync="recordVisible"
        append-to-body
        class="new-template"
        :close-on-press-escape="false"
        :close-on-click-modal="false"
      >
        <el-input
          type="textarea"
          placeholder="请输入记录内容"
          v-model="recordContent"
          autosize
        ></el-input>
        <el-checkbox v-model="isNotAuditProhibit" v-if="ruleForm.isSystem ==='1'" style="margin-top:20px">未审核期间禁止编辑 </el-checkbox>
        <span slot="footer" class="dialog-footer">
          <!-- <el-button type="primary" @click="handleOk">保存并应用</el-button> -->
          <el-button   v-if="1==2" type="primary" @click="handleConfirm">确定</el-button>
        </span>
      </el-dialog>
    </el-dialog>
	
	
	
	
	<el-dialog
	  width="80%"
	  :title="showFileTitle"
	  :visible.sync="showFileWin"
	  append-to-body
	  class="new-template"
	  :close-on-press-escape="false"
	  :close-on-click-modal="false"
	>
	    
		<el-upload
		action=""
		  class="upload-demo"   
		  :file-list="showFlieList"
		  :on-preview="preview"
		   :on-remove="handleRemove"
		>
		  
		</el-upload>
					
	    
	</el-dialog>
	
	<el-dialog title="查看" :visible.sync="imgDialogVisible"   append-to-body width="60%">
	  <img :src="imgUrl" alt="" style="width: 100%" />
	</el-dialog>
	
	<el-dialog title="查看" :visible.sync="fileDialogVisible"   append-to-body width="60%">
	  <a :href="imgUrl" target="_blank" class="buttonText" style="padding-left: 47%;">点击下载</a>
	</el-dialog>
	
  </el-dialog>
  
  

</template>

<script>
import { isEmpty, cloneDeep } from "lodash";

import { caseModelTypeData } from "@/api/case/manage";
import NoData from "@/components/NoData";
import storage from "store";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import {
  getTemplateList,
  saveTemplate,
  delTemplate,
  queryTemplateById,
  uploadFile,
  fileList,
  deleteFile,
} from "@/api/case/template";
import {Loading} from "element-ui";

const originData = [
  {
    name: "阶段一",
    isEdit: false,
    stageRecordList: [
    ],
  },
  {
    name: "阶段二",
    isEdit: false,
    stageRecordList: [],
  },
  {
    name: "阶段三",
    isEdit: false,
    stageRecordList: [],
  },
];
export default {
  name: "CreateStage",
  components: {
	Treeselect,
    NoData,
  },
  props: {
    visible: {
      type: Boolean,
      required: true,
    },
    loading: {
      type: Boolean,
      default: () => false,
    },
    model: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
		normalizer(node) { 
		  return {
		    id: node.id,
		    label: node.pname,
		    children: node.children,
		  };
		},
		showFileWin:false,
		showFileTitle:'查看附件',
		showFlieList:[],
		imgDialogVisible:false,
		fileDialogVisible:false,
		imgUrl:'',
      defaultProps: {
        children: "children",
        label: "name",
      },
      isDialogLoading: false,
      user: storage.get("user"),
      ruleForm: {
        name: "",
        isSystem: '0',
		caseModelType: null,
      },
      rules: {
        name: [{ required: true, message: "请输入模板名称", trigger: "blur" }],
		 caseModelType: [{ required: true, message: "请选择类型", trigger: "blur" }],
      },
      tabList: ["我的模板", "公共模板库"],
	    caseModelTypeList: [],
		
      activeTab: 0,
      searchKey: "",
      templateIndex: 0,
      templateList: [
        {
          name: "民事一审（原告）",
          stage: "材料准备，立案，审理，裁判，结案",
        },
        {
          name: "民事一、二审（被告）",
          stage: "材料准备，答辩，审理，裁判，结案",
        },
      ],
      innerVisible: false,
      stageName: "",
      stageList: originData,
      stageIndex: 0,
      recordVisible: false,
      recordContent: "",
      recordType: "",
      isNotAuditProhibit: false,
      uploadUrl: `${process.env.VUE_APP_API_BASE_URL}/law/lawcase/stageRecord/fileUpload`,
      header: {
        token: storage.get("token"),
      },
      clickedData: null
    };
  },
  mounted() {
    this.getTemplateList();
	
	this.getcaseModelType();

    // 当 model 发生改变时，为表单设置值
    this.$watch(
      "model",
      (e) => {
        if (!isEmpty(e)) {
          this.ruleForm = JSON.parse(JSON.stringify(this.model));
        }

        // this.model && this.$refs.ruleForm.resetFields()
      },
      {
        deep: true,
        immediate: true,
      }
    );
  },
  methods: {
	  handleCaseModelTypeDesc(e) {   
		  console.log("========1===")
	      this.ruleForm.caseModelType.id = e.id;
	      this.ruleForm.caseModelType.name = e.name;  
		  this.ruleForm.caseModelType.flag = e.flag;  
	      // this.caseCause = e.id;
	  },
	  getcaseModelType(){
		  caseModelTypeData().then((res) => {
			  this.caseModelTypeList =  res.treeData
		  });
	  },
    // 获取模板列表
    getTemplateList() {
      let formData = new FormData();
      formData.append("type", this.activeTab === 0 ? 1 : 2);
      getTemplateList(formData).then((res) => {
        this.templateList = res.page.list;
      });
    },
    // 阶段添加记录
    handleAddRecord() {
      if (this.activeTab === 0) {
        this.stageList[this.stageIndex].stageRecordList.push({
          name: "",
          content: "",
        });
      } else {
        this.parentData = "";
        this.recordType = "add";
        this.recordVisible = true;
      }
    },
    // 删除阶段记录
    handleDeleteRecord(index) {
      this.stageList[this.stageIndex].stageRecordList.splice(index, 1);
    },
    // 编辑树结构记录
    handleEditRecord(data) {
      this.parentData = data;
      this.recordVisible = true;
      this.recordContent = data.name;
      this.isNotAuditProhibit = data.isNotAuditProhibit ==='1'?true:false
      console.log(data, this.isNotAuditProhibit);

      this.recordType = "edit";
    },
    // 上下移动
    handleUpAndDown(type, data, clickedNode) {
      let cIndex = -1;
      for (let i = 0; i < clickedNode.parent.childNodes.length; i++) {
        let node = clickedNode.parent.childNodes[i]
        if (node.id === clickedNode.id) {
          cIndex = i;
          break
        }
      }
      console.log(cIndex)
      if (cIndex === -1) {
        return
      }

      const parent = clickedNode.parent;
      const children = parent.data.children || parent.data;

      console.log(parent)
      console.log(children)

      if (cIndex === 0 && type === 1){
        return;
      }
      if (cIndex === children.length - 1 && type === 0){
        return;
      }
      let newArray = []
      for (let i = 0; i < clickedNode.parent.childNodes.length; i++) {
        let data = clickedNode.parent.childNodes[i].data
        newArray.push(data)
      }

      const tempChildrenNodex1 = newArray[type === 0 ? cIndex + 1 : cIndex - 1];

      this.$set(children, cIndex, tempChildrenNodex1)
      this.$nextTick(() => {
        this.$set(children, type === 0 ? cIndex + 1 : cIndex - 1, newArray[cIndex])
      })
      console.log(children)
    },

    // 新增树结构记录
    handleAddTreeRecord(data) {
      this.recordType = "add";
      this.parentData = data;
      console.log(data);
      this.recordContent = "";
      this.isNotAuditProhibit = false
      this.recordVisible = true;
    },
    handleConfirm() {
      if (!this.recordContent) {
        this.$message({
          type: "error",
          message: "请输入记录内容!",
        });
        return;
      }

      if (this.recordType === "add") {
        let data = {
          children: [],
          content: "",
          name: this.recordContent,
          id: this.createUUID(10, 10),
          isNotAuditProhibit: this.isNotAuditProhibit?'1':'0'
        };

        this.$refs[`tree${this.stageIndex}`].append(data, this.parentData);
      } else {
        let node = this.$refs[`tree${this.stageIndex}`].getNode(
          this.parentData.id
        );
        node.data.name = this.recordContent;
        node.data.isNotAuditProhibit = this.isNotAuditProhibit?'1':'0'
        console.log(node,this.isNotAuditProhibit);
      }
      this.recordVisible = false;
      this.recordContent = "";
    },
    createUUID(len, radix) {
      var chars = "0123456789".split("");
      var uuid = [],
        i;
      radix = radix || chars.length;
      if (len) {
        for (i = 0; i < len; i++) {
          uuid[i] = chars[0 | (Math.random() * radix)];
        }
      }
      return uuid.join("");
    },
    // 删除树结构记录
    handleDeleteTreeRecord(data, index) {
      console.log(index);
      this.$refs[`tree${index}`].remove(data);
      console.log(this.stageList);
    },
    // 删除阶段
    handleDeleteStage(index) {
      this.stageList.splice(index, 1);
      this.stageIndex = 0;
      console.log(this.stageList[this.stageIndex].name)
    },
    // 编辑阶段名称
    handleEditStage(index) {
      this.stageList[index].isEdit = true;
      let ref = `inputRef${index}`;
      this.$refs[ref][0].focus();
    },
    // 编辑阶段输入框失去焦点
    handleBlur(index) {
      this.stageList[index].isEdit = false;
    },
    // 添加阶段名称
    enterName() {
      this.stageList.push({
        name: this.stageName,
        isEdit: false,
        stageRecordList: [],
      });
      this.stageName = "";
    },
    handleStage(index) {
      this.stageIndex = index;
      console.log(this.stageList[this.stageIndex])
    },
    tabChange(index) {
      this.activeTab = index;
      this.handleSelect(0);
      this.getTemplateList();
    },
    handleSelect(index) {
      this.templateIndex = index;
    },
    // 显示添加模板弹框
    handleShowTempModal(type, item) {
      this.stageList = [
        {
          name: "阶段一",
          isEdit: false,
          stageRecordList: [
          ],
        },
        {
          name: "阶段二",
          isEdit: false,
          stageRecordList: [],
        },
        {
          name: "阶段三",
          isEdit: false,
          stageRecordList: [],
        },
      ];
      this.stageIndex = 0
      this.type = type;
      if (type === "edit") {
        this.templateId = item.id;
        this.getTempInfo();
      } else {
        this.templateId = "";
        this.stageList = originData;
        this.ruleForm = {
          name: "",
          isSystem: '0', 
		  caseModelType:  null
        };
      }
      this.innerVisible = true;
    },
    getTempInfo() {
      let formData = new FormData();
      formData.append("id", this.templateId);
      queryTemplateById(formData).then((res) => {
        this.stageList = res.data.stageList;
        this.ruleForm = {
          name: res.data.name,
          isSystem: res.data.isSystem,
		  caseModelType:res.data.caseModelType!=null?res.data.caseModelType:null
        };
		console.log(this.ruleForm)
		console.log("=======")
      });
    },
    // 删除模板
    handleDeleteTemp(mdl) {
      this.$confirm("此操作将删除该模板, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          let formData = new FormData();
          formData.append("ids", mdl.id);
          delTemplate(formData).then((res) => {
            if (res.success) {
              this.getTemplateList();
              this.$message({
                type: "success",
                message: "删除成功!",
              });
            }
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    handleSaveTemplate() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) { 
          const param = {
            type: this.activeTab === 0?1:2,
            isSystem: this.ruleForm.isSystem,
			caseModelType:this.ruleForm.caseModelType,
            id: this.templateId || "",
            name: this.ruleForm.name,
            stageList: this.stageList,
          };
          saveTemplate(param).then((res) => {
            if (res.success) {
              this.innerVisible = false;
              this.getTemplateList();
              this.$message({
                type: "success",
                message: "保存成功!",
              });
            } else {
              this.$message.error(res.msg);
            }
          });
        } else {
          return;
        }
      });
    },
    handleOk() {
      let tempalteId = this.templateList[this.templateIndex].id;
      this.$emit("ok", tempalteId);
    },
    handleCancel() {
      this.$emit("cancel");
      this.$nextTick(() => {
        this.ruleForm = {
          name: "",
          isSystem: '0'
        };
      });
    },
    // 上传附件
    uploadFile(data){
      this.clickedData = data
      this.$refs.uploadInput.dispatchEvent(new MouseEvent('click'))
    },
    fileChange(file){
      let loadingInstance1 = Loading.service({ fullscreen: true})
      let formData = new FormData();
      formData.append("recordId", this.clickedData.id);
      formData.append("file", file.target.files[0]);
      uploadFile(formData).then(response => {
        if (response.success){
          this.$message.success("上传附件成功！");
		   this.getTempInfo();
        }else {
          this.$message.error("上传附件失败！");
        }
      }).catch(error => {
        this.$message.error("上传附件失败！");
        console.log(error)
      }).finally(() => {
        loadingInstance1.close()
      })
    },showFile(data){//查看附件
		this.showFileWin=true;
		this.showFileTitle='查看附件-'+data.name;
		 this.getFileList(data.id)
	 
	},getFileList(id){
		let formData = new FormData();
		formData.append("id", id); 
		fileList(formData).then((res) => {
			console.log(res)
		    this.showFlieList=res.list;
		});
	},preview(item){
		
		if (item.name.IsPicture()) {
		  this.imgDialogVisible = true;
		  this.imgUrl = item.fullPath;
		  return;
		}
		
		this.fileDialogVisible=true;
		  this.imgUrl = item.fullPath;
		
		
		
	},  handleRemove(file, fileList) {
      let formData = new FormData();
      formData.append("id", file.id);
      deleteFile(formData).then((res) => {
        if (res.success) {
          this.$message.success("删除成功"); 
		    this.getTempInfo();
        } else {
          this.$message.error("删除失败");
        }
      });
    },
  },
};
</script>

<style scoped lang="less">
.no-title-header {
  position: relative;
  border-bottom: 1px solid #e1e4ee;
  display: flex;
  .header-item {
    color: #999;
    padding-bottom: 10px;
    font-size: 16px;
    margin-right: 30px;
    cursor: pointer;
  }
  .header-item-active {
    color: #333;
    font-weight: 700;
    border-bottom: 1px solid #409eff;
  }
}
.content-wrap {
  min-height: 300px;
  max-height: 400px;
  overflow-y: auto;
  margin: 20px 0;
  .template-content {
    display: flex;
    align-items: center;
    box-sizing: border-box;
    padding: 15px 10px;
    border-bottom: 1px solid #e1e4ee;
    &:first-child {
      border-top: 1px solid #e1e4ee;
    }
  }
  .gray-template {
    background: #f4f5fa;
  }
  .icon-wrap {
    width: 45px;
    .circle {
      width: 14px;
      height: 14px;
      border-radius: 50%;
      border: 1px solid #999;
    }
  }
  .content-info {
    box-sizing: border-box;
    flex: 1;
    padding-right: 20px;
    padding-left: 10px;
    .stage-name {
      display: flex;
      font-size: 16px;
      color: #333;
      .name-txt {
        flex: 1;
      }
    }
    .stage-content {
      padding-top: 10px;
      font-size: 14px;
      color: #9fa5b9;
    }
  }
}
.new-template {
  .template-content {
    border: 1px solid #e1e4ee;
    margin-top: 15px;
    display: flex;
    .content-left {
      // width: 230px;
      border-right: 1px solid #e1e4ee;
      min-height: 600px;
      .left-header {
        height: 56px;
        line-height: 56px;
        background: #f9f9f9;
        font-size: 16px;
        color: #333;
        padding-left: 10px;
        border-bottom: 1px solid #e1e4ee;
      }
      .left-stage {
        padding: 0 10px;
        .stage-item {
          position: relative;
          // width: 210px;
          box-sizing: border-box;
          height: 40px;
          padding: 0 10px;
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-bottom: 10px;
          cursor: pointer;
          .item-txt {
            width: 120px;
            font-size: 14px;
            color: #666;
            background: none;
            .item-input {
              border: none;
              background: none;
              &:focus-visible {
                outline: none;
              }
            }
          }
          .item-num {
            position: absolute;
            right: 20px;
            top: 11px;
            font-size: 14px;
            color: #9fa5b9;
            text-align: right;
          }
          .iconwrap {
            display: none;
          }
          &:hover {
            background: #f4f5fa;
          }
          &:hover .iconwrap {
            display: block;
          }
          &:hover .item-num {
            display: none;
          }
        }
        .stage-active {
          background: #f4f5fa;
        }
      }
    }
    .content-right {
      flex: 1;
      padding: 0 10px;
      .right-title {
        height: 56px;
        display: flex;
        align-items: center;
        .title-txt {
          font-size: 16px;
          color: #333;
          flex: 1;
        }
      }
    }
  }
}
.right-table {
  border: 1px solid #e1e4ee;
  border-bottom: none;
  margin-bottom: 20px;
  .table-header {
    display: flex;
    align-items: center;
    height: 42px;
    font-size: 14px;
    color: #999;
    border-bottom: 1px solid #e1e4ee;
  }
  .table-content {
    display: flex;
    border-bottom: 1px solid #e1e4ee;
  }
  .item-title {
    box-sizing: border-box;
    flex: 1;

    border-right: 1px solid #e1e4ee;
    padding: 10px;
  }
  .item-detail {
    box-sizing: border-box;
    flex: 2;
    border-right: 1px solid #e1e4ee;
    padding: 10px;
  }
  .item-operate {
    box-sizing: border-box;
    width: 50px;
    padding: 10px;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
.edit-icon {
  display: none;
}
.stage-item {
  position: relative;
  box-sizing: border-box;
  height: 93px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  padding: 0 20px;
  &:hover {
    background: #f4f5fa;
  }
  &:hover .edit-icon {
    display: block;
  }
  .star-icon {
    .top-line {
      position: absolute;
      height: 35px;
      top: 0px;
      border-right: 2px dashed #ccc;
      left: 28px;
    }
    .bottom-line {
      position: absolute;
      height: 35px;
      border-right: 2px dashed #ccc;
      left: 28px;
    }
  }
  &:last-child {
    .bottom-line {
      display: none;
    }
  }
  &:first-child {
    .top-line {
      display: none;
    }
  }
}
.custom-tree-node {
  margin-right: 5px;
  border-bottom: 1px dashed #e1e4ee;
  flex: 1;
  i {
    margin-left: 10px;
    font-size: 16px;
  }
  .node-btn {
    display: none;
  }
  .folder-img {
    width: 16px;
    height: 16px;
    margin-right: 10px;
  }
  &:hover .node-btn {
    display: block;
  }
  &:hover .ih-control {
    display: none;
  }
  .item-p {
    font-size: 14px;
    color: #999;
    white-space: pre-line;
    margin-bottom: 10px;
  }
  .ih-control {
    font-size: 12px;
    color: silver;
  }
}
.mg20 {
  margin: 20px 0;
}
/deep/ .el-tree-node__content {
  height: auto;
}
</style>

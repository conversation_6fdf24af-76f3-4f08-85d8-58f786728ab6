<template>
  <el-dialog
    title="关联案件"
    :visible="visible"
    width="700px"
    :before-close="handleCancel"
    :close-on-press-escape="false"
    :close-on-click-modal="false"
  >
    <el-form
      :model="ruleForm"
      :rules="rules"
      ref="ruleForm"
      label-width="100px"
      class="ruleForm-box"
    >
      <el-row :gutter="20">
        <el-col :span="19">
          <el-form-item label="关联案件" prop="relatedCase">
            <el-select v-model="ruleForm.relatedCase" placeholder="请选择关联案件">
              <el-option
                :label="item.name"
                :value="item.id"
                v-for="item in caseList"
                :key="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="5">
          <el-button type="primary" @click="handleSave">关联案件</el-button>
        </el-col>
      </el-row>
    </el-form>
    <div>
      <el-table :data="relatedTableData" style="width: 100%">
        <el-table-column prop="relationCase.name" label="案件名称">
        </el-table-column>
        <el-table-column prop="relationCase.caseProgram.name" label="审理程序">
        </el-table-column>
        <el-table-column prop="relationCase.resultName" label="审理结果">
        </el-table-column>
        <el-table-column prop="relationCase.hostUser.name" label="主办人员">
        </el-table-column>
        <el-table-column label="操作">
          <template slot-scope="scope">
            <span class="cursor red" @click="handleCancelRelated(scope)"
              >取消关联</span
            >
          </template>
        </el-table-column>
      </el-table>
    </div>
  </el-dialog>
</template>

<script>
import { isEmpty, cloneDeep } from "lodash";
import {
  getRelateList,
  saveRelate,
  delRelate,
  getCaseList,
} from "@/api/case/caseRelation";
import storage from "store";
const dictList = storage.get("dictList");
const trial_resultList = dictList.trial_result;
export default {
  name: "RelatedCase",
  props: {
    visible: {
      type: Boolean,
      required: true,
    },
    loading: {
      type: Boolean,
      default: () => false,
    },
    model: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      ruleForm: {
        relatedCase: "",
      },
      rules: {
        relatedCase: [
          { required: true, message: "请选择案件", trigger: "blur" },
        ],
      },
      relatedTableData: [],
      caseList: [],
      relatedCase: "",
    };
  },
  mounted() {
    this.id = this.$route.query.id;

    // 当 model 发生改变时，为表单设置值
    this.$watch("visible", (e) => {
      if (e) {
        this.getCaseList();
        this.getRelateList();
      }
    });
  },
  methods: {
    //获取可关联的案件列表
    getCaseList() {
      let formData = new FormData();
      formData.append("caseId", this.id);
      getCaseList(formData).then((res) => {
        this.caseList = res.data;
      });
    },
    //获取关联列表
    getRelateList() {
      let formData = new FormData();
      formData.append("lawCase.id", this.id);
      getRelateList(formData).then((res) => {
        this.relatedTableData = res.page.list;
        this.relatedTableData.map((item) => {
          trial_resultList.map((child) => {
            if (item.relationCase.trialResult === child.value) {
              item.relationCase.resultName = child.label;
            }
          });
        });
      });
    },
    handleSave() {
      console.log(this.ruleForm.relatedCase)
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          let formData = new FormData();
          formData.append("lawCase.id", this.id);
          formData.append("relationCase.id", this.ruleForm.relatedCase);
          saveRelate(formData).then((res) => {
            if (res.success) {
              this.$message({
                type: "success",
                message: "关联成功!",
              });
              this.$emit("ok");
              this.getRelateList();
            } else {
              this.$message({
                type: "error",
                message: res.msg,
              });
            }
          });
        } else {
          return;
        }
      });
    },
    handleCancel() {
      this.$emit("cancel");
    },
    //取消关联
    handleCancelRelated(scope) {
      this.$confirm("确认取消关联该案件?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          let formData = new FormData();
          formData.append("ids", scope.row.id);
          delRelate(formData).then((res) => {
            if (res.success) {
              this.$message({
                type: "success",
                message: "取消关联成功!",
              });
              this.ruleForm.relatedCase = ''
              this.getRelateList();
              this.$emit("ok");
            } else {
              this.$message({
                type: "error",
                message: res.msg,
              });
            }
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消操作",
          });
        });
    },
  },
};
</script>

<style lang="less" scoped>
.red {
  color: red;
}
</style>
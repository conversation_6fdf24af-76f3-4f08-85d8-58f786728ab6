<template>
  <el-dialog
    lock-scroll
    append-to-body
    :title="model ? '编辑承办人员' : '新增承办人员'"
    :visible="visible"
    width="60%"
    :before-close="handleCancel"
    :close-on-press-escape="false"
    :close-on-click-modal="false"
  >
    <el-form
      :model="ruleForm"
      :rules="rules"
      ref="ruleForm"
      label-width="85px"
      class="ruleForm-box"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="姓名" prop="name">
            <el-input type="text" v-model="ruleForm.name"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="联系方式" prop="phone">
            <el-input type="text" v-model="ruleForm.phone"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="科室" prop="department">
            <el-input type="text" v-model="ruleForm.department"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="职务" prop="post">
            <el-input type="text" v-model="ruleForm.post"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="联系地址" prop="address">
            <el-input type="text" v-model="ruleForm.address"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="备注" prop="remarks">
            <el-input
              v-model="ruleForm.remarks"
              type="textarea"
              :rows="2"
              placeholder="请输入内容"
            >
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleCancel">取 消</el-button>
      <el-button type="primary" @click="handleOk('ruleForm')"
        >立即提交</el-button
      >
    </span>
  </el-dialog>
</template>

<script>
export default {
  name: "index",
  props: {
    visible: {
      type: Boolean,
      required: true,
    },
    loading: {
      type: Boolean,
      default: () => false,
    },
    model: {
      type: Object,
      default: () => null,
    },
  },
  data() {
    return {
      isShowMore: false,
      ruleForm: {
        name: "", // 姓名
        phone: "", // 联系方式
        department: "", // 科室
        post: "", // 职务
        address: "", // 联系地址
      },
      rules: {
        name: [{ required: true, message: "请输入姓名", trigger: "blur" }],
      },
    };
  },
  mounted() {
    // 当 model 发生改变时，为表单设置值
    this.$watch(
      "model",
      (e) => {
        if (e) {
          this.ruleForm = JSON.parse(JSON.stringify(this.model));
        } else {
          this.ruleForm = {
            id: '',
            name: "", // 姓名
            phone: "", // 联系方式
            department: "", // 科室
            post: "", // 职务
            address: "", // 联系地址
          };
        }
      },
      {
        deep: true,
        immediate: true,
      }
    );
  },
  methods: {
    handleOk(formName) {
      this.$emit("ok");
      // this.$refs[formName].resetFields();
    },
    handleCancel() {
      // this.$refs.ruleForm.resetFields();
      this.$emit("cancel");
    },
  },
};
</script>

<style lang="less" scoped>
.flex-center {
  color: #409eff;
  cursor: pointer;
}
h3 {
  margin: 20px 0;
}
.ruleForm-box {
  padding: 20px;
}
</style>
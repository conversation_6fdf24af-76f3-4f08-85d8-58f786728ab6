<template>
  <el-dialog
    lock-scroll
    append-to-body
    :title="model ? '编辑庭审记录' : '新增庭审记录'"
    :visible="visible"
    width="60%"
    :before-close="handleCancel"
    :close-on-press-escape="false"
    :close-on-click-modal="false"
  >
    <el-form
      :model="ruleForm"
      :rules="rules"
      ref="ruleForm"
      label-width="85px"
      class="ruleForm-box"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="开庭日期" prop="openCourtDate">
            <el-date-picker
              v-model="ruleForm.openCourtDate"
              type="date"
              placeholder="选择日期"
              value-format="yyyy-MM-dd HH:mm:ss"
            >
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="开庭类型" prop="openCourtType">
            <el-select v-model="ruleForm.openCourtType" placeholder="请选择">
              <el-option
                :label="item.label"
                :value="item.value"
                v-for="item in open_court_typeList"
                :key="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="出庭律师" prop="barrister.id">
            <el-select
              v-model="ruleForm.barrister.id"
              placeholder="请选择"
              @change="handleSelect"
            >
              <el-option
                :label="item.name"
                :value="item.id"
                v-for="item in briefList"
                :key="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="开庭地址" prop="openCourtAddress">
            <el-input
              type="text"
              v-model="ruleForm.openCourtAddress"
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="24">
          <el-form-item label="争议焦点" prop="controversyFocus">
            <el-input
              v-model="ruleForm.controversyFocus"
              type="textarea"
              :rows="2"
              placeholder="请输入内容"
            >
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="我方意见" prop="ourOpinion">
            <el-input
              v-model="ruleForm.ourOpinion"
              type="textarea"
              :rows="2"
              placeholder="请输入内容"
            >
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="对方意见" prop="otherOpinion">
            <el-input
              v-model="ruleForm.otherOpinion"
              type="textarea"
              :rows="2"
              placeholder="请输入内容"
            >
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="法官态度" prop="judgeAttitude">
            <el-input
              v-model="ruleForm.judgeAttitude"
              type="textarea"
              :rows="2"
              placeholder="请输入内容"
            >
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="庭审总结" prop="trialSummary">
            <el-input
              v-model="ruleForm.trialSummary"
              type="textarea"
              :rows="2"
              placeholder="请输入内容"
            >
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button
        @click="handleCancel "
        >取 消</el-button
      >
      <el-button
        type="primary"
        @click="handleOk('ruleForm')"
        >立即提交</el-button
      >
    </span>
  </el-dialog>
</template>

<script>
import storage from "store";
import { allBriefList } from "@/api/login";
export default {
  name: "index",
  props: {
    visible: {
      type: Boolean,
      required: true,
    },
    loading: {
      type: Boolean,
      default: () => false,
    },
    model: {
      type: Object,
      default: () => null,
    },
  },
  data() {
    const dictList = storage.get("dictList");

    return {
      open_court_typeList: dictList.open_court_type,
      isShowMore: false,
      ruleForm: {
        barrister: {
          // 出庭律师 数据来源：用户管理模块 /allBriefList 简短的所有用户信息接口
          id: "",
          name: "",
        },
        controversyFocus: "", // 争议焦点
        id: "",
        judgeAttitude: "", // 法官态度
        openCourtAddress: "", // 开庭地址
        openCourtDate: "", // 开庭日期
        openCourtType: "", // 开庭类型 字典：open_court_type
        otherOpinion: "", // 对方意见
        ourOpinion: "", // 我方意见
        remarks: "",
        trialSummary: "", // 庭审总结
      },
      briefList: [],
      rules: {
        startTime: [
          { required: true, message: "请选择开庭日期", trigger: "change" },
        ],
        'barrister.id': [
          { required: true, message: "请选择出庭律师", trigger: "change" },
        ],
      },
    };
  },
  mounted() {
    this.getAllBriefList();
    // 当 model 发生改变时，为表单设置值
    this.$watch(
      "model",
      (e) => {
        if (e) {
          this.ruleForm = JSON.parse(JSON.stringify(this.model));
        } else {
          this.ruleForm = {
            barrister: {
              // 出庭律师 数据来源：用户管理模块 /allBriefList 简短的所有用户信息接口
              id: "",
              name: "",
            },
            controversyFocus: "", // 争议焦点
            id: "",
            judgeAttitude: "", // 法官态度
            openCourtAddress: "", // 开庭地址
            openCourtDate: "", // 开庭日期
            openCourtType: "", // 开庭类型 字典：open_court_type
            otherOpinion: "", // 对方意见
            ourOpinion: "", // 我方意见
            remarks: "",
            trialSummary: "", // 庭审总结
          };
        }
      },
      {
        deep: true,
        immediate: true,
      }
    );
  },
  methods: {
    getAllBriefList() {
      allBriefList().then((res) => {
        this.briefList = res.data;
      });
    },
    handleSelect(e) {
      this.briefList.map((item) => {
        if (item.id === e) {
          this.ruleForm.barrister.name = item.name;
        }
      });
    },
    handleOk(formName) {
      this.$emit("ok");
      // this.$refs[formName].resetFields();
    },
    handleCancel() {
      // this.$refs.ruleForm.resetFields();
      this.$emit("cancel");
    },
  },
};
</script>

<style lang="less" scoped>
.flex-center {
  color: #409eff;
  cursor: pointer;
}
h3 {
  margin: 20px 0;
}
.ruleForm-box {
  // height: 70vh;
  overflow-y: scroll;
  padding: 20px;
}
</style>
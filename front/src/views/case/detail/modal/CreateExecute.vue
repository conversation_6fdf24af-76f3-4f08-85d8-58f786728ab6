<template>
  <el-dialog
    lock-scroll
    append-to-body
    :title="model ? '编辑执行情况' : '新增执行情况'"
    :visible="visible"
    width="60%"
    :before-close="handleCancel"
    :close-on-press-escape="false"
    :close-on-click-modal="false"
  >
    <el-form
      :model="ruleForm"
      :rules="rules"
      ref="ruleForm"
      label-width="85px"
      class="ruleForm-box"
    >
      <el-row>
        <el-col :span="24">
          <el-form-item label="关联案件" prop="caseName">
            <el-input type="text" v-model="caseInfo.name" disabled></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="申请执行日" prop="applyDate">
            <el-date-picker
              v-model="ruleForm.applyDate"
              type="date"
              placeholder="选择日期"
              value-format="yyyy-MM-dd HH:mm:ss"
            >
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="受理单位" prop="acceptUnit">
            <el-input type="text" v-model="ruleForm.acceptUnit"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="受理案号" prop="number">
            <el-input type="text" v-model="ruleForm.number"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="执行状态" prop="status">
            <el-select v-model="ruleForm.status" placeholder="请选择">
              <el-option
                :label="item.label"
                :value="item.value"
                v-for="item in execute_statusList"
                :key="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="执行措施" prop="measures">
            <el-select v-model="ruleForm.measures" placeholder="请选择">
              <el-option
                :label="item.label"
                :value="item.value"
                v-for="item in execute_measuresList"
                :key="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="执行请求" prop="executeRequest">
            <el-input
              v-model="ruleForm.executeRequest"
              type="textarea"
              :rows="2"
              placeholder="请输入内容"
            >
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="履行情况" prop="performance">
            <el-input
              v-model="ruleForm.performance"
              type="textarea"
              :rows="2"
              placeholder="请输入内容"
            >
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleCancel">取 消</el-button>
      <el-button type="primary" @click="handleOk('ruleForm')"
        >立即提交</el-button
      >
    </span>
  </el-dialog>
</template>

<script>
import { mapMutations, mapState } from "vuex";
import storage from "store";

export default {
  name: "index",
  props: {
    visible: {
      type: Boolean,
      required: true,
    },
    loading: {
      type: Boolean,
      default: () => false,
    },
    model: {
      type: Object,
      default: () => null,
    },
  },
  data() {
    const dictList = storage.get("dictList");

    return {
      execute_measuresList: dictList.execute_measures,
      execute_statusList: dictList.execute_status,
      isShowMore: false,
      ruleForm: {
        acceptUnit: "", // 受理单位
        applyDate: "", // 申请执行日
        executeRequest: "", // 执行请求
        id: "",
        measures: "", // 执行措施 字典：execute_measures
        number: "", // 执行案号
        performance: "", // 履行情况
        remarks: "",
        status: "", // 执行状态 字典：execute_status
      },
      rules: {
        applicant: [
          { required: true, message: "请输入申请人", trigger: "blur" },
        ],
        measures: [
          { required: true, message: "请选择执行措施", trigger: "change" },
        ],
        status: [
          { required: true, message: "请选择执行状态", trigger: "change" },
        ],
      },
    };
  },
  computed: mapState({
    caseInfo: (state) => state.caseInfo.caseInfo,
  }),
  mounted() {
    // 当 model 发生改变时，为表单设置值
    this.$watch(
      "model",
      (e) => {
        if (e) {
          this.ruleForm = JSON.parse(JSON.stringify(this.model));
        } else {
          this.ruleForm = {
            acceptUnit: "", // 受理单位
            applyDate: "", // 申请执行日
            executeRequest: "", // 执行请求
            id: "",
            measures: "", // 执行措施 字典：execute_measures
            number: "", // 执行案号
            performance: "", // 履行情况
            remarks: "",
            status: "", // 执行状态 字典：execute_status
          };
        }
      },
      {
        deep: true,
        immediate: true,
      }
    );
  },
  methods: {
    handleOk(formName) {
      this.$emit("ok");
      // this.$refs[formName].resetFields();
    },
    handleCancel() {
      // this.$refs.ruleForm.resetFields();
      this.$emit("cancel");
    },
  },
};
</script>

<style lang="less" scoped>
.flex-center {
  color: #409eff;
  cursor: pointer;
}
h3 {
  margin: 20px 0;
}
.ruleForm-box {
  padding: 20px;
}
</style>
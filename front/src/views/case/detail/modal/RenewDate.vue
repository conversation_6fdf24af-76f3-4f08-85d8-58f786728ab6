<template>
  <el-dialog
    lock-scroll
    append-to-body
    title="添加续封日期"
    :visible="visible"
    width="60%"
    :before-close="
      () => {
        $emit('cancel');
      }
    "
  >
    <el-form
      :model="ruleForm"
      :rules="rules"
      ref="ruleForm"
      label-width="100px"
      class="ruleForm-box"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="续封日期" prop="startTime">
            <el-date-picker
              v-model="ruleForm.startTime"
              type="date"
              placeholder="选择日期"
              value-format="yyyy-MM-dd HH:mm:ss"
            >
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="续封到期日" prop="startTime">
            <el-date-picker
              v-model="ruleForm.startTime"
              type="date"
              placeholder="选择日期"
              value-format="yyyy-MM-dd HH:mm:ss"
            >
            </el-date-picker>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="续封提醒" prop="">
            <el-date-picker
              v-model="ruleForm.startTime"
              type="date"
              placeholder="选择日期"
              value-format="yyyy-MM-dd HH:mm:ss"
            >
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="提醒方式" prop="">
            <el-select v-model="ruleForm.lawer" placeholder="请选择">
              <el-option label="区域一" value="shanghai"></el-option>
              <el-option label="区域二" value="beijing"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button
        @click="
          () => {
            $emit('ok');
          }
        "
        >取 消</el-button
      >
      <el-button
        type="primary"
        @click="
          () => {
            $emit('cancel');
          }
        "
        >立即提交</el-button
      >
    </span>
  </el-dialog>
</template>

<script>
export default {
  name: "RenewDate",
  props: {
    visible: {
      type: Boolean,
      required: true,
    },
    loading: {
      type: Boolean,
      default: () => false,
    },
    model: {
      type: Object,
      default: () => null,
    },
  },
  data() {
    return {
      isShowMore: false,
      ruleForm: {
        name: "",
        type: "个人",
        client: "是",
        caseType: "",
        caseNumber: "",
        caseReason: "",
        caseName: "",
        price: "",
        entrustedTime: "",
        registerDate: "",
        result: "",
        adjudicationDate: "",
        closingStatus: "",
        closingDate: "",
        filePerson: "",
        fileDate: "",
        keepingPlace: "",
        relatedClient: "",
        relatedProject: "",
        winAmount: "",
        contractAmount: "",
        collection: "",
        otherRemark: "",
      },
      rules: {
        startTime: [
          { required: true, message: "请选择开庭日期", trigger: "change" },
        ],
        lawer: [
          { required: true, message: "请选择出庭律师", trigger: "change" },
        ],
      },
    };
  },
  methods: {
    querySearch() {},
    handleSelect() {},
  },
};
</script>

<style lang="less" scoped>
.flex-center {
  color: #409eff;
  cursor: pointer;
}
h3 {
  margin: 20px 0;
}
.ruleForm-box {
  padding: 20px;
}
</style>
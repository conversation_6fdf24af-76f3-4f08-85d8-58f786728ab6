<template>
  <el-dialog
    title="共享成员管理"
    :visible="visible"
    width="600px"
    :before-close="handleCancel"
    :close-on-press-escape="false"
    :close-on-click-modal="false"
  >
    <div>
      <div class="">
        <el-input
          placeholder="请输入完整的名字后按回车键搜索"
          prefix-icon="el-icon-search"
          v-model="name"
          @change="handleSearch"
        >
        </el-input>
      </div>
      <div class="cursor border" @click="isShare = true">已分享</div>
      <div v-if="isShare">
        <div v-if="shareList.length">
          <div v-for="item in shareList" class="flex justify-between mg10">
            <div>{{ item.user.name }}</div>
            <el-button plain size="mini" @click="handleDelete(item.id)"
              >解除</el-button
            >
          </div>
        </div>
        <div v-else>暂无数据</div>
      </div>
      <div v-else>
        <div v-if="searchList.length">
          <div v-for="item in searchList" class="flex justify-between mg10">
            <div>{{ item.name }}</div>
            <el-button plain @click="handleAdd(item.id)" size="mini"
              >添加</el-button
            >
          </div>
        </div>
        <div v-else>暂无数据</div>
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button
        type="primary"
        @click="handleDeleteAll"
        :disabled="disabled"
        >解除所有共享</el-button
      >
    </span>
  </el-dialog>
</template>

<script>
import { isEmpty, cloneDeep } from "lodash";
import {
  getUserList,
  addUser,
  delUser,
  caseUserList,
} from "@/api/case/caseUser";

export default {
  name: "CreateStage",
  props: {
    visible: {
      type: Boolean,
      required: true,
    },
    loading: {
      type: Boolean,
      default: () => false,
    },
    model: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      name: "",
      shareList: [],
      searchList: [],
      isShare: true,
    };
  },
  computed: {
    disabled() {
      return this.isShare && this.shareList.length ? false : true;
    },
  },
  mounted() {
    this.id = this.$route.query.id;

    this.$watch("visible", (e) => {
      if (e) {
        this.getUserList();
      }
    });
  },
  methods: {
    getUserList() {
      let formData = new FormData();
      formData.append("lawCase.id", this.id);
      getUserList(formData).then((res) => {
        this.shareList = res.page.list;
      });
    },
    // 搜索
    handleSearch(e) {
      this.isShare = false;
      let formData = new FormData();
      formData.append("caseId", this.id);
      formData.append("name", e);
      caseUserList(formData).then((res) => {
        this.searchList = res.data;
      });
    },
    handleDelete(id) {
      let formData = new FormData();
      formData.append("ids", id);
      delUser(formData).then((res) => {
        if (res.success) {
          this.$message.success("解除成功");
          this.getUserList();
          this.$emit("refresh")
        }
      });
    },
    handleDeleteAll(){
      let ids = this.shareList.map(item=>item.id).join(',')
      this.handleDelete(ids)
    },
    handleAdd(id) {
      let formData = new FormData();
      formData.append("lawCase.id", this.id);
      formData.append("user.id", id);
      addUser(formData).then((res) => {
        if (res.success) {
          this.$message.success("添加成功");
          this.getUserList();
          this.$emit("refresh")
        }
      });
    },
    handleOk() {
      this.$emit("refresh");
    },
    handleCancel() {
      this.$emit("cancel");
    },
  },
};
</script>

<style lang="less" scoped>
.border {
  font-size: 16px;
  padding: 20px 0;
  border-bottom: 1px solid #e1e4ee;
}
</style>
<template>
  <el-dialog
    lock-scroll
    append-to-body
    :title="model ? '编辑财产保全' : '新增财产保全'"
    :visible="visible"
    width="60%"
    :before-close="handleCancel"
    :close-on-press-escape="false"
    :close-on-click-modal="false"
  >
    <el-form
      :model="ruleForm"
      :rules="rules"
      ref="ruleForm"
      label-width="85px"
      class="ruleForm-box"
    >
      <el-row>
        <el-col :span="24">
          <el-form-item label="关联案件" prop="caseName">
            <el-input type="text" v-model="caseInfo.name" disabled></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="申请人" prop="applicant">
            <el-input type="text" v-model="ruleForm.applicant"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="被申请人" prop="respondent">
            <el-input type="text" v-model="ruleForm.respondent"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="财产类型" prop="propertyType">
            <el-select v-model="ruleForm.propertyType" placeholder="请选择">
              <el-option
                :label="item.label"
                :value="item.value"
                v-for="item in property_typeList"
                :key="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="裁定书编号" prop="rulingNumber">
            <el-input type="text" v-model="ruleForm.rulingNumber"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="查封日期" prop="seizureDate">
            <el-date-picker
              v-model="ruleForm.seizureDate"
              type="date"
              placeholder="选择日期"
              value-format="yyyy-MM-dd HH:mm:ss"
            >
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="查封到期日" prop="seizureExpirationDate">
            <el-date-picker
              v-model="ruleForm.seizureExpirationDate"
              type="date"
              placeholder="选择日期"
              value-format="yyyy-MM-dd HH:mm:ss"
            >
            </el-date-picker>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="续封提醒" prop="continueRemindDate">
            <el-date-picker
              v-model="ruleForm.continueRemindDate"
              type="date"
              placeholder="选择日期"
              value-format="yyyy-MM-dd HH:mm:ss"
            >
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="提醒方式" prop="remindMode">
            <el-select v-model="ruleForm.remindMode" placeholder="请选择">
              <el-option label="微信" value="微信"></el-option>
              <el-option label="邮箱" value="邮箱"></el-option>
              <el-option label="微信+邮箱" value="微信+邮箱"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="受理单位" prop="acceptUnit">
            <el-input type="text" v-model="ruleForm.acceptUnit"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="承办人员" prop="undertakePerson">
            <el-input type="text" v-model="ruleForm.undertakePerson"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="保全金额" prop="preservationMoney">
            <el-input type="text" v-model.number="ruleForm.preservationMoney"
              ><template slot="append">元</template></el-input
            >
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="执行状态" prop="executeStatus">
            <el-select
              v-model="ruleForm.executeStatus"
              placeholder="请选择执行状态"
            >
              <el-option
                :label="item.label"
                :value="item.value"
                v-for="item in property_execute_statusList"
                :key="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="备注" prop="remarks">
            <el-input
              v-model="ruleForm.remarks"
              type="textarea"
              :rows="2"
              placeholder="请输入内容"
            >
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleCancel">取 消</el-button>
      <el-button type="primary" @click="handleOk('ruleForm')"
        >立即提交</el-button
      >
    </span>
  </el-dialog>
</template>

<script>
import { mapMutations, mapState } from "vuex";
import storage from "store";

export default {
  name: "index",
  props: {
    visible: {
      type: Boolean,
      required: true,
    },
    loading: {
      type: Boolean,
      default: () => false,
    },
    model: {
      type: Object,
      default: () => null,
    },
  },
  data() {
    const dictList = storage.get("dictList");

    return {
      property_execute_statusList: dictList.property_execute_status,
      property_typeList: dictList.property_type,
      isShowMore: false,
      ruleForm: {
        acceptUnit: "", // 受理单位
        applicant: "", // 申请人
        continueRemindDate: "", // 续封提醒
        executeStatus: "", // 执行状态 字典：property_execute_status
        id: "",
        lawCase: {
          // 案件信息
          id: "",
          name: "",
        },
        preservationMoney: 0, // 保全金额
        propertyType: "", // 财产类型 字典：property_type
        remarks: "", // 备注
        remindMode: "", // 提醒方式
        respondent: "", // 被申请人
        rulingNumber: "", // 裁定书编号
        seizureDate: "", // 查封日期
        seizureExpirationDate: "", // 查封到期日
        undertakePerson: "", // 承办人员
      },
      rules: {
        applicant: [
          { required: true, message: "请输入申请人", trigger: "blur" },
        ],
        respondent: [
          { required: true, message: "请输入被申请人", trigger: "blur" },
        ],
        price: [
          { required: true, message: "请输入案件标的", trigger: "blur" },
          { type: "number", message: "金额必须为数字值" },
        ],
        propertyType: [
          { required: true, message: "请选择财产类型", trigger: "change" },
        ],
      },
    };
  },
  computed: mapState({
    caseInfo: (state) => state.caseInfo.caseInfo,
  }),
  mounted() {
    // 当 model 发生改变时，为表单设置值
    this.$watch(
      "model",
      (e) => {
        if (e) {
          this.ruleForm = JSON.parse(JSON.stringify(this.model));
        } else {
          this.ruleForm = {
            acceptUnit: "", // 受理单位
            applicant: "", // 申请人
            continueRemindDate: "", // 续封提醒
            executeStatus: "", // 执行状态 字典：property_execute_status
            id: "",
            preservationMoney: 0, // 保全金额
            propertyType: "", // 财产类型 字典：property_type
            remarks: "", // 备注
            remindMode: "", // 提醒方式
            respondent: "", // 被申请人
            rulingNumber: "", // 裁定书编号
            seizureDate: "", // 查封日期
            seizureExpirationDate: "", // 查封到期日
            undertakePerson: "", // 承办人员
          };
        }
      },
      {
        deep: true,
        immediate: true,
      }
    );
  },
  methods: {
    handleOk(formName) {
      this.$emit("ok");
      // this.$refs[formName].resetFields();
    },
    handleCancel() {
      // this.$refs.ruleForm.resetFields();
      this.$emit("cancel");
    },
  },
};
</script>

<style lang="less" scoped>
.flex-center {
  color: #409eff;
  cursor: pointer;
}
h3 {
  margin: 20px 0;
}
.ruleForm-box {
  // height: 70vh;
  overflow-y: scroll;
  padding: 20px;
}
</style>
<template>
  <el-dialog
    lock-scroll
    append-to-body
    title="单位信息"
    :visible="visible"
    width="500px"
    :close-on-press-escape="false"
    :close-on-click-modal="false"
    :before-close="
      () => {
        $emit('cancel');
      }
    "
  >
    <el-form
      :model="ruleForm"
      :rules="rules"
      ref="ruleForm"
      label-width="70px"
      class="ruleForm-box"
    >
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="地区" prop="acceptUnitArea">
            <el-cascader style="width: 100%"
              placeholder="请选择"
              v-model="ruleForm.acceptUnitArea"
              :options="options"
            ></el-cascader>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="单位类型" prop="acceptUnitType">
            <el-select
              v-model="ruleForm.acceptUnitType"
              placeholder="请选择单位类型"
            >
              <el-option
                :label="item.label"
                :value="item.value"
                v-for="item in unitTypeList"
                :key="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="单位名称" prop="acceptUnitName">
            <el-autocomplete
            style="width: 100%"
              class="inline-input"
              v-model="ruleForm.acceptUnitName"
              :fetch-suggestions="querySearch"
              placeholder="请先选择地区"
            ></el-autocomplete>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button
        @click="
          () => {
            $emit('cancel');
          }
        "
        >取 消</el-button
      >
      <el-button
        type="primary"
        @click="
          () => {
            $emit('ok');
          }
        "
        >立即提交</el-button
      >
    </span>
  </el-dialog>
</template>

<script>
import options from "@/utils/cities.js";
import storage from "store";

export default {
  name: "RenewDate",
  props: {
    visible: {
      type: Boolean,
      required: true,
    },
    loading: {
      type: Boolean,
      default: () => false,
    },
    model: {
      type: Object,
      default: () => null,
    },
  },
  data() {
    const dictList = storage.get("dictList");

    return {
      unitTypeList: dictList.accept_unit_type,
      options,
      isShowMore: false,
      ruleForm: {},
      rules: {
        startTime: [
          { required: true, message: "请选择开庭日期", trigger: "change" },
        ],
        lawer: [
          { required: true, message: "请选择出庭律师", trigger: "change" },
        ],
      },
    };
  },

    mounted() {
    // 当 model 发生改变时，为表单设置值
    this.$watch(
      "visible",
      (e) => {
        if (e) {
          this.ruleForm = JSON.parse(JSON.stringify(this.model)) 
          this.ruleForm.acceptUnitArea = this.ruleForm.acceptUnitArea && this.ruleForm.acceptUnitArea.split('/')
        } 
      },
      {
        deep: true,
        immediate: true,
      }
    );
  },
  methods: {
    querySearch() {},
    handleSelect() {},
  },
};
</script>

<style lang="less" scoped>
.flex-center {
  color: #409eff;
  cursor: pointer;
}
h3 {
  margin: 20px 0;
}
.ruleForm-box {
  padding: 20px;
}
.el-autocomplete{
  width: 100%;
}
</style>
<template>
  <el-dialog
    :title="type === 'edit' ? '编辑文件夹' : '新增文件夹'"
    :visible="visible"
    width="600px"
    :before-close="handleCancel"
    :close-on-press-escape="false"
    :close-on-click-modal="false"
  >
    <el-form
      :model="ruleForm"
      :rules="rules"
      ref="ruleForm"
      label-width="130px"
      class="ruleForm-box"
    >
      <el-row>
        <el-col :span="24">
          <el-form-item label="文件夹名称" prop="name">
            <el-input
              placeholder="请输入文件夹名称"
              v-model.number="ruleForm.name"
              type="text"
            >
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleCancel">取 消</el-button>
      <el-button type="primary" @click="handleOk('ruleForm')">提交</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { isEmpty, cloneDeep } from "lodash";
export default {
  name: "CreateStage",
  props: {
    visible: {
      type: Boolean,
      required: true,
    },
    loading: {
      type: Boolean,
      default: () => false,
    },
    model: {
      type: Object,
      default: () => {},
    },
    type: {
      type: String,
    },
  },
  data() {
    return {
      ruleForm: {
        name: "",
      },
      rules: {
        name: [
          { required: true, message: "请输入文件夹名称", trigger: "blur" },
        ],
      },
    };
  },
  mounted() {
    this.$watch(
      "visible",
      (e) => {
        if (e && this.type === "edit") {
          this.ruleForm = JSON.parse(JSON.stringify(this.model));
        } else if(e) {
          this.ruleForm = {
            name: ""
          };
        }
      },
      {
        deep: true,
        immediate: true,
      }
    );
  },
  methods: {
    handleOk() {
      this.$emit("ok", this.ruleForm);
      this.$refs.ruleForm.resetFields();
    },
    handleCancel() {
      if (this.type === "add") {
        this.$refs.ruleForm.resetFields();
      }
      this.$emit("cancel");
    },
  },
};
</script>

<style>
</style>
<template>
  <el-dialog
    title="从模板创建"
    :visible="visible"
    width="80%"
    :before-close="handleCancel"
    :close-on-press-escape="false"
    :close-on-click-modal="false"
  >
    <div class="file-template-content">
      <div class="template-left">
        <el-button
          type="primary"
          size="mini"
          icon="el-icon-plus"
          style="margin-bottom: 10px"
          v-if="user.type.indexOf('2') > -1 || user.type.indexOf('1') > -1"
          @click="handleShowCategoryModal"
          >新增分类</el-button
        >
        <div class="left-list">
          <div
            :class="[
              'list-item',
              active === index ? 'list-item-active' : '',
              user.type.indexOf('2') > -1 || user.type.indexOf('1') > -1 ? 'category' : '',
            ]"
            v-for="(item, index) in categoryList"
            :key="item.id"
            @click="tabChange(index)"
          >
            <span>{{ item.name }}</span>
            <span class="icon-wrap" v-if="user.type.indexOf('2') > -1 || user.type.indexOf('1') > -1">
              <i class="el-icon-edit mr10" @click.stop="handleShowCategoryModal(item)"></i>
              <i class="el-icon-delete" @click.stop="handleDelete(item)"></i>
            </span>
          </div>
        </div>
      </div>
      <div class="template-right">
        <div class="my-header">
          <div class="header-item header-active"></div>
          <el-upload
            class="upload-demo"
            :action="uploadUrl"
            :on-success="handleUpload"
            :data="{ uploadPath: '/wps/docTemplate/' }"
            :headers="header"
            :show-file-list="false"
            v-if="user.type.indexOf('2') > -1 || user.type.indexOf('1') > -1"
          >
            <el-button type="primary" size="mini" icon="el-icon-upload"
              >上传</el-button
            >
          </el-upload>
        </div>
        <div class="file-item-wrap" v-loading="loading" v-if="docList.length">
          <div
            class="file-item"
            v-for="(item, index) in docList"
            :key="item.id"
          >
            <div class="file-item-content">
              <div class="content-gray">
                <img
                  src="../../../../assets/word-img.png"
                  alt=""
                  class="word-img"
                />
              </div>
              <div class="file-mask">
                <div
                  class="mask-btn"
                  style="margin-top: 50px"
                  @click="handleUseFile(item)"
                >
                  使用
                </div>
                <div
                  class="mask-plain-btn"
                  style="margin-top: 10px"
                  @click="preview(item)"
                >
                  预览
                </div>
                <div
                  class="mask-btn"
                  style="margin-top: 10px; background: rgb(236, 80, 80)"
                  @click="handleDeleteFile(item.id)"
                  v-if="user.type.indexOf('2') > -1 || user.type.indexOf('1') > -1"
                >
                  删除
                </div>
              </div>
            </div>
            <div class="file-name">{{ item.name }}</div>
          </div>
        </div>
        <div v-else>
          <NoData />
        </div>
      </div>
    </div>
    <CreateDocCategory
      ref="CategoryForm"
      :visible="CategoryVisible"
      :model="CategoryModalMdl"
      @cancel="handleCategoryCancel"
      @ok="handleCategoryOk"
    />
  </el-dialog>
</template>

<script>
import { isEmpty, cloneDeep } from "lodash";
import {
  getCategory,
  getDocList,
  addDoc,
  useDoc,
  deleteDoc,
  addCategory,
  delCategory
} from "@/api/case/docTemp";
import { useTodoDoc } from "@/api/case/todo";
import { getUrl } from "@/api/case/doc";
import storage from "store";
import NoData from "@/components/NoData";
import CreateDocCategory from "./CreateDocCategory";

export default {
  name: "CreateStage",
  components: {
    NoData,
    CreateDocCategory,
  },
  props: {
    visible: {
      type: Boolean,
      required: true,
    },
    directoryId: {
      type: String,
    },
    todoId: {
      type: String,
    },
  },
  data() {
    const baseUrl = process.env.VUE_APP_API_BASE_URL;
    return {
      categoryList: [{ name: 123 }],
      active: 0,
      docList: [],
      header: {
        token: storage.get("token"),
      },
      uploadUrl: `${baseUrl ? baseUrl : ""}/law/sys/file/webupload/upload`,
      loading: false,
      user: storage.get("user"),
      CategoryVisible: false,
      CategoryModalMdl: null,
    };
  },
  mounted() {
    this.lawCaseId = this.$route.query.id;
    this.$watch(
      "visible",
      (e) => {
        if (e) {
          this.getCategoryList();
        }
      },
      {
        deep: true,
        immediate: true,
      }
    );
  },
  methods: {
    handleShowCategoryModal(item) {
      if(item){
        this.CategoryModalMdl = item
      }else{
        this.CategoryModalMdl = null
      }
      this.CategoryVisible = true;
    },
    // 编辑/新建分类
    handleCategoryOk() {
      const data = this.$refs.CategoryForm.ruleForm;
      const { name } = data;
      this.$refs.CategoryForm.$refs.ruleForm.validate((valid) => {
        if (valid) {
          let formData = new FormData();
          formData.append("name", name);
          if (data.id) {
            formData.append("id", data.id);
          }
          addCategory(formData).then((res) => {
            if (res.success) {
              this.CategoryVisible = false;
              this.$message({
                showClose: true,
                message: res.msg,
                type: "success",
              });
              this.getCategoryList();
            } else {
              this.$message.error(res.msg);
            }
          });
        } else {
          return false;
        }
      });
    },
    handleCategoryCancel() {
      this.CategoryVisible = false;
    },
    // 删除分类
    handleDelete(item) {
      this.$confirm("确定删除该分类?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          let formData = new FormData();
          formData.append("id", item.id);
          delCategory(formData).then((res) => {
            if (res.success) {
              this.active = 0
              this.getCategoryList();
              this.$message({
                type: "success",
                message: "删除成功!",
              });
            }
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    // 使用文档模板
    handleUseFile(item) {
      if (this.todoId) {
        let formData = new FormData();
        formData.append("templateId", item.id);
        formData.append("todoInfoId", this.todoId);
        useTodoDoc(formData).then((res) => {
          if (res.success) {
            this.$emit("useDoc");
            this.preview(item, "write");
          }
        });
      } else {
        let formData = new FormData();
        formData.append("lawCaseId", this.lawCaseId);
        formData.append("templateId", item.id);
        formData.append("fileDirectoryId", this.directoryId);
        useDoc(formData).then((res) => {
          if (res.success) {
            this.$emit("useDoc");
            this.preview(item, "write");
          }
        });
      }
    },
    // 上传文件
    handleUpload(response, file, fileList) {
      if (response.success) {
        let formData = new FormData();
        formData.append("name", response.name);
        formData.append("path", response.url);
        formData.append("category.id", this.categoryList[this.active].id);
        formData.append("category.name", this.categoryList[this.active].name);
        addDoc(formData).then((res) => {
          if (res.success) {
            this.$message.success("上传成功");
            this.getDocList(this.categoryList[this.active].id);
          } else {
            this.$message.error("上传失败");
          }
        });
      } else {
        this.$message.error("上传失败");
      }
    },
    // 删除文档
    handleDeleteFile(id) {
      let formData = new FormData();
      formData.append("ids", id);
      deleteDoc(formData).then((res) => {
        if (res.success) {
          this.$message.success("删除成功");
          this.getDocList(this.categoryList[this.active].id);
        } else {
          this.$message.error("删除失败");
        }
      });
    },
    // 获取分类列表
    getCategoryList() {
      getCategory().then((res) => {
        if (res.success) {
          this.categoryList = res.treeData;
          this.getDocList(this.categoryList[this.active].id);
        }
      });
    },
    getDocList(id) {
      this.loading = true;
      let formData = new FormData();
      formData.append("category.id", id);
      getDocList(formData).then((res) => {
        this.loading = false;
        this.docList = res.page.list;
      });
    },
    preview(item, type) {
      let params = {
        _w_fname: item.path,
        _w_fileid: item.id,
        operateType: type === "write" ? "write" : "read",
      };
      getUrl(params).then((res) => {
        if (res) {
          // 跳转 使用sessionStorage，避免关键信息在ip中暴露
          // 使用push会停留当前页面，故不采纳
          // params 传递参数，子组件无法渲染iframe组件，故不采纳
          sessionStorage.wpsUrl = res.wpsUrl;
          sessionStorage.token = res.token;
          const jump = this.$router.resolve({ name: "viewFile" });
          window.open(jump.href, "_blank");
        } else {
          this.$message.error("请求错误！");
        }
      });
    },
    tabChange(index) {
      this.active = index;
      this.getDocList(this.categoryList[this.active].id);
    },
    handleOk() {
      this.$emit("ok", this.ruleForm);
    },
    handleCancel() {
      this.$emit("cancel");
    },
  },
};
</script>

<style lang="less" scoped>
.file-template-content {
  position: relative;
  display: flex;
  .template-left {
    width: 150px;
    .left-list {
      padding: 0 10px 10px;
      background: #f7f7f7;
      border-radius: 4px;
      overflow: hidden;
      .icon-wrap {
        display: none;
      }
      .category:hover .icon-wrap {
        display: block;
      }
      .list-item {
        height: 32px;
        line-height: 32px;
        font-size: 15px;
        border-radius: 4px;
        margin-top: 10px;
        padding: 0 10px;
        color: #999;
        cursor: pointer;
        display: flex;
        justify-content: space-between;
      }
      .left-line {
        margin-top: 10px;
        height: 1px;
        background: #e1e4ee;
      }
      .list-item-active {
        background: #f8c272;
        color: #fff;
      }
    }
  }
  .template-right {
    flex: 1;
    padding-left: 30px;
    padding-right: 0;
    height: 570px;
    overflow-y: auto;
    .my-header {
      position: relative;
      display: flex;
      margin-bottom: 25px;
      justify-content: space-between;
      align-items: center;
    }
    .file-item-wrap {
      display: flex;
      flex-wrap: wrap;
      margin-top: 15px;
      .file-item {
        width: 140px;
        margin-bottom: 20px;
        margin-right: 20px;
        .file-item-content {
          position: relative;
          width: 140px;
          height: 160px;
          background: #fff;
          box-shadow: 0 1px 5px 0 #ccc;
          border-radius: 4px;
          cursor: pointer;
          padding-top: 10px;
          .content-gray {
            width: 110px;
            height: 150px;
            background: #f8f8f8;
            margin-left: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            .word-img {
              width: 50%;
              opacity: 0.5;
            }
          }

          .file-mask {
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background: rgba(51, 51, 51, 0.3);
            border-radius: 4px;
            display: none;
            .mask-btn {
              width: 80px;
              height: 26px;
              line-height: 26px;
              background: #f8c272;
              font-size: 14px;
              color: #fff;
              text-align: center;
              border-radius: 4px;
              margin: 0 auto;
            }
            .mask-plain-btn {
              box-sizing: border-box;
              width: 80px;
              height: 26px;
              line-height: 26px;
              font-size: 14px;
              color: #fff;
              text-align: center;
              border-radius: 4px;
              border: 1px solid #fff;
              margin: 0 auto;
            }
          }
        }
        .file-name {
          font-size: 12px;
          color: #666;
          text-align: center;
          padding-top: 10px;
        }
        &:hover .file-mask {
          display: block;
        }
      }
    }
  }
}
</style>
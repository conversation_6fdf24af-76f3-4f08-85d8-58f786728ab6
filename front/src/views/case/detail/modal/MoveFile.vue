<template>
  <el-dialog
    title="移动文件目录"
    :visible="visible"
    width="600px"
    :before-close="handleCancel"
    :close-on-press-escape="false"
    :close-on-click-modal="false"
  >
    <el-tree
      :data="treeData"
      node-key="id"
      ref="treeNodes"
      :expand-on-click-node="false"
      @node-click="handleNodeClick"
      default-expand-all
    >
      <div
        class="custom-tree-node flex justify-between"
        slot-scope="{ node, data }"
      >
        <div>
          <img src="../../../../assets/folder.png" alt="" class="folder-img" />
          <span>{{ data.name }}</span>
        </div>
      </div>
    </el-tree>
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleCancel">取 消</el-button>
      <el-button type="primary" @click="handleOk('ruleForm')">提交</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { isEmpty, cloneDeep } from "lodash";
import { getTreeData } from "@/api/case/doc";
export default {
  name: "MoveFile",
  props: {
    visible: {
      type: Boolean,
      required: true,
    },
    loading: {
      type: Boolean,
      default: () => false,
    },
  },
  data() {
    return {
      ruleForm: {
        name: "",
      },
      rules: {
        name: [{ required: true, message: "请输入阶段名称", trigger: "blur" }],
      },
      treeData: [],
      currentDirectoryId: ''
    };
  },
  mounted() {
    this.lawCaseId = this.$route.query.id;

    this.$watch(
      "visible",
      (e) => {
        if (e) {
          this.getDirectory();
        }
      },
      {
        deep: true,
        immediate: true,
      }
    );
  },
  methods: {
    // 获取目录
    getDirectory() {
      let formdata = new FormData();
      formdata.append("lawCaseId", this.lawCaseId);
      getTreeData(formdata).then((res) => {
        this.treeData = res.treeData;
        this.directoryList = res.treeData;
      });
    },
    handleOk() {
      this.$emit("ok", this.currentDirectoryId);
    },
    handleCancel() {
      this.$emit("cancel");
    },
    handleNodeClick(data) {
      this.currentDirectoryId = data.id;
    },
  },
};
</script>

<style lang="less" scoped>
/deep/ .custom-tree-node {
  width: 100%;
  margin-right: 5px;

  .folder-img {
    width: 16px;
    height: 16px;
    margin-right: 10px;
  }
}
</style>
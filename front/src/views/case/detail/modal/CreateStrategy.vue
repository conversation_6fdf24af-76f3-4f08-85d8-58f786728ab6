<template>
  <el-dialog
    :title="model ? '编辑办案策略' : '新增办案策略'"
    :visible="visible"
    width="600px"
    :before-close="handleCancel"
    :close-on-press-escape="false"
    :close-on-click-modal="false"
  >
    <el-form
      :model="ruleForm"
      :rules="rules"
      ref="ruleForm"
      label-width="130px"
      class="ruleForm-box"
    >
      <el-row>
        <el-col :span="24">
          <el-form-item label="问题描述" prop="question">
            <el-input
              placeholder="请输入问题描述"
              v-model="ruleForm.question"
              type="text"
            >
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="解决方案" prop="solution">
            <el-input
              placeholder="请输入解决方案"
              v-model="ruleForm.solution"
              type="textarea"
            >
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="处理结果" prop="result">
            <el-input
              placeholder="请输入处理结果"
              v-model="ruleForm.result"
              type="text"
            >
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleCancel">取 消</el-button>
      <el-button type="primary" @click="handleOk('ruleForm')">提交</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { isEmpty, cloneDeep } from "lodash";
export default {
  name: "CreateStage",
  props: {
    visible: {
      type: Boolean,
      required: true,
    },
    loading: {
      type: Boolean,
      default: () => false,
    },
    model: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      ruleForm: {
        solution: "",
        question: "",
        result: "",
      },
      rules: {
        question: [{ required: true, message: "请输入问题描述", trigger: "blur" }],
        solution: [{ required: true, message: "请输入解决方案", trigger: "blur" }],
        result: [{ required: true, message: "请输入处理结果", trigger: "blur" }],
      },
    };
  },
  mounted() {
    // 当 model 发生改变时，为表单设置值
    this.$watch(
      "model",
      (e) => {
        if (e) {
          this.ruleForm = JSON.parse(JSON.stringify(this.model));
        } else {
          this.ruleForm = {
            solution: "",
            question: "",
            result: "",
          };
        }
      },
      {
        deep: true,
        immediate: true,
      }
    );
  },
  methods: {
    handleOk(formName) {
      this.$emit("ok", this.ruleForm);
    },
    handleCancel() {
      this.$emit("cancel");
    },
  },
};
</script>

<style>
</style>
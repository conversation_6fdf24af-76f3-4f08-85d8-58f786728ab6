<template>
  <div class="box">
    <!-- 审核 -->
    <div v-if="caseInfo.isAudit === '1'" class="mg20">
      <div class="flex justify-between align-center">
        <div class="mg20 flex">
          <div style="width: 80px">审核状态:</div>
          <span
            v-if="caseInfo.auditStatus === '0' && caseInfo.status !== '0'"
            style="color: #ec5050"
            >拒绝</span
          >
          <span v-if="caseInfo.auditStatus === '1'" style="color: #ffa836"
            >待审核</span
          >
          <span v-if="caseInfo.auditStatus === '2'" style="color: green"
            >审核通过</span
          >
          <span v-if="caseInfo.auditStatus === '3'" style="color: #bcbcbc"
            >待提交</span
          >
          <span
            v-if="caseInfo.auditStatus === '0' && caseInfo.status === '0'"
            style="color: #bcbcbc"
            >案件终止</span
          >
        </div>
        <div style="white-space: pre-line">{{ caseInfo.auditReason }}</div>
        <div
          class="primary cursor"
          v-if="
            (caseInfo.auditStatus === '0' || caseInfo.auditStatus === '3') &&
            user.type.indexOf('3') > -1 &&
            caseInfo.status !== '0'
          "
          @click="handleSubmit"
        >
          提交审核
        </div>
      </div>
      <div class="flex align-center">
        <div
          v-if="user.type.indexOf('2') > -1 && caseInfo.auditStatus === '1'"
          style="flex: 1"
          class="mr20 flex align-center"
        >
          <div style="width: 80px">审核意见</div>
          <el-input
            type="textarea"
            v-model="comments"
            placeholder="请输入审核意见"
          ></el-input>
        </div>

        <!-- <div
          class="primary cursor"
          v-if="
            (caseInfo.auditStatus === '0' || caseInfo.auditStatus === '3') &&
            user.type.indexOf('3') > -1
          "
          @click="handleSubmit"
        >
          提交审核
        </div> -->
        <div
          v-if="user.type.indexOf('2') > -1 && caseInfo.auditStatus === '1'"
          class="primary cursor"
        >
          <span class="mr20" @click="handleTrial('1')">通过</span>
          <span class="mr20" @click="handleTrial('2')">拒绝</span>
          <span @click="handleStopCase">终止案件</span>
        </div>
      </div>
    </div>
    <div class="detail-info">
      <!-- 左侧 -->
      <div class="record-left new-record-left">
        <div class="stage-header">
          <div class="stage-txt">办案流程</div>
          <el-dropdown trigger="click" @command="handleCommandStage($event)">
            <i class="el-icon-menu primary"></i>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item command="add">添加阶段</el-dropdown-item>
              <el-dropdown-item command="manage">管理模板</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </div>
        <div class="record-content">
          <div v-if="stageList.length">
            <draggable v-model="stageList" @change="dragStage">
              <div
                :class="[
                  'stage-item',
                  index === active ? 'stage-item-active' : '',
                ]"
                v-for="(item, index) in stageList"
                :key="index"
                @click="handleClickItem(index, item)"
              >
                <el-tooltip
                  class="item"
                  effect="dark"
                  content="设为当前阶段"
                  placement="top"
                >
                  <div @click="setCurrentStage(item)" class="star-icon">
                    <i
                      class="el-icon-star-off"
                      style="font-size: 18px"
                      v-if="item.isCurrent === '0'"
                    ></i>
                    <i
                      class="el-icon-star-on primary"
                      style="font-size: 20px"
                      v-else
                    ></i>
                    <div class="top-line"></div>
                    <div class="bottom-line"></div>
                  </div>
                </el-tooltip>
                <div class="item-right">
                  <div class="right-name flex">
                    <span
                      :class="[index === active ? 'primary' : '', 'stage-name']"
                      >{{ item.name }}</span
                    >
                    <span class="edit-icon" v-if="caseInfo.auditStatus !== '1' && caseInfo.auditStatus !== '3' && caseInfo.auditStatus !== '0'  ||(index == 0 && caseInfo.auditStatus !== '0')">
                      <el-dropdown
                        trigger="hover"
                        @command="handleCommandStage($event, item, index)"
                      >
                        <i
                          class="el-icon-edit-outline"
                          style="margin-left: 10px"
                        ></i>
                        <el-dropdown-menu slot="dropdown">
                          <el-dropdown-item command="edit"
                            >编辑</el-dropdown-item
                          >
                          <el-dropdown-item command="delete"
                            >删除</el-dropdown-item
                          >
                          <el-dropdown-item command="set"
                            >设为当前阶段</el-dropdown-item
                          >
                        </el-dropdown-menu>
                      </el-dropdown>
                    </span>
                  </div>
                  <div class="right-num">
                    <i class="el-icon-s-operation"></i>
                    <span
                      >{{ item.todoCompleteAmount }} /
                      {{ item.todoTotalAmount }}</span
                    >
                  </div>
                </div>
              </div>
            </draggable>
          </div>
          <div v-else>
            <NoData />
            <div style="text-align: center" class="mg10">
              从<span class="primary cursor" @click="handleShowManageModal"
                >模板库</span
              >中选择
            </div>
          </div>
        </div>
      </div>

      <!-- 中间 -->
      <div class="record-center">
        <div class="stage-header">
          <div class="stage-txt flex align-center">
            <span class="all-txt cursor" @click="showAll">全部</span>
            <div v-if="active !== null" class="flex align-center">
              <i class="el-icon-arrow-right"></i>
              <span style="color: #333; font-weight: 700">{{
                stageList[active].name
              }}</span>
            </div>
            <!-- <i class="el-icon-time" style="margin-left: 20px"></i>
          <span style="margin-left: 10px">2.1h</span> -->
          </div>
          <div class="flex align-center cursor">
            <!-- <div v-if="caseInfo.isAudit === '1'" class="flex mr20">
              <div
                v-if="
                  user.type.indexOf('2') > -1 && caseInfo.auditStatus === '1'
                "
              >
                <el-input
                  type="text"
                  v-model="comments"
                  placeholder="请输入审核意见"
                ></el-input>
              </div>
              <div class="mr20">
                审核状态:
                <span v-if="caseInfo.auditStatus === '0'">拒绝</span>
                <span v-if="caseInfo.auditStatus === '1'">待审核</span>
                <span v-if="caseInfo.auditStatus === '2'">审核通过</span>
                <span v-if="caseInfo.auditStatus === '3'">待提交</span>
              </div>
              <div
                class="primary"
                v-if="
                  (caseInfo.auditStatus === '0' ||
                    caseInfo.auditStatus === '3') &&
                  user.type.indexOf('3') > -1
                "
                @click="handleSubmit"
              >
                提交审核
              </div>
              <div
                v-if="
                  user.type.indexOf('2') > -1 && caseInfo.auditStatus === '1'
                "
                class="primary"
              >
                <span class="mr20" @click="handleTrial('1')">通过</span>
                <span class="mr20" @click="handleTrial('2')">拒绝</span>
                <span @click="handleStopCase">终止案件</span>
              </div>
            </div> -->
            <!-- <span class="edit-icon">
            <el-dropdown trigger="hover" @command="handleCommand">
              <i
                class="el-icon-upload2 primary"
                style="margin-right: 10px; font-size: 16px"
                >导出</i
              >
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item command="word">导出word</el-dropdown-item>
                <el-dropdown-item command="excel">导出excel</el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </span> -->
            <div
              class="primary"
              @click="handleShowTodoModal('add')"
              
            >
              <i
                class="el-icon-circle-plus-outline"
                style="margin-right: 10px"
              ></i>
              <span>添加</span>
            </div>
          </div>
        </div>
        <div class="record-content">
          <!-- <ul class="step-table" v-if="recordList.length">
          <li
            :class="['item', recordIndex === index ? 'stage-item-active' : '']"
            v-for="(item, index) in recordList"
            :key="index"
            @click="handleClickRecord(index)"
          >
            <div class="item-heading">
              <el-checkbox
                v-model="item.checked"
                class="ih-title"
                @change="handleTodoOk(item, 'toggle')"
                >{{ item.name }}</el-checkbox
              >
              <div class="ih-control">
                <span class="mr20" v-if="item.hostUser">
                  <i class="el-icon-user mr20"></i
                  >{{ item.hostUser.name }}</span
                >
                <span class="mr20">
                  <i class="el-icon-time mr20"></i
                  >{{ item.consumeTime }}分钟</span
                >
                <span>{{ item.updateDate }}</span>
              </div>
            </div>
            <div class="content">{{ item.content }}</div>
          </li>
        </ul>
        <div v-else>
          <NoData
            text="暂无案件办理记录，Ps：添加的记录，将同步展示在日程管理页面中。"
          />
        </div> -->

          <el-tree
            :data="recordList"
            node-key="id"
            :expand-on-click-node="false"
            :default-checked-keys="finishedTodoList"
            :default-expanded-keys="defaultExpandedKeys"
            @node-click="handleNodeClick"
            @check-change="handleCheckChange"
            @node-expand="nodeExpanded"
            icon-class="el-icon-plus"
          >
            <div
              :class="[
                'custom-tree-node',
                data.checked ? 'pass-bg' : 'nopass-bg',
              ]"
              slot-scope="{ node, data }"
            >
              <div class="flex justify-between align-center mg20">
                <div
                  class=""
                  style="
                    width: calc(70%);
                    white-space: normal;
                    word-break: break-all;
                    word-wrap: break-word;
                  "
                >
                  <span>{{ data.name }}</span>
				    <span v-if="data.isHaveFile==1" style="font-size: 10px;color:blue;">(有文档)</span>
				  <span v-if="data.isHaveDoc==1" style="font-size: 10px;color:red;padding-left: 10px;">(有附件)</span>
                </div>
                <div class="ih-control">
                  <span class="mr10" v-if="data.hostUser">
                    <i class="el-icon-user m120"></i
                    >{{ data.hostUser.name }}</span
                  >
                  <!-- <span class="mr20">
                    <i class="el-icon-time m120"></i
                    >{{ data.consumeTime || 0 }}分钟</span
                  > -->
                  <span>{{ data.updateDate }}</span>
                </div>
                <div
                  class="node-btn"
                  v-if="caseInfo.auditStatus === '2' || ((caseInfo.auditStatus==='1'||caseInfo.auditStatus==='3'))"
                >
                  <el-tooltip
                    class="item"
                    effect="dark"
                    content="新增待办"
                    placement="top"
                  >
                    <i
                      class="el-icon-plus"
                      @click="handleShowTodoModal('child', data)"
                    ></i>
                  </el-tooltip>
                  <el-tooltip
                    class="item"
                    effect="dark"
                    content="编辑"
                    placement="top"
                  >
                    <i
                      class="el-icon-edit"
                      @click="handleShowTodoModal('edit', data)"
                    ></i>
                  </el-tooltip>
                  <el-tooltip
                    class="item"
                    effect="dark"
                    content="删除"
                    placement="top"
                  >
                    <i
                      class="el-icon-delete"
                      @click="handleDeleteRecord(data.id)"
                    ></i>
                  </el-tooltip>
                </div>
              </div>
              <div class="item-p">{{ data.content }}</div>
            </div>
          </el-tree>
        </div>
      </div>

      <!-- 右侧 -->
      <div class="record-right">
        <div class="stage-header">
          <div class="stage-txt">事项详情</div>

          <div class="flex align-center cursor" v-if="recordList.length">
            <div
              class="primary mr20"
              @click="handleShowTodoModal('edit', info)"
              v-if="enableEdit"
            >
              <i class="el-icon-edit-outline" style="margin-right: 10px"></i>
              <span>编辑</span>
            </div>
            <div
              class="primary"
              @click="handleDeleteRecord(info.id)"
              v-if="enableEdit"
            >
              <i class="el-icon-delete" style="margin-right: 10px"></i>
              <span>删除</span>
            </div>
          </div>
        </div>
        <div class="record-content right-content">
          <div style="padding: 20px" v-if="recordList.length">
            <div class="right-mark">
              <el-checkbox
                v-model="info.checked"
                class="ih-title"
                @change="toggleTodoStatus"
                :disabled="!enableEdit"
                >{{ info.name }}</el-checkbox
              >
              <div class="mark-txt">
                {{ info.content }}
              </div>
            </div>
            <div class="right-info">
              <div class="info-item">
                <i class="el-icon-user"></i>
                <span class="txt">主办人员</span>
                <span class="value" v-if="info.hostUser">{{
                  info.hostUser.name
                }}</span>
              </div>
              <div class="info-item">
                <i class="el-icon-user"></i>
                <span class="txt">共办人员</span>
                <span class="value" v-for="(i,k) in shareList" :key="k">
                  {{i.user.name}}
                </span>
              </div>
              <!-- <div class="info-item">
                <i class="el-icon-time"></i>
                <span class="txt">开始时间</span>
                <span class="value">{{ info.startDate }}</span>
              </div>
              <div class="info-item">
                <i class="el-icon-time"></i>
                <span class="txt">结束时间</span>
                <span class="value">{{ info.endDate }}</span>
              </div>
              <div class="info-item">
                <i class="el-icon-time"></i>
                <span class="txt">花费时间</span>
                <span class="value">{{ info.consumeTime }}分钟</span>
              </div>
              <div class="info-item">
                <i class="el-icon-s-finance"></i>
                <span class="txt">提醒</span>
                <span class="value">{{ info.remindDate }}</span>
              </div>
              <div class="info-item">
                <i class="el-icon-time"></i>
                <span class="txt">完成时间</span>
                <span class="value">{{ info.completeDate }}</span>
              </div>
              <div class="info-item">
                <i class="el-icon-tickets"></i>
                <span class="txt">办理状态</span>
                <span class="value">{{
                  info.status === "1" ? "待办" : "已办"
                }}</span>
              </div> -->
              <div
                v-if="info.completeDate && info.hostUser"
                style="margin-top: 10px"
              >
                {{ info.hostUser.name }}{{ info.completeDate }}提交完成
              </div>
            </div> 
			<el-button style="float: right;margin-top: 10px;"
			  type="primary" size="mini"
			  icon="el-icon-plus"    v-if="user.id!='15fd4f2685e94cb3bbdd5d4642dcc00f'"
			  @click="handleShowDocTemp"
			  >从模板创建</el-button
			>
            <div  v-if="user.id!='15fd4f2685e94cb3bbdd5d4642dcc00f'"
              class="right-doc flex justify-between"
              style="align-items: flex-start;margin-top: 15px;"
             
            >
			
              <el-upload
                class="upload-demo"
                :action="uploadUrl"
                :on-success="handleUpload" 
                :data="{ 'todoInfo.id': info.id }"
                :headers="header"
                 :file-list="[]"
              >
			  <div slot="trigger" v-if="user.id!='15fd4f2685e94cb3bbdd5d4642dcc00f'" size="small" type="primary">  <i class="el-icon-paperclip mr10"></i>点击上传word或excel</div> 
				
				<div v-for="(url, i) in info.fileList" style="font-size:14px;margin-top: 20px;">
					 <div style="display: flex;justify-content:space-between;cursor:pointer;">
					  <a @click="preview(url)" style="color: #606266;overflow: hidden;width:400px;-webkit-line-clamp: 1;display: -webkit-box;-webkit-box-orient: vertical;overflow: hidden;text-overflow: ellipsis;">{{url.name}}</a>
					  <div class="node-btn" style="display: flex;"  >
					    <el-tooltip
					        class="item" style="color:#409eff;"
					        effect="dark"
					        content="下载"
					        placement="top">
					      <i class="el-icon-bottom" @click.stop="handleWpsDownlowd(url)"></i>
					    </el-tooltip>
						<el-tooltip style="padding-left: 10px;color:#409eff;"
						  class="item"  
						  effect="dark"
						  content="删除"
						  placement="top"
						>
						  <i
						    class="el-icon-delete"
						    @click.stop="handleWpsRemove(url)"
						  ></i>
						</el-tooltip>
						</div>
					</div>
						
				</div>
              </el-upload>
			
            
            </div>
			<div v-if="user.id=='15fd4f2685e94cb3bbdd5d4642dcc00f'"
			  style="align-items: flex-start;margin-top: 15px;"
			   
			>
			 
			   <div slot="trigger"  size="small" type="primary" @click="noUserAct" >   
				<i class="el-icon-paperclip mr10"></i>点击上传word或excel</div>
			   				
			   <div>
				   <div v-for="(url, i) in info.fileList" style="font-size:14px;margin-top: 20px;">
				     					 <div style="display: flex;justify-content:space-between;cursor:pointer;">
				     					  <a @click="preview(url)" style="color: #606266;overflow: hidden;width:400px;-webkit-line-clamp: 1;display: -webkit-box;-webkit-box-orient: vertical;overflow: hidden;text-overflow: ellipsis;">{{url.name}}</a>
				     					  
				     					</div>
				     						
				     				</div> 
			   </div>
			
			</div>
			<div v-if="user.id!='15fd4f2685e94cb3bbdd5d4642dcc00f'">
				<el-upload
				  class="upload-demo"
				  :action="uploadUrl"
				  :on-success="handleUpload"
				  :on-remove="handleRemove"
				  :data="{ 'todoInfo.id': info.id }"
				  :headers="header"
				  :file-list="info.fileList2"
				  
				  :on-preview="preview"
				>
				  <div class="mg10 form-label cursor">
				    <i class="el-icon-paperclip mr10"></i>
				    <span>点击上传pdf/图片</span>
				  </div> 
				</el-upload>
				
			</div>
			<div  v-if="user.id=='15fd4f2685e94cb3bbdd5d4642dcc00f'"
			  style="align-items: flex-start;margin-top: 15px;"
			   
			>
			 
			   <div slot="trigger"  size="small" type="primary" @click="noUserAct" >   
				<i class="el-icon-paperclip mr10"></i>点击上传pdf/图片</div>
			   				
			   <div>
				   <div v-for="(url, i) in info.fileList2" style="font-size:14px;margin-top: 20px;">
				     					 <div style="display: flex;justify-content:space-between;cursor:pointer;">
				     					  <a @click="preview(url)" style="color: #606266;overflow: hidden;width:400px;-webkit-line-clamp: 1;display: -webkit-box;-webkit-box-orient: vertical;overflow: hidden;text-overflow: ellipsis;">{{url.name}}</a>
				     					  
				     					</div>
				     						
				     				</div> 
			   </div>
			
			</div>
			
			
          </div>
          <div v-else>
            <NoData />
          </div>
        </div>
      </div>
      <el-dialog title="查看图片" :visible.sync="imgDialogVisible" width="60%">
        <img :src="imgUrl" alt="" style="width: 100%" />
      </el-dialog>
	  
	  <el-dialog title="查看PDF" :visible.sync="pdfDialogVisible" width="90%" append-to-body center>  
            <a :href="pdfDownUrl" target="_blank" class="buttonText" style="padding-left: 47%;">点击下载</a>
		     <div class="pdf_wrap">
		           <pdf class="pdfView"  :key="i" :page="i" :src="src" style="width: 100%" v-for="i in numPages"></pdf>
		           
	         </div>
	  </el-dialog>

      <CreateTodo
        ref="todoForm"
        :visible="todoVisible"
        :model="todoModalMdl"
        @cancel="handleTodoCancel"
        @ok="handleTodoOk"
        @changeFile="handleChangeFile"
        :stageList="stageList"
        :relevanceType="'2'"
        :selectStage="selectStage"
        :isCalendar="false"
      />
      <CreateStage
        ref="stageForm"
        :visible="stageVisible"
        :model="stageModalMdl"
        @cancel="handleStageCancel"
        @ok="handleStageOk"
      />
      <ManageTemplate
        ref="manageTemplateForm"
        :visible="manageVisible"
        :model="manageModalMdl"
        @cancel="handleManageCancel"
        @ok="handleManageOk"
      />
      <DocTemplate
        ref="docTempForm"
        :visible="docTempVisible"
        :todoId="todoId"
        @cancel="handleDocTempCancel"
        @useDoc="
          docTempVisible = false;
          getInfo(todoId);
        "
      />
    </div>
  </div>
</template>

<script>
import draggable from "vuedraggable";
import { mapMutations, mapState } from "vuex";
import storage from "store";
import pdf from 'vue-pdf'
import CreateStage from "./modal/CreateStage.vue";
import CreateTodo from "./modal/CreateTodo.vue";
import ManageTemplate from "./modal/ManageTemplate.vue";
import DocTemplate from "./modal/DocTemplate.vue";
import NoData from "@/components/NoData";
import { recursive } from "@/utils/util";
import { audit, detailInfo, submitAudit, caseStop } from "@/api/case/manage";
import { imgSuffix } from "../../../utils/common-data";
import { getUrl } from "@/api/case/doc";
import { getUserList } from "@/api/case/caseUser"; 
import {
  getStageList,
  save,
  delStage,
  setCurrent,
  saveStage,
  changeSort,
} from "@/api/case/stage";
import {
  todoList,
  saveTodo,
  delTodo,
  deleteFile,
  queryTodoById,
  customerList,
  fileList,
  changeStatus,
  statisticList,
  fileUpload,
} from "@/api/case/todo";
export default {
  name: "CaseRecord",
  components: {
    CreateTodo,
    NoData,
    CreateStage,
    ManageTemplate,
    draggable,
	pdf,
    DocTemplate,
  },
  props:{
    concernPersonList:[]
  },
  data() {
    const baseUrl = process.env.VUE_APP_API_BASE_URL;
    return {
      shareList:[],
	  isAdd:true,
      todoModalMdl: null,
      todoVisible: false,
      stageModalMdl: null,
      stageVisible: false,
      manageModalMdl: null,
      manageVisible: false,
      docTempVisible: false,
      recordList: [],
      active: null,
      recordIndex: 0,
      info: {},
      comments: "",
      stageList: [],
      finishedTodoList: [],
      defaultExpandedKeys: [],
      selectStage: null,
      user: storage.get("user"),
      header: {
        token: storage.get("token"),
      },
      uploadUrl: `${baseUrl ? baseUrl : ""}/law/lawcase/todoInfo/fileUpload`, 
      todoId: "",
      imgDialogVisible: false,
      imgUrl: "",
	  pdfDialogVisible: false, 
	   numPages: undefined,
	  currentPage: 0,
	  pageCount: 0,
	  pdfUrl: '',
	  pdfDownUrl:'',
	  src: '', 

    };
  },
  computed: mapState({
    caseInfo: (state) => state.caseInfo.caseInfo,
    enableEdit(){
      return this.caseInfo.auditStatus === '2' || ((this.caseInfo.auditStatus==='1'|| this.caseInfo.auditStatus==='3')&& this.info.isNotAuditProhibit !== '1')
    }
  }),
  mounted() {
    this.id = this.$route.query.id;
    this.getStageData();
    this.getUserList()
  },
  methods: {
    ...mapMutations(["SET_CASE_INFO"]),
    // 共享成员列表
    getUserList() {
      let formData = new FormData();
      formData.append("lawCase.id", this.id);
      getUserList(formData).then((res) => {
        this.shareList = res.page.list;
      });
    },
    //拖动阶段排序
    dragStage(e) {
      let newSort = e.moved.newIndex + 1;
      let id = e.moved.element.id;
      let formData = new FormData();
      formData.append("id", id);
      formData.append("newSort", newSort);
      changeSort(formData).then((res) => {
        this.getStageData();
      });
    }, 
      loadPdf() {
     this.src = pdf.createLoadingTask({
                'url':this.pdfUrl,
                cMapUrl: 'https://unpkg.com/pdfjs-dist@2.0.943/cmaps/',
                cMapPacked: true,
              })
		
        this.src.promise.then(pdf => {
          this.numPages = pdf.numPages // 这里拿到当前pdf总页数
        })
      },
    handleWpsRemove(file) {
		if(this.user.id=='15fd4f2685e94cb3bbdd5d4642dcc00f'){
			 return this.$message.error("体验者无权限操作");
		}
		//弹出提示框，询问用户是否删除
		this.$confirm('此操作将永久删除该文件, 是否继续?', '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning'
		}).then(() =>{
		//用户确认删除，执行删除代码
		    let formData = new FormData();
		    formData.append("ids", file.id);
		    deleteFile(formData).then((res) => {
		      if (res.success) {
		        this.$message.success("删除成功");
		        this.getInfo(this.infoId);
				this.getTodoList(
							this.active != null
							  ? this.stageList[this.active].id
							  : this.stageList[0].id
						  );
		      } else {
		        this.$message.error("删除失败");
		      }
		    });
		}).catch(() =>{
		//用户取消删除，不执行删除代码
		})
	 
		
   
    } ,handleWpsDownlowd(file){
		   window.open(file.fullPath);
	},
	
    preview(item) {
		if(this.user.id=='15fd4f2685e94cb3bbdd5d4642dcc00f'){
			 return this.$message.error("体验者不能操作");
		} 
      if (item.name.IsPicture()) {
        this.imgDialogVisible = true;
        this.imgUrl = item.fullPath;
        return;
      }
	  if (item.fileType=='pdf') {
	  window.open("/law/pdf/look?filePath="+encodeURIComponent(item.fullFilePath));
	   // this.pdfDialogVisible = true;
	  //  this.pdfUrl = item.fullPath;
	  		//this.pdfDownUrl=item.fullPath;
	  		//this.pdfFileDownUrl=item.fullFilePath; 
	  		//this.loadPdf()
	    return;
	  }
      let params = {
        _w_fname: item.path,
        _w_fileid: item.id,
        operateType: "write",
      };
      getUrl(params).then((res) => {
        if (res) {
          // 跳转 使用sessionStorage，避免关键信息在ip中暴露
          // 使用push会停留当前页面，故不采纳
          // params 传递参数，子组件无法渲染iframe组件，故不采纳
          sessionStorage.wpsUrl = res.wpsUrl;
          sessionStorage.token = res.token;
          const jump = this.$router.resolve({ name: "viewFile" });
          window.open(jump.href, "_blank");
        } else {
          this.$message.error("请求错误！");
        }
      });
    },
    // 上传文件
    handleUpload(response, file, fileList) {
		if(this.user.id=='15fd4f2685e94cb3bbdd5d4642dcc00f'){
			 return this.$message.error("体验者不能操作");
		}
      if (response.success) {
        this.$message.success("上传成功");
        this.getInfo(this.infoId);
		this.getTodoList(
					this.active != null
					  ? this.stageList[this.active].id
					  : this.stageList[0].id
				  );
      } else {
        this.$message.error("上传失败");
      }
    },
    // 上传文件
    handleUpload2(response, file, fileList) {
		if(this.user.id=='15fd4f2685e94cb3bbdd5d4642dcc00f'){
			 return this.$message.error("体验者不能操作");
		}
      if (response.success) {
        this.$message.success("上传成功");
        this.getInfo(this.infoId);
			this.getTodoList(
						this.active != null
						  ? this.stageList[this.active].id
						  : this.stageList[0].id
					  );
      } else {
        this.$message.error("上传失败");
      }
    },
    handleRemove(file, fileList) {
		if(this.user.id=='15fd4f2685e94cb3bbdd5d4642dcc00f'){
			 return this.$message.error("体验者不能操作");
		}
      let formData = new FormData();
      formData.append("ids", file.id);
      deleteFile(formData).then((res) => {
        if (res.success) {
          this.$message.success("删除成功");
          this.getInfo(this.infoId);
		  this.getTodoList(
		  			this.active != null
		  			  ? this.stageList[this.active].id
		  			  : this.stageList[0].id
		  		  );
        } else {
          this.$message.error("删除失败");
        }
      });
    },
    handleChangeFile() {
      this.getInfo(this.infoId);
    },
    // 审核
    handleTrial(type) {
		if(this.user.id=='15fd4f2685e94cb3bbdd5d4642dcc00f'){
			 return this.$message.error("体验者不能操作");
		}
      if (!this.comments) {
        return this.$message.error("请输入审批意见");
      }
      let formData = new FormData();
      formData.append("result", type);
      formData.append("id", this.id);
      formData.append("reason", this.comments);
      audit(formData).then((res) => {
        if (res.success) {
          this.getCaseInfo();
        }
      });
    },
    // 终止案件
    handleStopCase() {
		if(this.user.id=='15fd4f2685e94cb3bbdd5d4642dcc00f'){
			 return this.$message.error("体验者不能操作");
		}
      if (!this.comments) {
        return this.$message.error("请输入审批意见");
      }
      let formData = new FormData();
      formData.append("id", this.id);
      formData.append("reason", this.comments);
      caseStop(formData).then((res) => {
        if (res.success) {
          this.getCaseInfo();
        }
      });
    },
    // 提交审核
    handleSubmit() {
		if(this.user.id=='15fd4f2685e94cb3bbdd5d4642dcc00f'){
			 return this.$message.error("体验者不能操作");
		}
      let formData = new FormData();
      formData.append("id", this.id);
      submitAudit(formData).then((res) => {
        if (res.success) {
          this.getCaseInfo();
        }
      });
    },
    getCaseInfo() {
      let formData = new FormData();
      formData.append("id", this.id);
      detailInfo(formData).then((res) => {
        this.lawCase = res.data.lawCase;
        this.SET_CASE_INFO(this.lawCase);
      });
    },
    handleNodeClick(data, node) {
      console.log(data,node)
      this.todoId = data.id;
      this.getInfo(data.id);
    },
    nodeExpanded(data, node){
      console.log(data, node)
      this.defaultExpandedKeys = [data.id]
    },
    handleCheckChange(data, checked, indeterminate) {},
    // 获取待办列表
    getTodoList(id = "", type) {
      let formData = new FormData();
      formData.append("relevanceId", this.id);
      formData.append("stage.id", id);
	    formData.append("checkWord", 'checkDoc');
      todoList(formData).then((res) => {
        let list = res.list;
        this.recordList = list;
        this.finishedTodoList = recursive(this.recordList);
        list.length &&
          this.getInfo(type === "change" ? this.todoId : list[0].id);
      });
    },
    // 获取阶段列表
    getStageData() {
      let formData = new FormData();
      formData.append("lawCase.id", this.id);
      getStageList(formData).then((res) => {
        this.stageList = res.page.list;
        this.stageList.length &&
          this.getTodoList(
            this.active ? this.stageList[this.active].id : this.stageList[0].id
          );
      });
    },
    // 显示文档模板弹框
    handleShowDocTemp() {
      this.todoId = this.info.id;
      this.docTempVisible = true;
	  if(this.user.id=='15fd4f2685e94cb3bbdd5d4642dcc00f'){
	  	 return this.$message.error("体验者不能操作");
	  }
    },
	noUserAct(){
		 return this.$message.error("体验账号暂无权限");
	},
    // 关闭文档模板弹框
    handleDocTempCancel() {
      this.docTempVisible = false;
    },
    // 显示新增待办弹框
    handleShowTodoModal(type, data) {
		if(this.user.id=='15fd4f2685e94cb3bbdd5d4642dcc00f'){
			 return this.$message.error("体验者不能操作");
		}
      this.todoType = type;
	  this.isAdd=false;
      if (type === "edit") {
        this.todoModalMdl = data;
        this.selectStage = null;
      } else if (type === "add") {
        this.todoModalMdl = null;
        this.selectStage = null;
		this.isAdd=true;
      } else { 
	    this.isAdd=true;
        this.todoModalMdl = null;
        this.parentId = data.id;
        this.selectStage = data.stage;
      } 
      this.todoVisible = true;
    },
    // 确认新增待办
    handleTodoOk() {
		if(this.user.id=='15fd4f2685e94cb3bbdd5d4642dcc00f'){
			 return this.$message.error("体验者不能操作");
		}
      let data = this.$refs.todoForm.ruleForm;
      const {
        name,
        consumeTime,
        sort,
        content,
        startDate,
        endDate,
        stage,
        checked,
        fileList,
		 fileList2,
        remindDate,
        hostUser,
      } = data;
      const param = {
        id: this.todoType === "edit" ? data.id : "",
        parent:
          this.todoType === "child"
            ? this.parentId
            : this.todoType === "edit" && data.parentId != "0"
            ? data.parentId
            : "",
        name,
        consumeTime,
        sort,
        content,
        startDate,
        endDate,
        stage,
        status: checked ? "2" : "1",
        relevanceType: 2,
        relevanceId: this.id,
        fileList,
		fileList2,
        remindDate,
        hostUser,
      };
      if (!name) {
        return this.$message.error("请输入工作摘要");
      }
      if (!startDate) {
        return this.$message.error("请选择开始时间");
      }
      if (!stage.id) {
        return this.$message.error("请选择案件阶段");
      }
      saveTodo(param).then((res) => {
        if (res.success) {
          this.$message({
            showClose: true,
            message: res.msg,
            type: "success",
          });
		  this.manageModalMdl=null;
		  this.todoVisible=false;
          // this.getStageData();
		  this.isAdd=true;
          this.getTodoList(
            this.active != null
              ? this.stageList[this.active].id
              : this.stageList[0].id
          );
        } else {
          this.$message.error(res.msg);
        }
      });
      this.todoVisible = false;
    },

    //更新待办事项状态
    toggleTodoStatus(e) {
		if(this.user.id=='15fd4f2685e94cb3bbdd5d4642dcc00f'){
			 return this.$message.error("体验者不能操作");
		}
      let formData = new FormData();
      formData.append("id", this.info.id);
      formData.append("status", e ? "2" : "1");
      this.todoId = this.info.id;
      changeStatus(formData).then((res) => {
        if (res.success) {
          this.$message({
            showClose: true,
            message: res.msg,
            type: "success",
          });
          // this.getStageData();
          this.getTodoList(
            this.active != null
              ? this.stageList[this.active].id
              : this.stageList[0].id,
            "change"
          );
        } else {
          this.$message.error(res.msg);
        }
      });
    },
    // 关闭新增待办弹框
    handleTodoCancel() {
      this.todoVisible = false;
    },
    // 显示新增阶段弹框
    handleShowStageModal(type, mdl) {
      if (type === "add") {
        this.stageModalMdl = null;
      } else {
        this.stageModalMdl = mdl;
      }
      this.stageVisible = true;
	  if(this.user.id=='15fd4f2685e94cb3bbdd5d4642dcc00f'){
	  	 return this.$message.error("体验者不能操作");
	  }
    },
    // 确认新增阶段
    handleStageOk() {
		if(this.user.id=='15fd4f2685e94cb3bbdd5d4642dcc00f'){
			 return this.$message.error("体验者不能操作");
		}
      const data = this.$refs.stageForm.ruleForm;
      const { name } = data;
      this.$refs.stageForm.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.stageVisible = false;
          let formData = new FormData();
          formData.append("lawCase.id", this.id);
          formData.append("name", name);
          if (data.id) {
            formData.append("id", data.id);
          }
          save(formData).then((res) => {
            if (res.success) {
              this.$message({
                showClose: true,
                message: res.msg,
                type: "success",
              });
              this.getStageData();
            } else {
              this.$message.error(res.msg);
            }
          });
        } else {
          return false;
        }
      });
    },
    // 关闭新增阶段弹框
    handleStageCancel() {
      this.stageModalMdl = null;
      this.stageVisible = false;
    },
    // 显示模板管理弹框
    handleShowManageModal(type, mdl) {
		if(this.user.id=='15fd4f2685e94cb3bbdd5d4642dcc00f'){
			 return this.$message.error("体验者不能操作");
		}
      this.manageVisible = true;
    },
    // 确认模板管理
    handleManageOk(e) {
		if(this.user.id=='15fd4f2685e94cb3bbdd5d4642dcc00f'){
			 return this.$message.error("体验者不能操作");
		}
      this.manageVisible = false;
      let formData = new FormData();
      formData.append("lawCaseId", this.id);
      formData.append("templateId", e);
      saveStage(formData).then((res) => {
        if (res.success) {
          this.getStageData();
          this.getCaseInfo()
          this.$message({
            type: "success",
            message: "设置成功!",
          });
        } else {
          this.$message.error(res.msg);
        }
      });
    },
    // 关闭模板管理弹框
    handleManageCancel() {
      this.manageVisible = false;
    },
    handleCommandStage(command, mdl, index) {
      switch (command) {
        case "add":
          this.handleShowStageModal("add");
          break;
        case "manage":
          this.handleShowManageModal();
          break;
        case "edit":
          this.handleShowStageModal("edit", mdl);
          break;
        case "delete":
          this.handleDeleteStage(mdl);
          break;
        case "set":
          this.setCurrentStage(mdl);
          break;
        default:
          break;
      }
    },
    // 设为当前阶段
    setCurrentStage(mdl) {
      let formData = new FormData();
      formData.append("id", mdl.id);
      setCurrent(formData).then((res) => {
        if (res.success) {
          this.getStageData();
          this.$message({
            type: "success",
            message: "设置成功!",
          });
        } else {
          this.$message.error(res.msg);
        }
      });
    },
    handleCommand() {},
    //点击阶段
    handleClickItem(index, item) {
      this.active = index;
      this.recordIndex = 0;
      this.getTodoList(item.id);
    },
    // 显示全部
    showAll() {
      this.active = null;
      this.getTodoList();
      this.recordIndex = 0;
    },
    // 点击待办
    // handleClickRecord(index) {
    //   this.recordIndex = index;
    //   this.getInfo(this.recordIndex);
    // },
    // 获取详情
    getInfo(id) {
      this.infoId = id;
      let formData = new FormData();
      formData.append("id", id);
      queryTodoById(formData).then((res) => {
        if (res.success) {
          this.info = res.todoInfo;
          this.info.checked = this.info.status === "2" ? true : false; 
		  console.log("======123===="+this.isAdd)
		  if(!this.isAdd){
			   this.todoModalMdl = res.todoInfo
		  }else{
			   this.todoModalMdl =null;
		  }
         
        }
      });
    },
    // 删除阶段
    handleDeleteStage(mdl) {
		if(this.user.id=='15fd4f2685e94cb3bbdd5d4642dcc00f'){
			 return this.$message.error("体验者不能操作");
		}
      this.$confirm("此操作将删除该阶段, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          let formData = new FormData();
          formData.append("ids", mdl.id);
          delStage(formData).then((res) => {
            if (res.success) {
              this.active = null;
              this.getStageData();
              this.getTodoList();
              this.$message({
                type: "success",
                message: "删除成功!",
              });
            }
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    // 删除待办记录
    handleDeleteRecord(id) {
		if(this.user.id=='15fd4f2685e94cb3bbdd5d4642dcc00f'){
			 return this.$message.error("体验者不能操作");
		}
      this.$confirm("此操作将永久删除该记录, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          let formData = new FormData();
          formData.append("ids", id);
          delTodo(formData).then((res) => {
            if (res.success) {
              this.getStageData();
              // this.getTodoList();
              // this.recordIndex = 0;
              // this.active = null;
              this.$message({
                type: "success",
                message: "删除成功!",
              });
            }
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
  },
};
</script>

<style lang="less" scoped>
.detail-info {
  background: #fff;
  display: flex;
  height: calc(100vh - 145px);
  margin-top: 20px;
  .stage-header {
    box-sizing: border-box;
    height: 56px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px;
    border-bottom: 1px solid #e1e4ee;
    .stage-txt {
      font-size: 16px;
      font-weight: 700;
      color: #333;
      span {
        color: #9fa5b9;
        font-weight: 400;
      }
    }
    i {
      font-size: 18px;
    }
  }
  .record-content {
    height: calc(100% - 60px);
    overflow-y: auto;
    .ih-title {
      flex: 1;
      display: flex;
      font-size: 15px;
      white-space: normal;
    }
    .item-right {
      flex: 1;
      margin-left: 20px;
      .stage-name {
        font-weight: bold;
        font-size: 17px;
      }
      .edit-icon {
        display: none;
      }
      .right-num i {
        font-size: 11px;
        color: #9fa5b9;
        margin-right: 18px;
        margin-left: 2px;
      }
      .right-num span {
        font-size: 14px;
        color: #a1a7ba;
      }
    }
  }

  .step-table {
    .item {
      padding: 10px;
      cursor: pointer;
      border-bottom: 1px dashed #e1e4ee;
      .content {
        font-size: 14px;
        padding-left: 23px;
        color: #999;
        white-space: pre-line;
      }
    }
    .item-heading {
      display: flex;
      align-items: center;
      margin: 10px 0;

      .ih-control {
        font-size: 12px;
        color: silver;
      }
    }
  }

  .record-left {
    min-width: 200px;
    box-sizing: border-box;
    border-right: 1px solid #e1e4ee;
    height: 100%;
  }
  .record-center {
    flex: 1;
    border-right: 1px solid #e1e4ee;
  }
  .record-right {
    width: 470px;
  }
  .stage-item-active {
    background: #f4f5fa;
  }
  .stage-item {
    position: relative;
    box-sizing: border-box;
    height: 93px;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    padding: 0 20px;
    &:hover {
      background: #f4f5fa;
    }
    &:hover .edit-icon {
      display: block;
    }
    .star-icon {
      .top-line {
        position: absolute;
        height: 35px;
        top: 0px;
        border-right: 2px dashed #ccc;
        left: 28px;
      }
      .bottom-line {
        position: absolute;
        height: 35px;
        border-right: 2px dashed #ccc;
        left: 28px;
      }
    }
    &:last-child {
      .bottom-line {
        display: none;
      }
    }
    &:first-child {
      .top-line {
        display: none;
      }
    }
  }
}
/deep/.el-checkbox__input.is-checked .el-checkbox__inner,
.el-checkbox__input.is-indeterminate .el-checkbox__inner {
  background-color: #ccc;
  border-color: #ccc;
}

/deep/.el-checkbox__input.is-checked + .el-checkbox__label {
  color: #999;
}
.right-content {
  .right-mark {
    padding: 20px 0;
    border-bottom: 1px dashed #e1e4ee;
    /deep/.el-checkbox__label {
      font-weight: bold;
    }
    .mark-txt {
      color: #858896;
      font-size: 14px;
      line-height: 28px;
      margin-top: 15px;
      white-space: pre-line;
    }
  }
  .right-info {
    display: flex;
    flex-wrap: wrap;
    padding-bottom: 28px;
    border-bottom: 1px dashed #e1e4ee;
    .info-item {
      width: 50%;
      padding-top: 18px;
      font-size: 14px;
      i {
        color: #9fa5b9;
        font-size: 15px;
      }
      .txt {
        color: #9fa5b9;
        padding-left: 2px;
      }
      .value {
        color: #444;
        padding-left: 20px;
      }
    }
  }
  .right-doc {
    padding: 20px 0;
    .doc-title {
      font-size: 15px;
      color: #666;
      display: flex;
      align-items: center;
    }
  }
}
/deep/ .el-tree-node__content {
  height: auto;
}
.mg20 {
  margin: 20px 0;
}
.custom-tree-node {
  margin-right: 5px;
  border-bottom: 1px dashed #e1e4ee;
  flex: 1;
  i {
    margin-left: 10px;
    font-size: 16px;
  }
  .node-btn {
    display: none;
  }
  .folder-img {
    width: 16px;
    height: 16px;
    margin-right: 10px;
  }
  &:hover .node-btn {
    display: block;
  }
  &:hover .ih-control {
    display: none;
  }
  .item-p {
    font-size: 14px;
    color: #999;
    white-space: pre-line;
    margin-bottom: 10px;
  }
  .ih-control {
    font-size: 12px;
    color: silver;
  }
}
.pass-bg {
  background: rgba(216, 252, 216, 0.3);
}
.nopass-bg {
  background: rgba(250, 220, 220, 0.3);
}
// /deep/ .el-icon-caret-right:before {
//     content: "+";
//     display: inline-block;
//     color: #c4c4c4;
//     font-size: 18px;
//     font-weight: bold;
//     width: 7px;
//     height: 14px;
//   }
/deep/.el-icon-plus:before {
}

/deep/ .is-leaf.el-icon-plus:before {
  content: "—";
  display: inline-block;
  color: #c4c4c4;
  font-size: 15px;
  font-weight: bold;
  width: 16px;
  height: 14px;
}
/deep/ .el-tree {
  .el-tree-node {
    position: relative;
    padding-left: 16px; // 缩进量
  }
  .el-tree-node__children {
    padding-left: 16px; // 缩进量
  }

  // 竖线
  .el-tree-node::before {
    content: "";
    height: 100%;
    width: 1px;
    position: absolute;
    left: -3px;
    top: -7px;
    border-width: 1px;
    border-left: 1px dashed #52627c;
  }
  // 当前层最后一个节点的竖线高度固定
  .el-tree-node:last-child::before {
    height: 38px; // 可以自己调节到合适数值
  }

  // 横线
  .el-tree-node::after {
    content: "";
    width: 24px;
    height: 20px;
    position: absolute;
    left: -3px;
    top: 30px;
    border-width: 1px;
    border-top: 1px dashed #52627c;
  }

  // 去掉最顶层的虚线，放最下面样式才不会被上面的覆盖了
  & > .el-tree-node::after {
    border-top: none;
  }
  & > .el-tree-node::before {
    border-left: none;
  }

  // 展开关闭的icon
  .el-tree-node__expand-icon {
    font-size: 16px;
    // 叶子节点（无子节点）
    &.is-leaf {
      color: transparent;
      // display: none; // 也可以去掉
    }
  }
}

.pdf_wrap{
  height: 100%;
  padding-bottom: 1.4rem;
  background-color: #fff;
}
</style>
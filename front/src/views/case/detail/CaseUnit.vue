<template>
  <div class="detail-info">
    <div class="detail-bottom">
      <el-card class="bottom-left">
        <div class="common-detail-tab">
          <div class="tab-item-wrap">
            <div
              :class="['tab-item', tabActive === index ? 'tab-active' : '']"
              v-for="(item, index) in tabList"
              :key="index"
              @click="changeTab(index)"
            >
              {{ item }}
            </div>
          </div>
          <div
            class="tab-operate primary"
            v-if="tabActive === 0"
            @click="handleShowPreservationModal()"
          >
            <i class="el-icon-circle-plus-outline mr10"></i>
            <span>添加保全</span>
          </div>
          <div
            class="tab-operate primary"
            v-else-if="tabActive === 1"
            @click="handleShowTrialModal('add')"
          >
            <i class="el-icon-circle-plus-outline mr10"></i>
            <span>添加记录</span>
          </div>
          <div
            class="tab-operate primary"
            v-else
            @click="handleShowExecuteModal('add')"
          >
            <i class="el-icon-circle-plus-outline mr10"></i>
            <span>添加执行</span>
          </div>
        </div>
        <div class="left-content" v-if="tabActive === 0">
          <div v-if="PreservationList.length">
            <div
              class="common-item"
              v-for="(item, index) in PreservationList"
              :key="index"
            >
              <div class="common-gray-title" key="1">
                <div class="title-txt align-center">
                  <span class="mr10">{{ item.propertyTypeName }}</span>
                  <el-tag size="small">{{ item.executeStatusName }}</el-tag>
                </div>
                <div class="title-operate">
                  <!-- <el-tooltip effect="dark" content="续封" placement="top">
                    <i
                      class="el-icon-s-finance mr20 cursor"
                      @click="handleShowRenewModal()"
                    ></i>
                  </el-tooltip> -->
                  <el-tooltip effect="dark" content="编辑" placement="top">
                    <i
                      class="el-icon-edit mr20 cursor"
                      @click="handleShowPreservationModal('edit', item)"
                    ></i>
                  </el-tooltip>
                  <el-tooltip effect="dark" content="删除" placement="top">
                    <i
                      class="el-icon-delete cursor"
                      @click="handleDeletePreservation(item.id)"
                    ></i>
                  </el-tooltip>
                </div>
              </div>
              <div class="common-item-content">
                <el-row class="mg10">
                  <el-col :span="8">
                    <div class="new-info-item">
                      <span class="item-title">裁定书编号：</span>
                      <span class="item-value">{{ item.rulingNumber }}</span>
                    </div>
                  </el-col>
                  <el-col :span="8">
                    <div class="new-info-item">
                      <span class="item-title">申请人：</span>
                      <span class="item-value">{{ item.applicant }}</span>
                    </div>
                  </el-col>
                  <el-col :span="8">
                    <div class="new-info-item">
                      <span class="item-title">被申请人：</span>
                      <span class="item-value">{{ item.respondent }}</span>
                    </div>
                  </el-col>
                </el-row>
                <el-row class="mg10">
                  <el-col :span="8">
                    <div class="new-info-item">
                      <span class="item-title">保全金额：</span>
                      <span class="item-value">{{
                        item.preservationMoney
                      }}</span>
                    </div>
                  </el-col>
                  <el-col :span="8">
                    <div class="new-info-item">
                      <span class="item-title">受理单位：</span>
                      <span class="item-value">{{ item.acceptUnit }}</span>
                    </div>
                  </el-col>
                  <el-col :span="8">
                    <div class="new-info-item">
                      <span class="item-title">承办人员：</span>
                      <span class="item-value">{{ item.undertakePerson }}</span>
                    </div>
                  </el-col>
                </el-row>
                <el-row class="mg10">
                  <el-col :span="8">
                    <div class="new-info-item">
                      <span class="item-title">查封日期：</span>
                      <span class="item-value">{{ item.seizureDate }}</span>
                    </div>
                  </el-col>
                  <el-col :span="8">
                    <div class="new-info-item">
                      <span class="item-title">续封提醒：</span>
                      <span class="item-value">{{
                        item.continueRemindDate
                      }}</span>
                    </div>
                  </el-col>
                  <el-col :span="8">
                    <div class="new-info-item">
                      <span class="item-title">查封到期日：</span>
                      <span class="item-value">{{
                        item.seizureExpirationDate
                      }}</span>
                    </div>
                  </el-col>
                </el-row>
                <el-row class="mg10">
                  <el-col :span="8">
                    <div class="new-info-item">
                      <span class="item-title">提醒方式：</span>
                      <span class="item-value">{{ item.remindMode }}</span>
                    </div>
                  </el-col>
                  <el-col :span="8">
                    <div class="new-info-item">
                      <span class="item-title">备注 ：</span>
                      <span class="item-value">{{ item.remarks }}</span>
                    </div>
                  </el-col>
                </el-row>
              </div>
            </div>
          </div>
          <div v-else>
            <NoData />
          </div>
        </div>
        <div class="left-content" v-else-if="tabActive === 1">
          <div v-if="trialList.length">
            <div
              class="common-item"
              v-for="(item, index) in trialList"
              :key="index"
            >
              <div class="common-gray-title" key="2">
                <div class="title-txt">{{ item.openCourtTypeName }}</div>
                <div class="title-operate">
                  <el-tooltip effect="dark" content="编辑" placement="top">
                    <i
                      class="el-icon-edit mr20 cursor"
                      @click="handleShowTrialModal('edit', item)"
                    ></i>
                  </el-tooltip>
                  <el-tooltip effect="dark" content="删除" placement="top">
                    <i
                      class="el-icon-delete cursor"
                      @click="handleDeleteTrial(item.id)"
                    ></i>
                  </el-tooltip>
                </div>
              </div>
              <el-row class="mg10" style="padding: 0 20px">
                <el-col :span="8">
                  <div class="new-info-item">
                    <span class="item-title">开庭日期：</span>
                    <span class="item-value">{{ item.openCourtDate }}</span>
                  </div>
                </el-col>
                <el-col :span="8">
                  <div class="new-info-item">
                    <span class="item-title">出庭律师：</span>
                    <span class="item-value" v-if="item.barrister">{{
                      item.barrister.name
                    }}</span>
                  </div>
                </el-col>
                <el-col :span="8">
                  <div class="new-info-item">
                    <span class="item-title">开庭地址：</span>
                    <span class="item-value">{{ item.openCourtAddress }}</span>
                  </div>
                </el-col>
              </el-row>
              <div class="new-Trial">
                <div class="title">争议焦点：</div>
                <div class="txt">{{ item.controversyFocus }}</div>
              </div>
              <div class="new-Trial">
                <div class="title">我方意见：</div>
                <div class="txt">{{ item.ourOpinion }}</div>
              </div>
              <div class="new-Trial">
                <div class="title">对方意见：</div>
                <div class="txt">{{ item.otherOpinion }}</div>
              </div>
              <div class="new-Trial">
                <div class="title">法官态度：</div>
                <div class="txt">{{ item.judgeAttitude }}</div>
              </div>
              <div class="new-Trial">
                <div class="title">庭审总结：</div>
                <div class="txt">{{ item.trialSummary }}</div>
              </div>
            </div>
          </div>
          <div v-else>
            <NoData text="暂无策略" />
          </div>
        </div>
        <div class="left-content" v-else>
          <div v-if="executeTableData.length">
            <div
              class="common-item"
              v-for="(item, index) in executeTableData"
              :key="index"
            >
              <div class="common-gray-title" key="1">
                <div class="title-txt align-center">
                  <span class="mr10">{{ item.measuresName }}</span>
                  <el-tag size="small">{{ item.statusName }}</el-tag>
                </div>
                <div class="title-operate">
                  <el-tooltip effect="dark" content="编辑" placement="top">
                    <i
                      class="el-icon-edit mr20 cursor"
                      @click="handleShowExecuteModal('edit', item)"
                    ></i>
                  </el-tooltip>
                  <el-tooltip effect="dark" content="删除" placement="top">
                    <i
                      class="el-icon-delete cursor"
                      @click="handleDeleteExecute(item.id)"
                    ></i>
                  </el-tooltip>
                </div>
              </div>
              <div class="common-item-content">
                <el-row class="mg10">
                  <el-col :span="8">
                    <div class="new-info-item">
                      <span class="item-title">受理单位：</span>
                      <span class="item-value">{{ item.acceptUnit }}</span>
                    </div>
                  </el-col>
                  <el-col :span="8">
                    <div class="new-info-item">
                      <span class="item-title">受理案号：</span>
                      <span class="item-value">{{ item.number }}</span>
                    </div>
                  </el-col>
                  <el-col :span="8">
                    <div class="new-info-item">
                      <span class="item-title">申请执行时间：</span>
                      <span class="item-value">{{ item.applyDate }}</span>
                    </div>
                  </el-col>
                </el-row>
                <el-row class="mg10">
                  <el-col :span="24">
                    <div class="new-info-item">
                      <span class="item-title">执行请求：</span>
                      <span class="item-value">{{ item.executeRequest }}</span>
                    </div>
                  </el-col>
                </el-row>
                <el-row class="mg10">
                  <el-col :span="24">
                    <div class="new-info-item">
                      <span class="item-title">履行情况：</span>
                      <span class="item-value">{{ item.performance }}</span>
                    </div>
                  </el-col>
                </el-row>
              </div>
            </div>
          </div>
          <div v-else>
            <NoData />
          </div>
        </div>
      </el-card>
      <div class="bottom-right">
        <el-card class="right-item">
          <div class="flex justify-between">
            <div class="flex">
              <i class="el-icon-office-building icon"></i>
              <span>受理单位</span>
            </div>
            <div @click="handleShowUnitInfoModal">
              <i class="el-icon-edit cursor"></i>
            </div>
          </div>
          <div class="unit-content">
            <div class="unit-content-left">
              <div class="left-circle">
                <i class="el-icon-place"></i>
              </div>
              <div class="left-line"></div>
              <div class="left-circle">
                <i class="el-icon-s-check"></i>
              </div>
              <div class="left-line"></div>
              <div class="left-circle">
                <i class="el-icon-school"></i>
              </div>
            </div>
            <div class="unit-content-right">
              <div class="right-area">地区</div>
              <div class="right-area-txt">
                {{ lawCase.acceptUnitArea || "暂未填写" }}
              </div>
              <div class="right-area" style="margin-top: 30px">单位类型：</div>
              <div class="right-area-txt">
                {{ lawCase.acceptUnitTypeName || "暂未填写" }}
              </div>
              <div class="right-area" style="margin-top: 30px">单位名称：</div>
              <div class="right-area-txt">
                {{ lawCase.acceptUnitName || "暂未填写" }}
              </div>
            </div>
          </div>
        </el-card>
        <el-card class="right-item undertaker-box" style="margin-top: 20px">
          <div class="flex justify-between">
            <div class="flex">
              <i class="el-icon-s-custom icon"></i>
              <span>承办人员</span>
            </div>
            <div
              class="tab-operate primary cursor"
              @click="handleShowCatererModal('add')"
            >
              <i class="el-icon-circle-plus-outline mr10"></i>
              <span>添加承办人</span>
            </div>
          </div>
          <div class="caterer-content">
            <div
              class="caterer-item"
              v-for="item in catererList"
              :key="item.id"
            >
              <div class="caterer-item-title">
                <div class="title-circle">{{ item.name.substring(0, 1) }}</div>
                <div class="item-name">{{ item.name }}</div>
                <i
                  class="el-icon-edit mr20"
                  @click="handleShowCatererModal('edit', item)"
                ></i>
                <i
                  class="el-icon-delete"
                  @click="handleDeleteCaterer(item.id)"
                ></i>
              </div>
              <div class="new-info-item-wrap">
                <div class="new-info-item text-ellipsis">
                  <span class="item-title">职务：</span>
                  <span class="item-value">{{ item.post }}</span>
                </div>
              </div>
              <div class="new-info-item-wrap">
                <div class="new-info-item text-ellipsis">
                  <span class="item-title">电话：</span>
                  <span class="item-value">{{ item.phone }}</span>
                </div>
              </div>
              <div class="new-info-item-wrap">
                <div class="new-info-item text-ellipsis">
                  <span class="item-title">科室：</span>
                  <span class="item-value">{{ item.department }}</span>
                </div>
              </div>
              <div class="new-info-item-wrap">
                <div class="new-info-item text-ellipsis">
                  <span class="item-title">地址：</span>
                  <span class="item-value">{{ item.address }}</span>
                </div>
              </div>
              <div class="new-info-item-wrap">
                <div class="new-info-item text-ellipsis">
                  <span class="item-title">备注：</span>
                  <span class="item-value">{{ item.remark }}</span>
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </div>
    </div>
    <CreatePreservation
      ref="preservationForm"
      :visible="preservationVisible"
      :model="preservationModalMdl"
      @cancel="handlePreservationCancel"
      @ok="handlePreservationOk"
    />
    <CreateTrial
      ref="trialForm"
      :visible="trialVisible"
      :model="trialModalMdl"
      @cancel="handleTrialCancel"
      @ok="handleTrialOk"
    />
    <CreateExecute
      ref="ExecuteForm"
      :visible="executeVisible"
      :model="executeModalMdl"
      @cancel="handleExecuteCancel"
      @ok="handleExecuteOk"
    />
    <RenewDate
      ref="renewForm"
      :visible="renewVisible"
      :model="renewModalMdl"
      @cancel="handleRenewCancel"
      @ok="handleRenewOk"
    />
    <UnitInfo
      ref="unitInfoForm"
      :visible="unitInfoVisible"
      :model="unitInfoModalMdl"
      @cancel="handleUnitInfoCancel"
      @ok="handleUnitInfoOk"
    />
    <CreateCaterer
      ref="catererForm"
      :visible="catererVisible"
      :model="catererModalMdl"
      @cancel="handleCatererCancel"
      @ok="handleCatererOk"
    />
  </div>
</template>

<script>
import storage from "store";
import CreatePreservation from "./modal/CreatePreservation.vue";
import CreateTrial from "./modal/CreateTrial.vue";
import CreateExecute from "./modal/CreateExecute";
import RenewDate from "./modal/RenewDate";
import UnitInfo from "./modal/UnitInfo";
import CreateCaterer from "./modal/CreateCaterer";
import NoData from "@/components/NoData";
import { editUnit, detailInfo } from "@/api/case/manage";

import {
  getPreservationList,
  addPreservation,
  delPreservation,
  queryPreservationById,
} from "@/api/case/casePropertyPreservation";
import {
  getTrialList,
  addTrial,
  delTrial,
  queryTrialById,
} from "@/api/case/caseTrialRecord";
import {
  getExecuteList,
  addExecute,
  delExecute,
  queryExecuteById,
} from "@/api/case/caseExecuteSituation";
import {
  getUndertakeList,
  addUndertake,
  delUndertake,
  queryUndertakeById,
} from "@/api/case/caseUndertakePerson";
const dictList = storage.get("dictList");
import { mapMutations, mapState } from "vuex";

const property_execute_statusList = dictList.property_execute_status;
const property_typeList = dictList.property_type;
const open_court_typeList = dictList.open_court_type;
const execute_measuresList = dictList.execute_measures;
const execute_statusList = dictList.execute_status;
const unitTypeList = dictList.accept_unit_type;

export default {
  name: "CaseInfo",
  components: {
    CreatePreservation,
    NoData,
    CreateTrial,
    CreateExecute,
    RenewDate,
    UnitInfo,
    CreateCaterer,
  },
  data() {
    return {
      editVisible: false,
      editModalMdl: null,
      preservationVisible: false,
      preservationModalMdl: null,
      trialVisible: false,
      trialModalMdl: null,
      executeVisible: false,
      executeModalMdl: null,
      renewVisible: false,
      renewModalMdl: null,
      unitInfoVisible: false,
      unitInfoModalMdl: null,
      catererVisible: false,
      catererModalMdl: null,
      tabList: ["财产保全", "庭审记录", "执行情况"],
      tabActive: 0,
      PreservationList: [],
      trialList: [],
      executeTableData: [],
      catererList: [],
      lawCase: {},
    };
  },
  mounted() {
    this.id = this.$route.query.id;
    this.getPreservationList();
    this.getCaseInfo();
    this.getUndertakeList();
  },
  computed: mapState({
    caseInfo: (state) => state.caseInfo.caseInfo,
  }),
  methods: {
    getCaseInfo() {
      let formData = new FormData();
      formData.append("id", this.id);
      detailInfo(formData).then((res) => {
        this.lawCase = res.data.lawCase;
        unitTypeList.map((item) => {
          if (item.value === this.lawCase.acceptUnitType) {
            this.lawCase.acceptUnitTypeName = item.label;
          }
        });
      });
    },
    //获取财产保全列表
    getPreservationList() {
      let formData = new FormData();
      formData.append("lawCase.id", this.id);
      getPreservationList(formData).then((res) => {
        this.PreservationList = res.page.list;
        this.PreservationList.map((item) => {
          property_execute_statusList.map((child) => {
            if (item.executeStatus === child.value) {
              item.executeStatusName = child.label;
            }
          });
          property_typeList.map((son) => {
            if (item.propertyType === son.value) {
              item.propertyTypeName = son.label;
            }
          });
        });
      });
    },
    //获取庭审记录列表
    getTrialList() {
      let formData = new FormData();
      formData.append("lawCase.id", this.id);
      getTrialList(formData).then((res) => {
        this.trialList = res.page.list;
        this.trialList.map((item) => {
          execute_statusList.map((child) => {
            if (item.openCourtType === child.value) {
              item.openCourtTypeName = child.label;
            }
          });
        });
      });
    },
    //获取执行情况列表
    getExecuteList() {
      let formData = new FormData();
      formData.append("lawCase.id", this.id);
      getExecuteList(formData).then((res) => {
        this.executeTableData = res.page.list;
        this.executeTableData.map((item) => {
          execute_statusList.map((child) => {
            if (item.status === child.value) {
              item.statusName = child.label;
            }
          });
          execute_measuresList.map((child) => {
            if (item.measures === child.value) {
              item.measuresName = child.label;
            }
          });
        });
      });
    },
    //获取承办人列表
    getUndertakeList() {
      let formData = new FormData();
      formData.append("lawCase.id", this.id);
      getUndertakeList(formData).then((res) => {
        this.catererList = res.page.list;
      });
    },
    changeTab(index) {
      this.tabActive = index;
      if (index === 0) {
        this.getPreservationList();
      } else if (index === 1) {
        this.getTrialList();
      } else {
        this.getExecuteList();
      }
    },
    // 显示修改信息弹框
    handleShowEditModal() {
      this.editVisible = true;
    },
    // 确认修改信息
    handleEditOk() {
      this.editVisible = false;
    },
    // 关闭修改信息弹框
    handleEditCancel() {
      this.editVisible = false;
    },
    // 显示新增财产保全弹框
    handleShowPreservationModal(type, item) {
      if (type === "edit") {
        this.preservationModalMdl = item;
      } else {
        this.preservationModalMdl = null;
      }
      this.preservationVisible = true;
    },
    // 确认新增财产保全
    handlePreservationOk() {
      this.$refs.preservationForm.$refs.ruleForm.validate((valid) => {
        if (valid) {
          const data = this.$refs.preservationForm.ruleForm;
          let {
            acceptUnit,
            applicant,
            continueRemindDate,
            executeStatus,
            id,
            preservationMoney,
            propertyType,
            remarks,
            remindMode,
            respondent,
            rulingNumber,
            seizureDate,
            seizureExpirationDate,
            undertakePerson,
          } = data;
          const param = {
            acceptUnit,
            applicant,
            continueRemindDate,
            executeStatus,
            id,
            lawCase: {
              id: this.id,
            },
            preservationMoney,
            propertyType,
            remarks,
            remindMode,
            respondent,
            rulingNumber,
            seizureDate,
            seizureExpirationDate,
            undertakePerson,
          };
          addPreservation(param).then((res) => {
            this.preservationVisible = false;
            if (res.success) {
              this.$message({
                showClose: true,
                message: res.msg,
                type: "success",
              });
              this.getPreservationList();
            } else {
              this.$message.error(res.msg);
            }
          });
        } else {
          return;
        }
      });
    },
    // 关闭新增财产保全弹框
    handlePreservationCancel() {
      this.preservationVisible = false;
    },
    // 显示添加续封日期弹框
    handleShowRenewModal() {
      this.renewVisible = true;
    },
    // 确认添加续封日期
    handleRenewOk() {
      this.renewVisible = false;
    },
    // 关闭添加续封日期弹框
    handleRenewCancel() {
      this.renewVisible = false;
    },

    // 显示单位信息弹框
    handleShowUnitInfoModal() {
      this.unitInfoVisible = true;
      this.unitInfoModalMdl = this.lawCase;
    },
    // 确认单位信息
    handleUnitInfoOk() {
      const data = this.$refs.unitInfoForm.ruleForm;
      const { acceptUnitArea, acceptUnitType, acceptUnitName, id } = data;
      let formData = new FormData();
      formData.append("id", id);
      formData.append(
        "acceptUnitArea",
        (acceptUnitArea && acceptUnitArea.join("/")) || ""
      );
      formData.append("acceptUnitType", acceptUnitType);
      formData.append("acceptUnitName", acceptUnitName);
      editUnit(formData).then((res) => {
        this.unitInfoVisible = false;
        if (res.success) {
          this.$message({
            showClose: true,
            message: res.msg,
            type: "success",
          });
          this.getCaseInfo();
        } else {
          this.$message.error(res.msg);
        }
      });
    },
    // 关闭单位信息弹框
    handleUnitInfoCancel() {
      this.unitInfoVisible = false;
    },
    // 显示庭审记录弹框
    handleShowTrialModal(type, item) {
      if (type === "edit") {
        this.trialModalMdl = item;
      } else {
        this.trialModalMdl = null;
      }
      this.trialVisible = true;
    },
    // 确认庭审记录
    handleTrialOk() {
      this.$refs.trialForm.$refs.ruleForm.validate((valid) => {
        if (valid) {
          const data = this.$refs.trialForm.ruleForm;
          const {
            barrister,
            controversyFocus, // 争议焦点
            id,
            judgeAttitude, // 法官态度
            openCourtAddress, // 开庭地址
            openCourtDate, // 开庭日期
            openCourtType, // 开庭类型 字典：open_court_type
            otherOpinion, // 对方意见
            ourOpinion, // 我方意见
            remarks,
            trialSummary, // 庭审总结
          } = data;
          const param = {
            barrister,
            controversyFocus, // 争议焦点
            id,
            lawCase: {
              id: this.id,
            },
            judgeAttitude, // 法官态度
            openCourtAddress, // 开庭地址
            openCourtDate, // 开庭日期
            openCourtType, // 开庭类型 字典：open_court_type
            otherOpinion, // 对方意见
            ourOpinion, // 我方意见
            remarks,
            trialSummary, // 庭审总结
          };
          addTrial(param).then((res) => {
            this.trialVisible = false;
            if (res.success) {
              this.$message({
                showClose: true,
                message: res.msg,
                type: "success",
              });
              this.getTrialList();
            } else {
              this.$message.error(res.msg);
            }
          });
        } else {
          return;
        }
      });
    },
    // 关闭庭审记录弹框
    handleTrialCancel() {
      this.trialVisible = false;
    },
    // 显示添加执行弹框
    handleShowExecuteModal(type, item) {
      if (type === "edit") {
        this.executeModalMdl = item;
      } else {
        this.executeModalMdl = null;
      }
      this.executeVisible = true;
    },
    // 确认添加执行
    handleExecuteOk() {
      this.$refs.ExecuteForm.$refs.ruleForm.validate((valid) => {
        if (valid) {
          const data = this.$refs.ExecuteForm.ruleForm;
          const {
            acceptUnit, // 受理单位
            applyDate, // 申请执行日
            executeRequest, // 执行请求
            id,
            measures, // 执行措施 字典：execute_measures
            number, // 执行案号
            performance, // 履行情况
            remarks,
            status, // 执行状态 字典：execute_status
          } = data;
          const param = {
            lawCase: {
              id: this.id,
            },
            acceptUnit, // 受理单位
            applyDate, // 申请执行日
            executeRequest, // 执行请求
            id,
            measures, // 执行措施 字典：execute_measures
            number, // 执行案号
            performance, // 履行情况
            remarks,
            status, // 执行状态 字典：execute_status
          };
          addExecute(param).then((res) => {
            this.executeVisible = false;
            if (res.success) {
              this.$message({
                showClose: true,
                message: res.msg,
                type: "success",
              });
              this.getExecuteList();
            } else {
              this.$message.error(res.msg);
            }
          });
        } else {
          return;
        }
      });
    },
    // 关闭添加执行弹框
    handleExecuteCancel() {
      this.executeVisible = false;
    },
    // 显示添加承办人员弹框
    handleShowCatererModal(type, item) {
      if (type === "edit") {
        this.catererModalMdl = item;
      } else {
        this.catererModalMdl = null;
      }
      this.catererVisible = true;
    },
    // 确认添加承办人员
    handleCatererOk() {
      this.$refs.catererForm.$refs.ruleForm.validate((valid) => {
        if (valid) {
          const data = this.$refs.catererForm.ruleForm;
          const { name, phone, department, post, address, id } = data;
          let formData = new FormData();
          formData.append("id", id);
          formData.append("name", name);
          formData.append("department", department);
          formData.append("phone", phone);
          formData.append("post", post);
          formData.append("address", address);
          formData.append("lawCase.id", this.id);
          addUndertake(formData).then((res) => {
            this.catererVisible = false;
            if (res.success) {
              this.$message({
                showClose: true,
                message: res.msg,
                type: "success",
              });
              this.getUndertakeList();
            } else {
              this.$message.error(res.msg);
            }
          });
        } else {
          return;
        }
      });
    },
    // 关闭添加承办人员弹框
    handleCatererCancel() {
      this.catererVisible = false;
    },
    //删除保全记录
    handleDeletePreservation(id) {
      this.$confirm("此操作将永久删除该记录, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          let formData = new FormData();
          formData.append("ids", id);
          delPreservation(formData).then((res) => {
            if (res.success) {
              this.getPreservationList();
              this.$message({
                type: "success",
                message: "删除成功!",
              });
            }
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    // 删除庭审记录
    handleDeleteTrial(id) {
      this.$confirm("此操作将永久删除该庭审记录, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          let formData = new FormData();
          formData.append("ids", id);
          delTrial(formData).then((res) => {
            if (res.success) {
              this.getTrialList();
              this.$message({
                type: "success",
                message: "删除成功!",
              });
            }
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    //删除执行记录
    handleDeleteExecute(id) {
      this.$confirm("确认删除该执行情况?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          let formData = new FormData();
          formData.append("ids", id);
          delExecute(formData).then((res) => {
            if (res.success) {
              this.getExecuteList();
              this.$message({
                type: "success",
                message: "删除成功!",
              });
            }
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    //删除承办人员
    handleDeleteCaterer(id) {
      this.$confirm("确认删除该承办人员?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          let formData = new FormData();
          formData.append("ids", id);
          delUndertake(formData).then((res) => {
            if (res.success) {
              this.getUndertakeList();
              this.$message({
                type: "success",
                message: "删除成功!",
              });
            }
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
  },
};
</script>

<style lang="less" scoped>
.detail-info {
  height: calc(100vh - 165px);
  margin-top: 20px;
  .info-box {
    width: 100%;
    height: 280px;
    .new-info-item {
      font-size: 14px;
      .item-title {
        color: #858896;
      }
      .item-value {
        color: #303443;
        padding-right: 5px;
        flex: 1;
      }
    }
  }
  .detail-bottom {
    margin-top: 20px;
    display: flex;
    margin-bottom: 20px;
    height: 100%;
    .bottom-left {
      width: 67%;
      background: #fff;
      margin-right: 20px;
      border-radius: 6px;
      .common-detail-tab {
        display: flex;
        align-items: center;
        height: 56px;
        border-bottom: 1px solid #e1e4ee;
        .tab-item-wrap {
          flex: 1;
          display: flex;
          margin-left: 10px;
          .tab-active {
            color: #303443;
            font-weight: 700;
            border-bottom: 3px solid #303443;
          }
          .tab-item {
            width: 72px;
            text-align: center;
            font-size: 16px;
            color: #858896;
            height: 56px;
            line-height: 56px;
            margin-right: 40px;
            cursor: pointer;
          }
        }
        .tab-operate {
          font-size: 15px;
          cursor: pointer;
          margin-right: 20px;
        }
      }
    }
    .bottom-right {
      width: 33%;
      .right-item {
        // height: calc(50% - 10px);
        min-height: 245px;
        background: #fff;
        border-radius: 6px;
        .icon {
          font-size: 22px;
          color: #9fa5b9;
        }
        .content-link-wai {
          padding: 0px 30px;
          margin-top: 30px;
          .man-type {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            color: #fff;
            font-size: 12px;
            text-align: center;
            line-height: 36px;
            background: #f6c379;
          }
          .man-name {
            padding-left: 10px;
            font-size: 14px;
            color: #303443;
            padding-right: 30px;
          }
        }
        .unit-content {
          display: flex;
          margin-top: 20px;
          .unit-content-left {
            width: 38px;
            .left-circle {
              width: 38px;
              height: 38px;
              line-height: 38px;
              border-radius: 50%;
              border: 1px dashed #e1e4ee;
              text-align: center;
              i {
                color: #888;
                font-size: 22px;
              }
            }
            .left-line {
              width: 1px;
              height: 30px;
              border-right: 1px dashed #e1e4ee;
              margin-left: 19px;
            }
          }
          .unit-content-right {
            flex: 1;
            padding-left: 30px;
            .right-area {
              color: #858896;
              font-size: 14px;
            }
            .right-area-txt {
              color: #303443;
              font-size: 16px;
            }
          }
        }
      }
    }
  }
}
.common-item {
  margin: 10px 0;
  .common-item-content {
    margin: 20px;
    border-bottom: 1px dashed #e1e4ee;
  }
  .new-info-item {
    font-size: 14px;
    .item-title {
      color: #858896;
    }
    .item-value {
      color: #303443;
      padding-right: 5px;
      flex: 1;
    }
  }
  .common-gray-title {
    height: 40px;
    background: #f7f7fc;
    display: flex;
    align-items: center;
    padding: 0 20px;
    .title-txt {
      font-size: 16px;
      color: #303443;
      flex: 1;
      display: flex;
    }
  }
  .new-Trial {
    display: flex;
    font-size: 14px;
    line-height: 22px;
    margin-top: 20px;
    margin-bottom: 20px;
    padding: 0 20px;
    .title {
      color: #858896;
    }
    .txt {
      flex: 1;
      color: #303443;
      white-space: pre-line;
    }
  }
}
.caterer-content {
  padding: 0 30px;
  .caterer-item {
    border-bottom: 1px dashed #e1e4ee;
    padding-bottom: 30px;
    font-size: 14px;
    .caterer-item-title {
      display: flex;
      align-items: center;
      margin-top: 20px;
      .title-circle {
        width: 38px;
        height: 38px;
        line-height: 38px;
        background: #c8cedf;
        border-radius: 50%;
        color: #fff;
        font-size: 18px;
        text-align: center;
      }
      .item-name {
        flex: 1;
        font-size: 18px;
        color: #303443;
        padding-left: 15px;
      }
      i {
        color: #9fa5b9;
        font-size: 16px;
        cursor: pointer;
      }
    }
    .new-info-item-wrap {
      margin-top: 14px;
      .new-info-item {
        box-sizing: border-box;
        display: flex;
        .item-title {
          color: #858896;
        }
        .item-value {
          color: #303443;
          padding-right: 5px;
          flex: 1;
        }
      }
    }
  }
}
.undertaker-box {
  height: calc(100% - 285px);
}
</style>
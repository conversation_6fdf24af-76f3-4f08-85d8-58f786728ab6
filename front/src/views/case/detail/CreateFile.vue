<template>
  <div id="createFile">
    <iframe ref="docContent" class="frame-content" :src="src" />
  </div>
</template>

<script>
import { createDoc, getUrl } from "@/api/case/doc";

export default {
  name: "createFile",
  data() {
    return {
      src: "",
    };
  },
  created() {
    this.getCreateFilePath(sessionStorage.createFileUrlType);
  },
  methods: {
    getCreateFilePath(type) {
      let formData = new FormData();
      formData.append("type", type);
      formData.append("fileDirectoryId", sessionStorage.fileDirectoryId);
      formData.append("lawCaseId", sessionStorage.lawCaseId);
      createDoc(formData)
        .then((res) => {
          if (res.success) {
            this.handleOpen(res)
          } else {
            this.$message.error("请求错误！");
          }
        })
        .catch(() => {
          this.$message.error("请求错误！");
        });
    },
    handleOpen(item) {
      let params = {
        _w_fname: item.url,
        _w_fileid: new Date().getTime(),
        operateType: "write",
      };
      getUrl(params).then((res) => {
        if (res) {
          const wps = this.wps.config({
            mount: document.querySelector("#app"),
            wpsUrl: res.url,
          });
          wps.setToken({ token: res.token });
          this.src = res.wpsUrl

        } else {
          this.$message.error("请求错误！");
        }
      });
    },
  },
  mounted() {
    // const _this = this;
    // window.onbeforeunload = function(e) {
    //     e = e || window.event;
    //     // 兼容IE8和Firefox 4之前的版本
    //     if (e) {
    //         _this.$store.state.createFileDone = true;
    //     }
    //     // Chrome, Safari, Firefox 4+, Opera 12+ , IE 9+
    //     _this.$store.state.createFileDone = true;
    // };
  },
};
</script>

<style scoped>
.frame-content {
  display: flex;
  flex-direction: column;
  padding: 0;
  margin: 0;
  width: 100%;
  height: 100%;
  border: none;
  /* 防止双击缩放 */
  touch-action: manipulation;
}
body #createFile {
  font-family: "Avenir", Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #2c3e50;
  height: 100%;
  width: 100%;
  /* 防止双击缩放 */
  touch-action: manipulation;
}
body #wps-iframe {
  display: flex;
  flex-direction: column;
  padding: 0;
  margin: 0;
  height: 100%;
  /* 防止双击缩放 */
  touch-action: manipulation;
}
</style>
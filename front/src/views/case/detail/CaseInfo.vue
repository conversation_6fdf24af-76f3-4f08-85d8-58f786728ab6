<template>
  <div class="detail-info">
    <el-card class="info-box">
      <div class="flex align-center justify-between">
        <div>
          <i class="el-icon-notebook-2 mr10"></i>
          <span>基本案情</span>
        </div>
        <div class="primary cursor" @click="handleShowEditModal">
          <i class="el-icon-edit mr10"></i>
          <span>编辑案情</span>
        </div>
      </div>
      <el-row class="mg10">
        <el-col :span="6">
          <div class="new-info-item">
            <span class="item-title">案件类型：</span>
            <span class="item-value">{{lawCase.typeName}}</span>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="new-info-item">
            <span class="item-title">收费方式：</span>
            <span class="item-value">{{lawCase.chargeModeName}}</span>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="new-info-item">
            <span class="item-title">收费简介：</span>
            <span class="item-value">民事案件</span>
          </div>
        </el-col>
      </el-row>
       <el-row class="mg10">
        <el-col :span="6">
          <div class="new-info-item">
            <span class="item-title">案由：</span>
            <span class="item-value">{{lawCase.caseCause && lawCase.caseCause.name || ''}}</span>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="new-info-item">
            <span class="item-title">合同金额：</span>
            <span class="item-value">{{lawCase.contractMoney}}</span>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="new-info-item">
            <span class="item-title">委托日期：</span>
            <span class="item-value">{{lawCase.entrustDate}}</span>
          </div>
        </el-col>
      </el-row>
       <el-row class="mg10">
        <el-col :span="6">
          <div class="new-info-item">
            <span class="item-title">审理程序：</span>
            <span class="item-value">{{lawCase.caseProgram.name}}</span>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="new-info-item">
            <span class="item-title">案件标的：</span>
            <span class="item-value">{{lawCase.subjectMatter}}</span>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="new-info-item">
            <span class="item-title">立案日期：</span>
            <span class="item-value">{{lawCase.recordDate}}</span>
          </div>
        </el-col>
      </el-row>
       <el-row class="mg10">
        <el-col :span="6">
          <div class="new-info-item">
            <span class="item-title">审理结果：</span>
            <span class="item-value">{{lawCase.trialResultName}}</span>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="new-info-item">
            <span class="item-title">胜诉金额：</span>
            <span class="item-value">{{lawCase.winMoney}}</span>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="new-info-item">
            <span class="item-title">裁决日期：</span>
            <span class="item-value">{{lawCase.rulingDate}}</span>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="new-info-item">
            <span class="item-title">结案状态：</span>
            <span class="item-value">{{lawCase.settleCaseStatusName}}</span>
          </div>
        </el-col>
      </el-row>
      <el-row class="mg10">
        <el-col :span="6">
          <div class="new-info-item">
            <span class="item-title">律所案号：</span>
            <span class="item-value">{{lawCase.number}}</span>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="new-info-item">
            <span class="item-title">实际回款：</span>
            <span class="item-value">{{lawCase.actualBackMoney}}</span>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="new-info-item">
            <span class="item-title">结案日期：</span>
            <span class="item-value">{{lawCase.settleCaseDate}}</span>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="new-info-item">
            <span class="item-title">归档人：</span>
            <span class="item-value">{{lawCase.archiveUser && lawCase.archiveUser.name || ''}}</span>
          </div>
        </el-col>
      </el-row>
        <el-row class="mg10">
        <el-col :span="6">
          <div class="new-info-item">
            <span class="item-title">关联客户：</span>
            <span class="item-value">民事案件</span>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="new-info-item">
            <span class="item-title">关联项目：</span>
            <span class="item-value">民事案件</span>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="new-info-item">
            <span class="item-title">归档日期：</span>
            <span class="item-value">{{lawCase.archiveDate}}</span>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="new-info-item">
            <span class="item-title">档案保管地：</span>
            <span class="item-value">{{lawCase.custodyPlace}}</span>
          </div>
        </el-col>
      </el-row>
      </el-row>
        <el-row class="mg10">
        <el-col :span="24">
          <div class="new-info-item">
            <span class="item-title">案件备注：</span>
            <span class="item-value">{{lawCase.remarks}}</span>
          </div>
        </el-col>
        </el-row>
    </el-card>
    <div class="detail-bottom">
      <el-card class="bottom-left">
        <div class="common-detail-tab">
          <div class="tab-item-wrap">
            <div :class="['tab-item', tabActive===index?'tab-active':'']" v-for="(item, index) in tabList" :key="index" @click="changeTab(index)">{{item}}</div>
          </div>
          <div class="tab-operate primary" v-if="tabActive===0" @click="handleShowClientModal('add')">
            <i class="el-icon-circle-plus-outline mr10"></i>
            <span>添加当事人</span>
          </div>
           <div class="tab-operate primary" v-else-if="tabActive===1" @click="handleShowStrategyModal('add')">
            <i class="el-icon-circle-plus-outline mr10"></i>
            <span>添加策略</span>
          </div>
           <div class="tab-operate primary" v-else @click="handleShowRelatedModal">
            <i class="el-icon-circle-plus-outline mr10"></i>
            <span>新增关联</span>
          </div>
        </div>
        <div class="left-content" v-if="tabActive===0">
          <div v-if="tableData.length">
            <el-table :data="tableData" style="width: 100%" :key="1">
              <el-table-column type="expand">
                <template slot-scope="props">
                    <el-row>
                      <el-col :span="4">
                          <span>民族:</span>
                          <span>{{ props.row.nation }}</span>
                      </el-col>
                      <el-col :span="4">
                          <span>性别:</span>
                          <span>{{ props.row.sex==='1'?'男':'女' }}</span>
                      </el-col>
                      <el-col :span="8">
                          <span>身份证号码:</span>
                          <span>{{ props.row.idNumber }}</span>
                      </el-col>
                      <el-col :span="8">
                          <span>住所地:</span>
                          <span>{{ props.row.address }}</span>
                      </el-col>
                      <el-col :span="7">
                          <span>备注:</span>
                          <span>{{ props.row.remark }}</span>
                      </el-col>
                    </el-row>
                </template>
              </el-table-column>
              <el-table-column label="类型" prop="type">
                 <template slot-scope="scope">
                  <div>{{scope.row.type==='1'?'个人':'单位'}}</div>
                </template>
              </el-table-column>
              <el-table-column label="名称" prop="name"> </el-table-column>
              <el-table-column label="委托方" prop="isEntrust">
                <template slot-scope="scope">
                  <div>{{scope.row.isEntrust==='1'?'是':'否'}}</div>
                </template>
              </el-table-column>
<!--              <el-table-column label="属性" prop="attribute"> </el-table-column>-->
              <el-table-column label="联系电话" prop="phone"> </el-table-column>
              <el-table-column label="地址" prop="address"> </el-table-column>
              <el-table-column label="操作">
                <template slot-scope="scope">
                  <div class="flex align-center">
                    <!-- <el-tooltip effect="dark" content="复制" placement="top">
                      <i class="el-icon-document-copy mr10 cursor" @click="handleShowClientModal('copy')"></i>
                    </el-tooltip> -->
                    <el-tooltip effect="dark" content="编辑" placement="top">
                      <i class="el-icon-edit mr10 cursor" @click="handleShowClientModal('edit', scope)"></i>
                    </el-tooltip>
                    <el-tooltip effect="dark" content="删除" placement="top">
                      <i class="el-icon-delete cursor" @click="handleDeleteClient(scope)"></i>
                    </el-tooltip>
                  </div>
                </template>
              </el-table-column>
          </el-table>
          </div>
          <div v-else>
            <NoData text="暂无当事人"/>
          </div>
        </div>
        <div class="left-content" v-else-if="tabActive===1">
          <div v-if="strategyList.length">
            <div class="common-item" v-for="(item, index) in strategyList" :key="index">
              <div class="common-gray-title">
                <div class="title-txt">策略一</div>
                <div class="title-operate">
                  <el-tooltip effect="dark" content="编辑" placement="top">
                    <i class="el-icon-edit mr20 cursor" @click="handleShowStrategyModal('edit', item)"></i>
                  </el-tooltip>
                  <el-tooltip effect="dark" content="删除" placement="top">
                    <i class="el-icon-delete cursor" @click="handleDeleteStrategy(item)"></i>
                  </el-tooltip>
                </div>
              </div>
              <div class="new-strategy">
                <div class="title">问题描述：</div>
                <div class="txt">{{item.question}}</div>
              </div>
              <div class="new-strategy">
                <div class="title">解决方案：</div>
                <div class="txt">{{item.solution}}</div>
              </div>
              <div class="new-strategy">
                <div class="title">处理结果：</div>
                <div class="txt">{{item.result}}</div>
              </div>
            </div>
          </div>
          <div v-else>
            <NoData text="暂无策略"/>
          </div>
        </div>
        <div class="left-content" v-else>
          <div v-if="relatedTableData.length">
            <el-table :data="relatedTableData" style="width: 100%" key="2">
              <el-table-column prop="relationCase.name" label="案件名称">
              </el-table-column>
              <el-table-column prop="relationCase.caseProgram.name" label="审理程序">
              </el-table-column>
              <el-table-column prop="relationCase.resultName" label="审理结果"> </el-table-column>
              <el-table-column prop="relationCase.hostUser.name" label="主办人员"> </el-table-column>
              <el-table-column label="操作">
                <template slot-scope="scope">
                  <span class="cursor red" @click="handleCancelRelated(scope)">取消关联</span>
                </template>
              </el-table-column>
            </el-table>
          </div>
          <div v-else>
            <NoData text="暂无关联"/>
          </div>
        </div>
      </el-card>
      <div class="bottom-right">
        <el-card class="right-item">
          <div class="flex">
            <i class="el-icon-s-custom icon"></i>
            <span>主协办管理</span>
          </div>
          <div class="content-link-wai flex align-center mg10">
            <div class="man-type man-orange">主办</div>
            <div class="man-name" v-if="lawCase.hostUser">{{lawCase.hostUser.name}}</div>
          </div>
        </el-card>
        <el-card class="right-item" style="margin-top: 20px">
          <div class="flex">
            <i class="el-icon-s-custom icon"></i>
            <span>共享成员管理</span>
          </div>
          <div class="content-link-wai flex align-center cursor mg10" @click="handleShowShareModal">
            <i class="el-icon-circle-plus-outline" style="font-size: 40px;color: #dfdfdf;"></i>
          </div>
          <div>
            <div v-for="item in shareList" class="content-link-wai flex align-center mg10">
             <div class="man-type man-orange">成员</div>
              <div class="man-name">{{ item.user.name }}</div>
            </div>
          </div>

        </el-card>
      </div>
    </div>
    <EditCase
      ref="editForm"
      :visible="editVisible"
      :model="editModalMdl"
      :allCustomerList="allCustomerList"
      @cancel="handleEditCancel"
      @ok="handleEditOk"
    />
     <CreateClient
      ref="clientForm"
      :visible="clientVisible"
      :model="clientModalMdl"
      :type="clientVisibleType"
      @cancel="handleClientCancel"
      @ok="handleClientOk"
    />
    <CreateStrategy
      ref="strategyForm"
      :visible="strategyVisible"
      :model="strategyModalMdl"
      @cancel="handleStrategyCancel"
      @ok="handleStrategyOk"
    />
    <RelatedCase
      ref="relatedForm"
      :visible="relatedVisible"
      :model="relatedModalMdl"
      @cancel="handleRelatedCancel"
      @ok="handleRelatedOk"
    />
    <CreateShare
      ref="ShareForm"
      :visible="ShareVisible"
      @cancel="handleShareCancel"
      @refresh="getUserList"
    />
  </div>
</template>

<script>
import { mapMutations, mapState } from "vuex";

import EditCase from "../my/modal/EditCase.vue";
import CreateStrategy from "./modal/CreateStrategy.vue";
import RelatedCase from "./modal/RelatedCase";
import CreateShare from "./modal/CreateShare";
import CreateClient from "../privy/modal/CreateClient.vue";
import NoData from "@/components/NoData";
import { detailInfo, update } from "@/api/case/manage";
import { allCustomerData } from "@/api/customer/customer";
import { getUserList } from "@/api/case/caseUser";
import {
  getPersonList,
  savePerson,
  delPerson,
  queryPerspnById,
} from "@/api/case/concernPerson";
import {
  getStrategyList,
  saveStrategy,
  delStrategy,
  queryStrategyById,
} from "@/api/case/strategy";
import { getRelateList, delRelate } from "@/api/case/caseRelation";
import storage from "store";
const dictList = storage.get("dictList");
const charge_modeList = dictList.charge_mode;
const caseTypeList = dictList.case_type;
const trial_resultList = dictList.trial_result;
const settle_case_statusList = dictList.settle_case_status;
export default {
  name: "CaseInfo",
  components: {
    EditCase,
    CreateClient,
    NoData,
    CreateStrategy,
    RelatedCase,
    CreateShare,
  },
  data() {
    return {
      editVisible: false,
      editModalMdl: null,
      clientVisible: false,
      clientModalMdl: null,
      strategyVisible: false,
      strategyModalMdl: null,
      relatedVisible: false,
      relatedModalMdl: null,
      tabList: ["当事人", "办案策略", "关联案件"],
      tabActive: 0,
      tableData: [],
      clientVisibleType: "",
      strategyList: [],
      relatedTableData: [],
      lawCase: {
        archiveUser: "",
        caseProgram: "",
      },
      allCustomerList: [],
	        user: storage.get("user"),
      caseList: [],
      ShareVisible: false,
      shareList: [],
    };
  },
  mounted() {
    this.id = this.$route.query.id;
    this.getCaseInfo();
    this.getConcernPerson();
    this.getAllCustomerList();
    this.getUserList();
  },
  computed: mapState({
    caseInfo: (state) => state.caseInfo.caseInfo,
  }),
  methods: {
    ...mapMutations(["SET_CASE_INFO"]),
    // 获取客户列表
    getAllCustomerList() {
      allCustomerData().then((res) => {
        this.allCustomerList = res.data;
      });
    },
    // 共享成员列表
    getUserList() {
      let formData = new FormData();
      formData.append("lawCase.id", this.id);
      getUserList(formData).then((res) => {
        this.shareList = res.page.list;
      });
    },
    // 获取案件信息
    getCaseInfo() {
      let formData = new FormData();
      formData.append("id", this.id);
      detailInfo(formData).then((res) => {
        this.editModalMdl = res.data;
        this.lawCase = res.data.lawCase;
        this.SET_CASE_INFO(this.lawCase);
        charge_modeList.map((item) => {
          if (this.lawCase.chargeMode === item.value) {
            this.lawCase.chargeModeName = item.label;
          }
        });
        settle_case_statusList.map((item) => {
          if (this.lawCase.settleCaseStatus === item.value) {
            this.lawCase.settleCaseStatusName = item.label;
          }
        });
        trial_resultList.map((item) => {
          if (this.lawCase.trialResult === item.value) {
            this.lawCase.trialResultName = item.label;
          }
        });
        caseTypeList.map((item) => {
          if (this.lawCase.type === item.value) {
            this.lawCase.typeName = item.label;
          }
        });
      });
    },
    //获取当事人列表
    getConcernPerson() {
      let formData = new FormData();
      formData.append("lawCase.id", this.id);
      getPersonList(formData).then((res) => {
        this.tableData = res.page.list;
      });
    },
    //获取案件策略列表
    getStrategyData() {
      let formData = new FormData();
      formData.append("lawCase.id", this.id);
      getStrategyList(formData).then((res) => {
        this.strategyList = res.page.list;
      });
    },
    //获取关联列表
    getRelateList() {
      let formData = new FormData();
      formData.append("lawCase.id", this.id);
      getRelateList(formData).then((res) => {
        this.relatedTableData = res.page.list;
        this.relatedTableData.map((item) => {
          trial_resultList.map((child) => {
            if (item.relationCase.trialResult === child.value) {
              item.relationCase.resultName = child.label;
            }
          });
        });
      });
    },

    changeTab(index) {
      this.tabActive = index;
      if (index === 0) {
        this.getConcernPerson();
      } else if (index === 1) {
        this.getStrategyData();
      } else {
        this.getRelateList();
      }
    },
    // 显示修改信息弹框
    handleShowEditModal() {
		if(this.user.id=='15fd4f2685e94cb3bbdd5d4642dcc00f'){
			 return this.$message.error("体验账号暂无权限");
		}
      // this.editModalMdl = this.lawCase
      this.editVisible = true;
    },
    // 确认修改信息
    handleEditOk() {
		if(this.user.id=='15fd4f2685e94cb3bbdd5d4642dcc00f'){
			 return this.$message.error("体验账号暂无权限");
		}
      this.$refs.editForm.$refs.ruleForm.validate((valid) => {
        if (valid) {
          const data = this.$refs.editForm.ruleForm.lawCase;
          const param = {
            id: data.id,
            type: data.type, // 案件类型
            caseProgram: data.caseProgram, // 案件程序
            name: data.name, // 案件名称
            caseCause: data.caseCause, // 案由
            number: data.number, // 案号
            entrustDate: data.entrustDate || "", // 委托时间
            subjectMatter: data.subjectMatter, // 案件标的
            hostUser: data.hostUser,
            recordDate: data.recordDate || "", // 立案日期
            trialResult: data.trialResult, // 审理结果
            rulingDate: data.rulingDate || "", // 裁决日期
            settleCaseStatus: data.settleCaseStatus, // 结案状态
            settleCaseDate: data.settleCaseDate || "", // 结案日期
            archiveUser: data.archiveUser,
            archiveDate: data.archiveDate || "", // 归档日期
            custodyPlace: data.custodyPlace, // 档案保管地
            chargeMode: data.chargeMode, // 收费方式
            contractMoney: data.contractMoney, // 合同金额
            chargeRemarks: data.chargeRemarks, // 收费备注
            remarks: data.remarks, // 收费备注
            actualBackMoney: data.actualBackMoney, // 实际回款
            winMoney: data.winMoney || "", // 胜诉金额
            acceptUnitArea: data.acceptUnitArea, // 受理单位地区 省-市-县
            acceptUnitType: data.acceptUnitType, // 单位类型
            acceptUnitName: data.acceptUnitName, // 单位名称
          };
          update(param).then((res) => {
            if (res.success) {
              this.editVisible = false;
              this.$message({
                showClose: true,
                message: res.msg,
                type: "success",
              });
              this.getCaseInfo();
            } else {
              this.$message.error(res.msg);
            }
          });
        } else {
          return;
        }
      });
    },
    // 关闭修改信息弹框
    handleEditCancel() {
      this.editVisible = false;
    },
    // 显示新增当事人弹框
    handleShowClientModal(type, scope) {
		if(this.user.id=='15fd4f2685e94cb3bbdd5d4642dcc00f'){
			 return this.$message.error("体验账号暂无权限");
		}
      this.clientVisibleType = type;
      if (type === "add") {
        this.clientModalMdl = null;
      } else {
        this.clientModalMdl = scope.row;
      }
      this.clientVisible = true;
    },
    // 确认新增当事人
    handleClientOk() {
		if(this.user.id=='15fd4f2685e94cb3bbdd5d4642dcc00f'){
			 return this.$message.error("体验账号暂无权限");
		}
      this.$refs.clientForm.$refs.ruleForm.validate((valid) => {
        if (valid) {
          let data = this.$refs.clientForm.ruleForm;
          const {
            type,
            isEntrust,
            name,
            attribute,
            nation,
            sex,
            phone,
            idNumber,
            address,
            legalRepresentative,
            unifiedSocialCreditCode,
          } = data;
          this.clientVisible = false;
          const param = {
            lawCase: {
              // 案件信息
              id: this.id,
            },
            id: data.id ? data.id : "",
            type, // 类型（个人、单位）
            isEntrust, // 委托方（是/否）
            name, // 姓名/单位名称
            attribute, // 属性
            nation, // 民族
            sex, // 性别
            phone, // 联系方式
            idNumber, // 证件号码
            address, // 住所地/单位地址
            legalRepresentative, // 法定代表人
            unifiedSocialCreditCode, // 统一社会信用代码
          };
          savePerson(param).then((res) => {
            if (res.success) {
              this.$message({
                showClose: true,
                message: res.msg,
                type: "success",
              });
              this.getConcernPerson();
            } else {
              this.$message.error(res.msg);
            }
          });
        } else {
          return false;
        }
      });
    },
    // 关闭新增当事人弹框
    handleClientCancel() {
      this.clientVisible = false;
    },
    // 显示新增策略弹框
    handleShowStrategyModal(type, item) {
		if(this.user.id=='15fd4f2685e94cb3bbdd5d4642dcc00f'){
			 return this.$message.error("体验账号暂无权限");
		}
      if (type === "edit") {
        this.strategyModalMdl = item;
      } else {
        this.strategyModalMdl = null;
      }
      this.strategyVisible = true;
    },
    // 确认新增策略
    handleStrategyOk() {
		if(this.user.id=='15fd4f2685e94cb3bbdd5d4642dcc00f'){
			 return this.$message.error("体验账号暂无权限");
		}
      this.$refs.strategyForm.$refs.ruleForm.validate((valid) => {
        if (valid) {
          let data = this.$refs.strategyForm.ruleForm;
          const { question, result, solution } = data;
          const param = {
            lawCase: {
              // 案件信息
              id: this.id,
            },
            id: data.id ? data.id : "",
            question,
            result,
            solution,
          };
          saveStrategy(param).then((res) => {
            if (res.success) {
              this.strategyVisible = false;
              this.$message({
                showClose: true,
                message: res.msg,
                type: "success",
              });
              this.getStrategyData();
            } else {
              this.$message.error(res.msg);
            }
          });
        } else {
          return;
        }
      });
    },
    // 关闭新增策略弹框
    handleStrategyCancel() {
      this.strategyVisible = false;
    },
    // 显示关联案件弹框
    handleShowRelatedModal() {
		if(this.user.id=='15fd4f2685e94cb3bbdd5d4642dcc00f'){
			 return this.$message.error("体验账号暂无权限");
		}
      this.relatedVisible = true;
    },
    // 确认关联案件
    handleRelatedOk() {
      this.getRelateList();
    },
    // 关闭关联案件弹框
    handleRelatedCancel() {
      this.relatedVisible = false;
    },
    //删除该当事人
    handleDeleteClient(scope) {
		if(this.user.id=='15fd4f2685e94cb3bbdd5d4642dcc00f'){
			 return this.$message.error("体验账号暂无权限");
		}
      this.$confirm("此操作将永久删除该当事人, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          let formData = new FormData();
          formData.append("ids", scope.row.id);
          delPerson(formData).then((res) => {
            if (res.success) {
              this.getConcernPerson();
              this.$message({
                type: "success",
                message: "删除成功!",
              });
            }
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    // 删除策略
    handleDeleteStrategy(item) {
      this.$confirm("此操作将永久删除该办案策略, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          let formData = new FormData();
          formData.append("ids", item.id);
          delStrategy(formData).then((res) => {
            if (res.success) {
              this.getStrategyData();
              this.$message({
                type: "success",
                message: "删除成功!",
              });
            }
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    //取消关联
    handleCancelRelated(scope) {
      this.$confirm("确认取消关联该案件?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          let formData = new FormData();
          formData.append("ids", scope.row.id);
          delRelate(formData).then((res) => {
            if (res.success) {
              this.$message({
                type: "success",
                message: "取消关联成功!",
              });
              this.getRelateList();
            } else {
              this.$message({
                type: "error",
                message: res.msg,
              });
            }
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    handleShowShareModal() {
      this.ShareVisible = true;
    },
    handleShareCancel() {
      this.ShareVisible = false;
    },
    handleShareOk() {
      this.ShareVisible = false;
    },
  },
};
</script>

<style lang="less" scoped>
.detail-info {
  height: calc(100vh - 165px);
  margin-top: 20px;
  .info-box {
    width: 100%;
    height: 280px;
    .new-info-item {
      font-size: 14px;
      .item-title {
        color: #858896;
      }
      .item-value {
        color: #303443;
        padding-right: 5px;
        flex: 1;
      }
    }
  }
  .detail-bottom {
    margin-top: 20px;
    display: flex;
    margin-bottom: 20px;
    height: calc(100% - 300px);
    .bottom-left {
      width: calc(100% - 400px);
      background: #fff;
      margin-right: 20px;
      border-radius: 6px;
      .common-detail-tab {
        display: flex;
        align-items: center;
        height: 56px;
        border-bottom: 1px solid #e1e4ee;
        .tab-item-wrap {
          flex: 1;
          display: flex;
          margin-left: 10px;
          .tab-active {
            color: #303443;
            font-weight: 700;
            border-bottom: 3px solid #303443;
          }
          .tab-item {
            width: 72px;
            text-align: center;
            font-size: 16px;
            color: #858896;
            height: 56px;
            line-height: 56px;
            margin-right: 40px;
            cursor: pointer;
          }
        }
        .tab-operate {
          font-size: 15px;
          cursor: pointer;
          margin-right: 20px;
        }
      }
    }
    .bottom-right {
      width: 420px;
      .right-item {
        height: calc(50% - 10px);
        min-height: 245px;
        background: #fff;
        border-radius: 6px;
        .icon {
          font-size: 22px;
          color: #9fa5b9;
        }
        .content-link-wai {
          padding: 0px 30px;
          .man-type {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            color: #fff;
            font-size: 12px;
            text-align: center;
            line-height: 36px;
            background: #f6c379;
          }
          .man-name {
            padding-left: 10px;
            font-size: 14px;
            color: #303443;
            padding-right: 30px;
          }
        }
      }
    }
  }
}
.common-item {
  .common-gray-title {
    height: 40px;
    background: #f7f7fc;
    display: flex;
    align-items: center;
    padding: 0 20px;
    .title-txt {
      font-size: 16px;
      color: #303443;
      flex: 1;
      display: flex;
    }
  }
  .new-strategy {
    display: flex;
    font-size: 14px;
    line-height: 22px;
    margin-top: 20px;
    margin-bottom: 20px;
    padding: 0 20px;
    .title {
      color: #858896;
    }
    .txt {
      flex: 1;
      color: #303443;
      white-space: pre-line;
    }
  }
}
</style>

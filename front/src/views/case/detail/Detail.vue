<template>
  <div>
    <div class="flex justify-between align-center">
      <div class="flex align-center">
        <i class="el-icon-arrow-left" @click="$router.back()"></i>
        <h3>{{ caseInfo.name }}</h3>
      </div>
      <div class="tab-list">
        <div
          v-for="(item, index) in tabList"
          :class="[
            'tab-item',
            index === actTab ? 'active-item' : 'normal-item',
          ]"
          :key="index"
          @click="changeTab(index)"
        >
          {{ item }}
        </div>
      </div>
    </div>
    <section v-if="actTab === 0"><CaseRecord :concernPersonList="concernPersonList" /></section>
    <section v-if="actTab === 1"><CaseInfo /></section>
    <section v-if="actTab === 2"><CaseUnit /></section>
    <section v-if="actTab === 3"><CaseDoc /></section>
    <section v-if="actTab === 4"><Finance /></section>
  </div>
</template>

<script>
import { mapState, mapMutations } from "vuex";
import CaseRecord from "./CaseRecord.vue";
import CaseInfo from "./CaseInfo.vue";
import CaseUnit from "./CaseUnit.vue";
import CaseDoc from "./CaseDoc.vue";
import storage from "store";
import Finance from '../finance/Finance.vue'
import { detailInfo } from "@/api/case/manage";

export default {
  name: "Detail",
  components: {
    CaseRecord,
    CaseInfo,
    CaseUnit,
    CaseDoc,
    Finance
  },
  data() {
    return {
      tabList: ["办案记录", "基本案情", "受理单位", "案件文档", "财务流水"],
      concernPersonList:[],
      actTab: 0
    };
  },
  computed: mapState({
    caseInfo: (state) => state.caseInfo.caseInfo,
  }),
  mounted() {
    this.id = this.$route.query.id;
    this.getCaseInfo();
  },
  methods: {
    ...mapMutations(['SET_CASE_INFO']),
    changeTab(index) {
      this.actTab = index;
    },
    getCaseInfo() {
      let formData = new FormData();
      formData.append("id", this.id);
      detailInfo(formData).then((res) => {
        this.editModalMdl = res.data;
        this.lawCase = res.data.lawCase;
        this.concernPersonList = res.data.concernPersonList;
        this.SET_CASE_INFO(this.lawCase)
      });
    },
  },
};
</script>

<style lang="less" scoped>
.el-icon-arrow-left {
  font-size: 22px;
  padding: 0 10px;
  cursor: pointer;
}
.tab-list {
  display: flex;
  .tab-item {
    height: 28px;
    line-height: 28px;
    margin: 0 10px;
    cursor: pointer;
  }
  .normal-item:hover {
    color: #409eff;
  }
  .active-item {
    background: #409eff;
    color: #fff;
    padding: 0 15px;
    border-radius: 15px;
  }
}
</style>
<template>
  <div>
    <div class="flex justify-between">
      <div class="flex">
        <el-button
          plain
          style="margin-left: 20px"
          icon="el-icon-arrow-left"
          @click="$router.back()"
          >返回我的案件</el-button
        >
      </div>
      <div class="flex align-center">
        <div class="search-input flex align-center">
          <el-input
            v-model="formLabelAlign.name"
            placeholder="请输入当事人"
            style="border: none"
            class="input"
          ></el-input>
          <i class="el-icon-search" @click="getConcernPerson"></i>
        </div>
        <el-popover placement="bottom" width="400" trigger="click">
          <el-form
            label-position="right"
            label-width="80px"
            :model="formLabelAlign"
          >
            <el-form-item label="当事人">
              <el-input v-model="formLabelAlign.name"></el-input>
            </el-form-item>
            <el-form-item label="委托方">
              <el-select
                style="width: 100%"
                v-model="formLabelAlign.isEntrust"
                placeholder="请选择"
              >
                <el-option
                  :label="item.label"
                  :value="item.value"
                  v-for="(item, index) in yes_no"
                  :key="index"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="案件名称">
              <el-input v-model="formLabelAlign.lawCaseName"></el-input>
            </el-form-item>

            <el-form-item label="类型">
              <el-select
                style="width: 100%"
                v-model="formLabelAlign.type"
                placeholder="请选择"
              >
                <el-option
                  :label="item.label"
                  :value="item.value"
                  v-for="(item, index) in customer_type"
                  :key="index"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button
                type="primary"
                @click="
                  currentPage = 1;
                  getConcernPerson();
                "
                >开始检索</el-button
              >
              <el-button plain style="margin-left: 30px" @click="reset"
                >重置信息</el-button
              >
            </el-form-item>
          </el-form>
          <div class="high-wrap" slot="reference">
            <i class="el-icon-c-scale-to-original" style="font-size: 20px"></i
            >高级
          </div>
        </el-popover>

        <div class="top-header-divide"></div>
        <div @click="handleExport" style="margin-left: 20px" class="cursor">
          <i class="el-icon-upload2"></i>
          <span>导出</span>
        </div>
      </div>
    </div>
    <div class="table-box">
      <el-table :data="tableData" style="width: 100%" v-loading="loading">
        <el-table-column type="expand">
          <template slot-scope="props">
            <el-form label-position="left" inline class="demo-table-expand">
              <el-row>
                <el-col :span="6">
                  <el-form-item label="民族:">
                    <span>{{ props.row.nation }}</span>
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="性别:">
                    <span>{{ props.row.sex === "1" ? "男" : "女" }}</span>
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="身份证号码:">
                    <span>{{ props.row.idNumber }}</span>
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="住所地:">
                    <span>{{ props.row.address }}</span>
                  </el-form-item>
                </el-col>
                <el-col :span="7">
                  <el-form-item label="备注:">
                    <span>{{ props.row.remark }}</span>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </template>
        </el-table-column>
        <el-table-column label="类型" prop="type">
          <template slot-scope="scope">
            <span>{{ scope.row.type === "1" ? "个人" : "单位" }}</span>
          </template>
        </el-table-column>
        <el-table-column label="名称" prop="name"> </el-table-column>
        <el-table-column label="委托方" prop="client">
          <template slot-scope="scope">
            <span>{{ scope.row.isEntrust === "1" ? "是" : "否" }}</span>
          </template>
        </el-table-column>
        <!-- <el-table-column label="属性" prop="attribute"> </el-table-column> -->
        <el-table-column label="联系方式" prop="phone"> </el-table-column>
        <el-table-column label="关联案件" prop="lawCaseName"> </el-table-column>
        <el-table-column label="审理程序" prop="caseProgram.name">
        </el-table-column>
        <el-table-column label="案由" prop="caseCause.name"> </el-table-column>
        <el-table-column label="承办律师" prop="hostUser.name"> </el-table-column>
        <el-table-column label="操作">
          <template slot-scope="scope">
            <div class="flex align-center">
              <span class="btn-text" @click="handleShowModal(scope.row)"
                >修改</span
              >
              <div class="top-header-divide"></div>
              <span class="btn-text" @click="handleDelete(scope.row.id)"
                >删除</span
              >
            </div>
          </template>
        </el-table-column>
      </el-table>
      <div class="block" style="margin-top: 15px">
        <el-pagination
          background
          align="center"
          @size-change="handleSizeChange"
          @current-change="handleCurrentSizeChange"
          :current-page="currentPage"
          :page-sizes="[1, 5, 10, 20]"
          :page-size="pageSize"
          layout="prev, pager, next"
          :total="total"
        >
        </el-pagination>
      </div>
    </div>
    <CreateClient
      ref="clientForm"
      :visible="editVisible"
      :model="clientMdl"
      type="edit"
      @cancel="handleCancel"
      @ok="handleOk"
    />
  </div>
</template>

<script>
import CreateClient from "./modal/CreateClient.vue";
import storage from "store";
import formatParams from "@/utils/formatRestfulParams";
import {
  getAllConcernPerson,
  savePerson,
  delPerson,
  queryPerspnById,
} from "@/api/case/concernPerson";
export default {
  name: "Privy",
  components: {
    CreateClient,
  },
  data() {
    const dictList = storage.get("dictList");

    return {
      input: "",
      customer_type: dictList.customer_type,
      yes_no: dictList.yes_no,

      formLabelAlign: {
        name: "",
        lawCaseName: "",
        type: "",
        isEntrust: "",
      },
      isTable: true,
      tableData: [],
      editVisible: false,
      clientMdl: null,
      loading: false,
      currentPage: 1, // 当前页码
      pageSize: 10, // 每页的数据条数
      total: 0,
    };
  },
  mounted() {
    this.id = this.$route.query.id;
    this.getConcernPerson();
  },
  methods: {
    //获取当事人列表
    getConcernPerson() {
      this.loading = true;
      let formData = new FormData();
      formData.append("pageNo", this.currentPage);
      formData.append("pageSize", this.pageSize);
      formData.append("name", this.formLabelAlign.name);
      formData.append("lawCaseName", this.formLabelAlign.lawCaseName);
      formData.append("type", this.formLabelAlign.type);
      formData.append("isEntrust", this.formLabelAlign.isEntrust);

      getAllConcernPerson(formData).then((res) => {
        this.loading = false;
        this.total = res.page.count;
        this.tableData = res.page.list;
      });
    },
    reset() {
      this.formLabelAlign = {
        name: "",
        lawCaseName: "",
        type: "",
        isEntrust: "",
      };
      this.getConcernPerson();
    },
    //每页条数改变时触发 选择一页显示多少行
    handleSizeChange(val) {
      this.currentPage = 1;
      this.pageSize = val;
    },
    //当前页改变时触发 跳转其他页
    handleCurrentSizeChange(val) {
      this.currentPage = val;
      this.getConcernPerson();
    },
    handleShowModal(mdl) {
      this.editVisible = true;
      this.clientMdl = mdl;
    },
    // 导出
    handleExport() {
      const param = {
        pageNo: this.currentPage,
        pageSize: this.pageSize,
        name: this.formLabelAlign.name,
        lawCaseName: this.formLabelAlign.lawCaseName,
        type: this.formLabelAlign.type,
        isEntrust: this.formLabelAlign.isEntrust,
      };
      formatParams("/law/case/caseConcernPerson/export", param, "当事人库");
    },
    handleCancel() {
      this.editVisible = false;
    },

    // 编辑当事人
    handleOk() {
      this.$refs.clientForm.$refs.ruleForm.validate((valid) => {
        if (valid) {
          let data = this.$refs.clientForm.ruleForm;
          const {
            type,
            isEntrust,
            name,
            attribute,
            nation,
            sex,
            phone,
            idNumber,
            address,
            legalRepresentative,
            unifiedSocialCreditCode,
            lawCaseId,
          } = data;
          const param = {
            lawCase: {
              // 案件信息
              id: lawCaseId,
            },
            id: data.id || "",
            type, // 类型（个人、单位）
            isEntrust, // 委托方（是/否）
            name, // 姓名/单位名称
            attribute, // 属性
            nation, // 民族
            sex, // 性别
            phone, // 联系方式
            idNumber, // 证件号码
            address, // 住所地/单位地址
            legalRepresentative, // 法定代表人
            unifiedSocialCreditCode, // 统一社会信用代码
          };
          savePerson(param).then((res) => {
            this.editVisible = false;
            if (res.success) {
              this.$message({
                showClose: true,
                message: res.msg,
                type: "success",
              });
              this.getConcernPerson();
            } else {
              this.$message.error(res.msg);
            }
          });
        } else {
          return;
        }
      });
    },
    handleDelete(id) {
      this.$confirm("此操作将永久删除该当事人, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          let formData = new FormData();
          formData.append("ids", id);
          delPerson(formData).then((res) => {
            if (res.success) {
              this.getConcernPerson();
              this.$message({
                type: "success",
                message: "删除成功!",
              });
            }
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
  },
};
</script>

<style lang="less" scoped>
.search-input {
  border-bottom: 1px solid #dcdfe6;
}
.input {
  /deep/.el-input__inner {
    border: none;
    background: none;
  }
}
.high-wrap {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin-left: 10px;
  margin-right: 20px;
  height: 34px;
  cursor: pointer;
}
.top-header-divide {
  width: 1px;
  height: 20px;
  background: #c1c4d3;
  margin: 0 10px;
}
/deep/.el-date-editor {
  width: 100%;
}
.table-box {
  background: #fff;
  margin-top: 30px;
  .footer-box {
    padding: 20px;
  }
}
.btn-text {
  color: #888;
  cursor: pointer;
}
</style>
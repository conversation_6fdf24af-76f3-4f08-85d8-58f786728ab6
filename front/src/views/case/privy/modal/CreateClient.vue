<template>
  <el-dialog
    title="编辑当事人 "
    :visible="visible"
    width="60%"
    :before-close="handleCancel"
    :close-on-press-escape="false"
    :close-on-click-modal="false"
  >
    <el-form
      :model="ruleForm"
      :rules="rules"
      ref="ruleForm"
      label-width="130px"
      class="ruleForm-box"
    >
      <el-row>
        <el-col :span="12">
          <el-form-item label="类型" prop="type">
            <el-radio-group v-model="ruleForm.type">
              <el-radio
                :label="item.value"
                v-for="(item, index) in customer_type"
                :key="index"
                >{{ item.label }}</el-radio
              >
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="委托方" prop="isEntrust">
            <el-radio-group v-model="ruleForm.isEntrust">
              <el-radio
                :label="item.value"
                v-for="(item, index) in yes_no"
                :key="index"
                >{{ item.label }}</el-radio
              >
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item
            :label="ruleForm.type === '1' ? '姓名' : '单位姓名'"
            prop="name"
          >
            <el-input type="text" v-model="ruleForm.name"></el-input>
          </el-form-item>
        </el-col>
<!--        <el-col :span="12">-->
<!--          <el-form-item label="属性" prop="attribute">-->
<!--            &lt;!&ndash; <el-select v-model="ruleForm.attribute" placeholder="请选择属性">-->
<!--              <el-option label="区域一" value="shanghai"></el-option>-->
<!--              <el-option label="区域二" value="beijing"></el-option>-->
<!--            </el-select> &ndash;&gt;-->
<!--            <el-input type="text" v-model="ruleForm.attribute"></el-input>-->
<!--          </el-form-item>-->
<!--        </el-col>-->
      </el-row>
      <div v-if="ruleForm.type === '1'">
        <el-row>
          <el-col :span="12">
            <el-form-item label="民族" prop="nation">
              <el-input type="text" v-model="ruleForm.nation"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="性别" prop="sex">
              <el-radio-group v-model="ruleForm.sex">
                <el-radio
                  :label="item.value"
                  v-for="(item, index) in sexList"
                  :key="index"
                  >{{ item.label }}</el-radio
                >
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="联系方式" prop="phone">
              <el-input type="text" v-model="ruleForm.phone"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="证件号码" prop="idNumber">
              <el-input type="text" v-model="ruleForm.idNumber"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="住所地" prop="address">
              <el-input type="text" v-model="ruleForm.address"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </div>
      <div v-else>
        <el-row>
          <el-col :span="24">
            <el-form-item label="单位地址" prop="address">
              <el-input type="text" v-model="ruleForm.address"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="法定代表人" prop="legalRepresentative">
              <el-input
                type="text"
                v-model="ruleForm.legalRepresentative"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="联系方式" prop="phone">
              <el-input type="text" v-model="ruleForm.phone"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item
              label="统一社会信用代码"
              prop="unifiedSocialCreditCode"
            >
              <el-input
                type="text"
                v-model="ruleForm.unifiedSocialCreditCode"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </div>
      <el-row>
        <el-col :span="24">
          <el-form-item label="备注" prop="remarks">
            <el-input v-model="ruleForm.remarks" type="textarea" :rows="2">
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleCancel('ruleForm')">取 消</el-button>
      <el-button type="primary" @click="handleOk('ruleForm')"
        >立即提交</el-button
      >
    </span>
  </el-dialog>
</template>

<script>
import storage from "store";

export default {
  name: "index",
  props: {
    visible: {
      type: Boolean,
      required: true,
    },
    loading: {
      type: Boolean,
      default: () => false,
    },
    model: {
      type: Object,
      default: () => null,
    },
    type: {
      type: String,
      required: true,
    },
  },
  data() {
    const dictList = storage.get("dictList");

    return {
      sexList: dictList.sex,
      customer_type: dictList.customer_type,
      yes_no: dictList.yes_no,
      ruleForm: {
        name: "",
        type: "1",
        isEntrust: "1",
        attribute: "",
        nation: "",
        sex: "1",
        idNumber: "",
        phone: "",
        address: "",
        remarks: "",
        legalRepresentative: "",
        unifiedSocialCreditCode: "",
      },
      rules: {
        name: [
          { required: true, message: "请输入姓名", trigger: "blur" },
        ],
      },
    };
  },
  mounted() {
    // 当 model 发生改变时，为表单设置值
    this.$watch(
      "model",
      (e) => {
        if (e) {
          this.ruleForm = JSON.parse(JSON.stringify(this.model));
        }
      },
      {
        deep: true,
        immediate: true,
      }
    );
  },
  methods: {
    handleOk() {
      this.$emit("ok", this.ruleForm);
    },
    handleCancel() {
      this.$emit("cancel");
    },
  },
};
</script>

<style>
</style>

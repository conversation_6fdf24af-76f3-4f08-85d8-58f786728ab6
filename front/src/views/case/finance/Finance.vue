<template>
  <div class="block" style="margin-top: 20px">
    <h3 class="title">案件流水</h3>
    <div class="title">案件名称：{{ caseName }}</div>
    <el-row>
      <el-col :span="4">
        <div class="">总收入</div>
        <div class="tit-txt">￥{{info.receiveMoney || 0}}</div>
      </el-col>
      <el-col :span="4">
        <div class="">总支出</div>
        <div class="tit-txt" >￥{{info.expenditureMoney || 0}}</div>
      </el-col>
    </el-row>
    <el-divider></el-divider>
    <!-- <h3 class="title">财务管控</h3>
    <el-dropdown trigger="click" @command="handleCommand">
      <el-button type="primary" icon="el-icon-caret-bottom"> 新建 </el-button>
      <el-dropdown-menu slot="dropdown">
        <el-dropdown-item command="a">新增收款</el-dropdown-item>
        <el-dropdown-item command="b">新增支出</el-dropdown-item>
      </el-dropdown-menu>
    </el-dropdown>

    <el-divider></el-divider> -->
    <el-table
      v-loading="loading"
      ref="singleTable"
      :data="tableData"
      style="width: 100%"
    >
      <el-table-column property="type" label="收支类型">
        <template slot-scope="scope">
          <span>{{ scope.row.type === "1" ? "收入" : "支出" }}</span>
        </template>
      </el-table-column>
      <el-table-column property="name" label="费用名称"></el-table-column>
      <el-table-column property="money" label="金额"></el-table-column>
      <el-table-column property="happenDate" label="发生日期"></el-table-column>
      <el-table-column
        property="happenUser.name"
        label="发生人"
      ></el-table-column>
      <el-table-column property="lawCase.name" label="关联"></el-table-column>
      <!-- <el-table-column label="操作" width="100">
        <template slot-scope="scope">
          <el-button @click="append(scope.row)" type="text" size="small"
            >编辑</el-button
          >
          <el-button type="text" size="small" @click="remove(scope.row)"
            >删除</el-button
          >
        </template>
      </el-table-column> -->
    </el-table>
    <div class="block" style="margin-top: 15px">
      <el-pagination
        background
        align="center"
        @size-change="handleSizeChange"
        @current-change="handleCurrentSizeChange"
        :current-page="currentPage"
        :page-sizes="[1, 5, 10, 20]"
        :page-size="pageSize"
        layout="prev, pager, next"
        :total="total"
      >
      </el-pagination>
    </div>
    <!-- <CreateIncomeModal
      ref="createIncomeForm"
      :visible="incomeVisible"
      :model="model"
      @cancel="handleIncomeCancel"
      @ok="handleIncomeOk"
    />
    <CreateExpensesModal
      ref="createExpensesForm"
      :visible="ExpensesVisible"
      :model="model"
      @cancel="handleExpensesCancel"
      @ok="handleExpensesOk"
    /> -->
  </div>
</template>

<script>
import {
  getList,
  save,
  del,
  edit,
  listByCase,
  queryCaseFinance,
} from "@/api/case/cost";
// import CreateIncomeModal from "./modal/CreateIncomeModal.vue";
// import CreateExpensesModal from "./modal/CreateExpensesModal.vue";
import storage from "store";
import moment from "moment";
export default {
  name: "version",
  components: {
    // CreateIncomeModal,
    // CreateExpensesModal,
  },
  data() {
    return {
      tableData: [],
      total: 0,
      currentPage: 1, // 当前页码
      pageSize: 10, // 每页的数据条数
      incomeVisible: false,
      model: null,
      ExpensesVisible: false,
      caseName: "",
      info: {}
    };
  },
  created() {
    this.getList();
    this.caseName = this.$route.query.name;
    this.getInfo();
  },
  methods: {
    getInfo() {
      let formData = new FormData();
      formData.append("caseId", this.$route.query.id);
      queryCaseFinance(formData).then((res) => {
        this.info = res.data
      });
    },
    handleCommand(e) {
      if (e === "a") {
        this.incomeVisible = true;
        this.incomeType = "add";
        this.model = null;
      } else {
        this.ExpensesVisible = true;
        this.model = null;
      }
    },
    append(row) {
      this.model = row;
      if (row.type === "1") {
        this.incomeVisible = true;
        this.incomeType = "edit";
      } else {
        this.ExpensesVisible = true;
      }
    },
    getList() {
      this.loading = true;
      let formData = new FormData();
      formData.append("pageNo", this.currentPage);
      formData.append("pageSize", this.pageSize);
      formData.append("lawCase.id", this.$route.query.id);
      listByCase(formData).then((res) => {
        this.loading = false;
        this.tableData = res.page.list;
        this.total = res.page.count;
      });
    },
    //每页条数改变时触发 选择一页显示多少行
    handleSizeChange(val) {
      this.currentPage = 1;
      this.pageSize = val;
    },
    //当前页改变时触发 跳转其他页
    handleCurrentSizeChange(val) {
      this.currentPage = val;
      this.getCaseList();
    },
    handleIncomeCancel() {
      this.incomeVisible = false;
    },
    handleIncomeOk() {
      if (this.incomeType === "add") {
        this.$refs.createIncomeForm.$refs.ruleForm.validate((valid) => {
          if (valid) {
            let data = this.$refs.createIncomeForm.ruleForm;
            let params = {
              name: data.name,
              type: 1,
              receivableMoney: data.receivableMoney,
              flowRecordList: data.flowRecordList,
              id: data.id || "",
              lawCase: data.lawCase,
            };
            save(params).then((res) => {
              this.incomeVisible = false;
              if (res.success) {
                this.$message({
                  showClose: true,
                  message: res.msg,
                  type: "success",
                });
                this.getList();
              } else {
                this.$message.error(res.msg);
              }
            });
          } else {
            return false;
          }
        });
        this.$refs.createIncomeForm.$refs.ruleForm.validate((valid) => {
          if (valid) {
            let data = this.$refs.createIncomeForm.ruleForm;
            let params = {
              name: data.name,
              type: 1,
              receivableMoney: data.receivableMoney,
              flowRecordList: data.flowRecordList,
              id: data.id || "",
              lawCase: data.lawCase,
            };
            save(params).then((res) => {
              this.incomeVisible = false;
              if (res.success) {
                this.$message({
                  showClose: true,
                  message: res.msg,
                  type: "success",
                });
                this.getList();
              } else {
                this.$message.error(res.msg);
              }
            });
          } else {
            return false;
          }
        });
      } else {
        this.$refs.createIncomeForm.$refs.ruleForm.validate((valid) => {
          if (valid) {
            let data = this.$refs.createIncomeForm.ruleForm;
            let formData = new FormData();
            formData.append("id", data.id || "");
            formData.append("lawCase.id", data.lawCase.id);
            formData.append("lawCase.name", data.lawCase.name);
            formData.append("money", data.money);
            formData.append("happenDate", data.happenDate);
            formData.append("type", "1");
            formData.append("name", data.name);
            edit(formData).then((res) => {
              this.incomeVisible = false;
              if (res.success) {
                this.$message({
                  showClose: true,
                  message: res.msg,
                  type: "success",
                });
                this.getList();
              } else {
                this.$message.error(res.msg);
              }
            });
          } else {
            return false;
          }
        });
      }
    },
    remove(data) {
      this.$confirm("确定删除该记录?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          let param = new FormData();
          param.append("ids", data.id);
          del(param).then((res) => {
            if (res.success) {
              this.getList();
              this.$message({
                type: "success",
                message: "删除成功!",
              });
            } else {
              this.$message.error(res.msg);
            }
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    handleExpensesCancel() {
      this.ExpensesVisible = false;
    },
    handleExpensesOk() {
      this.$refs.createExpensesForm.$refs.ruleForm.validate((valid) => {
        if (valid) {
          let data = this.$refs.createExpensesForm.ruleForm;
          let formData = new FormData();
          formData.append("id", data.id || "");
          formData.append("lawCase.id", data.lawCase.id);
          formData.append("lawCase.name", data.lawCase.name);
          formData.append("money", data.money);
          formData.append("type", "2");
          formData.append("name", data.name);
          formData.append("happenDate", data.happenDate);
          edit(formData).then((res) => {
            this.ExpensesVisible = false;
            if (res.success) {
              this.$message({
                showClose: true,
                message: res.msg,
                type: "success",
              });
              this.getList();
            } else {
              this.$message.error(res.msg);
            }
          });
        } else {
          return false;
        }
      });
    },
  },
};
</script>

<style lang="less" scoped>
.block {
  padding: 20px;
  background: #fff;
  .title {
    padding: 20px 0;
  }
  /deep/.el-tree-node__content {
    padding: 5px 0;
  }
  .custom-tree-node {
    width: 50%;
    display: flex;
    //   justify-content: space-between;
    .label {
      margin-right: 30px;
    }
  }
}
.tit-txt{
  font-weight: bold;
  margin: 10px 0;
  color: red;
}
</style>
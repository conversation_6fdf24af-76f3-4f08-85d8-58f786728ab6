<template>
  <div>
    <div class="flex">
      <div class="left-box">
        <h3 style="margin: 10px 0">案件筛选</h3>
        <el-collapse accordion @change="handlecollapse">
          <el-collapse-item title="案件类型" name="1">
            <div
              v-for="(item, index) in caseTypeList"
              :key="item.id"
              :class="['cursor', active1 === index ? 'primary' : '']"
              @click="handleSelectCase('1', item, index)"
            >
              {{ item.label }}
            </div>
          </el-collapse-item>
          <el-collapse-item title="年度" name="2">
            <div
              v-for="(item, index) in yearList"
              :key="item.id"
              :class="['cursor', active2 === index ? 'primary' : '']"
              @click="handleSelectCase('2', item, index)"
            >
              {{ item }}
            </div>
          </el-collapse-item>
          <el-collapse-item title="办案律师" name="3">
            <div
              v-for="(item, index) in briefList"
              :key="item.id"
              :class="['cursor', active3 === index ? 'primary' : '']"
              @click="handleSelectCase('3', item, index)"
            >
              {{ item.name }}
            </div>
          </el-collapse-item>
        </el-collapse>
      </div>
      <div style="flex: 1">
        <div class="flex justify-between">
          <div class="flex">
            <el-button
              slot="reference"
              @click="handleShowCreateModal"
              type="primary"
            >
              新建案件</el-button
            >
            <el-dropdown trigger="click" @command="handleSelect($event)">
              <el-button plain style="margin-left: 20px"
                >{{
                  formLabelAlign.checkWord === "1"
                    ? "分给他人"
                    : formLabelAlign.checkWord === "2"
                    ? "他人分给"
                    : "共办案件"
                }}<i class="el-icon-arrow-down"></i
              ></el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item command="1">分给他人</el-dropdown-item>
                <el-dropdown-item command="2">他人分给</el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
            <el-button plain style="margin-left: 20px" @click="goPrivy">当事人库</el-button>
            <el-button plain style="margin-left: 20px" @click="setStatus(99)">未结案件</el-button>
            <el-button plain style="margin-left: 20px" @click="setStatus(2)">已结案件</el-button>
          </div>
          <div class="flex align-center">
            <div class="search-input flex align-center">
              <el-input
                v-model="formLabelAlign.queryConcernPersonName"
                placeholder="请输入当事人"
                style="border: none"
                class="input"
              ></el-input>
              <i class="el-icon-search" @click="getCaseList"></i>
            </div>
            <el-popover placement="bottom" width="400" trigger="click">
              <el-form
                label-position="right"
                label-width="80px"
                :model="formLabelAlign"
              >
                <el-form-item label="案件名称">
                  <el-input v-model="formLabelAlign.name"></el-input>
                </el-form-item>
                <el-form-item label="受理机关">
                  <el-input v-model="formLabelAlign.acceptUnitName"></el-input>
                </el-form-item>
                <el-form-item label="受理案号">
                  <el-input v-model="formLabelAlign.number"></el-input>
                </el-form-item>
                <el-form-item label="案件类型">
                  <el-select
                    style="width: 100%"
                    v-model="formLabelAlign.type"
                    placeholder="请选择案件类型"
                  >
                    <el-option
                      :label="item.label"
                      :value="item.value"
                      v-for="item in caseTypeList"
                      :key="item.id"
                    ></el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="审理程序">
                  <el-input
                    v-model="formLabelAlign.caseProgram.name"
                  ></el-input>
                </el-form-item>
                <el-form-item label="案件阶段">
                  <el-input v-model="formLabelAlign.queryStageName"></el-input>
                </el-form-item>
                <el-form-item label="案件年份">
                  <el-date-picker
                    v-model="formLabelAlign.queryYear"
                    type="year"
                    placeholder="选择年份"
                    value-format="yyyy"
                  >
                  </el-date-picker>
                </el-form-item>
                <el-form-item
                  label="办案律师"
                  v-if="
                    user.type.indexOf('2') > -1 || user.type.indexOf('1') > -1
                  "
                >
                  <el-select
                    v-model="formLabelAlign.hostUser.id"
                    placeholder="请选择办案律师"
                  >
                    <el-option
                      :label="item.name"
                      :value="item.id"
                      v-for="item in briefList"
                      :key="item.id"
                    ></el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="案由">
                  <el-cascader
                    v-model="formLabelAlign.caseReason"
                    :options="caseCausList"
                    :props="{ checkStrictly: true, value: 'id', label: 'name' }"
                    clearable
                    @change="handleCaseCause($event)"
                    ref="cause"
                  >
                  </el-cascader>
                </el-form-item>
                <el-form-item label="委案日期">
                  <el-date-picker
                    v-model="formLabelAlign.weian"
                    type="daterange"
                    align="right"
                    unlink-panels
                    range-separator="至"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    value-format="yyyy-MM-dd HH:mm:ss"
                  >
                  </el-date-picker>
                </el-form-item>
                <el-form-item label="结案时间">
                  <el-date-picker
                    v-model="formLabelAlign.jiean"
                    type="daterange"
                    align="right"
                    unlink-panels
                    range-separator="至"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    value-format="yyyy-MM-dd HH:mm:ss"
                  >
                  </el-date-picker>
                </el-form-item>
                <el-form-item>
                  <el-button
                    type="primary"
                    @click="
                      currentPage = 1;
                      getCaseList();
                    "
                    >开始检索</el-button
                  >
                  <el-button
                    plain
                    style="margin-left: 30px"
                    @click="
                      currentPage = 1;
                      reset();
                    "
                    >重置信息</el-button
                  >
                </el-form-item>
              </el-form>
              <div class="high-wrap" slot="reference">
                <i
                  class="el-icon-c-scale-to-original"
                  style="font-size: 20px"
                ></i
                >高级
              </div>
            </el-popover>

            <div class="top-header-divide"></div>
            <div @click="toggoleShow" style="margin-left: 20px">
              <i class="el-icon-s-operation" v-if="isTable"></i>
              <i class="el-icon-menu" v-else></i>
            </div>
          </div>
        </div>
        <div class="table-box">
          <el-table
            v-loading="loading"
            ref="singleTable"
            :data="tableData"
            :highlight-current-row="!isMultiple"
            @current-change="handleCurrentChange"
            :row-class-name="tableRowClassName"
            @selection-change="handleSelectionChange"
            style="width: 100%"
          >
            <el-table-column
              align="left"
              type="selection"
              width="55"
              v-if="isMultiple"
            >
            </el-table-column>
            <!-- <el-table-column align="left">
          <template slot="header" slot-scope="scope">
            <div style="text-align: center">
              <i
                class="el-icon-setting"
                style="font-size: 20px"
                @click="handleShowHeaderModal"
              ></i>
            </div>
          </template>
          <template slot-scope="scope">
            <div style="text-align: center" class="tag-item">
              <el-dropdown
                trigger="hover"
                @command="handleDegree($event, scope.$index)"
                placement="right"
              >
                <div
                  class="degree-dot red"
                  v-if="scope.row.degree === '1'"
                ></div>
                <div
                  class="degree-dot orange"
                  v-if="scope.row.degree === '2'"
                ></div>
                <div
                  class="degree-dot grey"
                  v-if="scope.row.degree === '3'"
                ></div>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item command="1">
                    <div class="flex align-center">
                      <div
                        class="degree-dot red"
                        style="margin-right: 20px"
                      ></div>
                      重要
                    </div>
                  </el-dropdown-item>
                  <el-dropdown-item command="2">
                    <div class="flex align-center">
                      <div
                        class="degree-dot orange"
                        style="margin-right: 20px"
                      ></div>
                      一般
                    </div>
                  </el-dropdown-item>
                  <el-dropdown-item command="3">
                    <div class="flex align-center">
                      <div
                        class="degree-dot grey"
                        style="margin-right: 20px"
                      ></div>
                      次要
                    </div>
                  </el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </div>
          </template>
        </el-table-column> -->
            <el-table-column property="lawCase.name" label="案件名称">
            </el-table-column>
            <el-table-column
              align="left"
              :formatter="typeFormat"
              label="类型"
              prop="lawCase.type"
              :filters="caseTypeList"
              :filter-method="filterTag"
            >
            </el-table-column>
            <el-table-column
              property="lawCase.caseProgram.name"
              label="审理程序"
            >
              <!-- <template slot-scope="scope">
            <span>123</span>
          </template> -->
            </el-table-column>
            <el-table-column property="lawCase.caseCause.name" label="案由">
            </el-table-column>
            <el-table-column
              property="lawCase.entrustPersonNames"
              label="委托人"
            >
            </el-table-column>
            <el-table-column
              property="lawCase.hostUser.name"
              label="承办律师"
            >
            </el-table-column>
            <el-table-column property="lawCase.stageName" label="案件阶段">
            </el-table-column>
            <el-table-column property="lawCase.stageName" label="审核状态">
              <template slot-scope="scope">
                <span v-if="scope.row.lawCase.auditStatus === '0'"
                  >审核拒绝</span
                >
                <span v-if="scope.row.lawCase.auditStatus === '1'">待审核</span>
                <span v-if="scope.row.lawCase.auditStatus === '2'"
                  >审核通过</span
                >
                <span v-if="scope.row.lawCase.auditStatus === '3'">待提交</span>
              </template>
            </el-table-column>
            <el-table-column property="todo" label="待办事项">
              <template slot-scope="scope">
                <div v-if="scope.row.todoInfoList.length === 1">
                  <span class="mr10">{{
                    scope.row.todoInfoList[0].startDate
                  }}</span>
                  <span>{{ scope.row.todoInfoList[0].title }}</span>
                </div>
                <div v-if="scope.row.todoInfoList.length > 1">
                  <el-tooltip placement="top">
                    <div slot="content">
                      <div
                        v-for="(item, index) in scope.row.todoInfoList"
                        :key="item.id"
                      >
                        <span class="mr10">{{ item.startDate }}</span>
                        <span>{{ item.name }}</span>
                      </div>
                    </div>
                    <span>{{ scope.row.todoInfoList.length }}条</span>
                  </el-tooltip>
                </div>
              </template>
            </el-table-column>
            <el-table-column property="lawCase.acceptUnitArea" label="受理单位">
            </el-table-column>
            <el-table-column label="操作" fixed="right">
              <template slot-scope="scope">
                <div @click.stop="">
                  <el-dropdown
                    trigger="click"
                    @command="handleCommand($event, scope)"
                  >
                    <i class="el-icon-more"></i>
                    <el-dropdown-menu slot="dropdown">
                      <el-dropdown-item
                        command="closing"
                        v-if="scope.row.lawCase.status !== '2'"
                        >结案</el-dropdown-item
                      >
                      <el-dropdown-item
                        disabled
                        v-if="scope.row.lawCase.status === '2'"
                        >已结案</el-dropdown-item
                      >
                      <el-dropdown-item command="file">下载归档</el-dropdown-item>
                      <el-dropdown-item command="edit"
                        >修改基本信息</el-dropdown-item
                      >
                      <!-- <el-dropdown-item command="team">移至团队</el-dropdown-item> -->
                      <!-- <el-dropdown-item command="copy">创建副本</el-dropdown-item> -->
                      <el-dropdown-item
                        command="delete"
                        v-if="
                          user.type.indexOf('2') > -1 ||
                          user.type.indexOf('1') > -1
                        "
                        >删除</el-dropdown-item
                      >
                    </el-dropdown-menu>
                  </el-dropdown>
                </div>
              </template>
            </el-table-column>
          </el-table>
          <div class="block" style="margin-top: 15px">
            <el-pagination
              background
              align="center"
              @size-change="handleSizeChange"
              @current-change="handleCurrentSizeChange"
              :current-page="currentPage"
              :page-sizes="[1, 5, 10, 20]"
              :page-size="pageSize"
              layout="prev, pager, next"
              :total="total"
            >
            </el-pagination>
          </div>
          <div class="footer-box">
            <el-switch v-model="isMultiple" @change="toggleMultiple">
            </el-switch>
            开启批量操作
            <!-- <el-button
          plain
          style="margin-left: 30px"
          :disabled="!isMultiple"
          @click="handleShowClosingModal"
          >结案</el-button
        > -->
            <el-button
              plain
              style="margin-left: 30px"
              :disabled="!isMultiple"
              @click="handleShowFileModal"
              >归档</el-button
            >
            <el-dropdown trigger="click" @command="handleCommand">
              <el-button
                plain
                icon="el-icon-arrow-down"
                style="margin-left: 30px"
                >更多批量操作</el-button
              >
              <el-dropdown-menu slot="dropdown">
                <!-- <el-dropdown-item :disabled="!isMultiple" command="edit"
              >修改案件基本信息</el-dropdown-item
            > -->
                <el-dropdown-item
                  :disabled="!isMultiple"
                  command="multipleDelete"
                  >删除</el-dropdown-item
                >
              </el-dropdown-menu>
            </el-dropdown>
          </div>
        </div>
      </div>
    </div>
    <CreateCase
      ref="createForm"
      :visible="createVisible"
      :model="createModalMdl"
      :allCaseList="allCaseList"
      :allCustomerList="allCustomerList"
      @cancel="handleCreateCancel('createForm')"
      @ok="handleCreateOk('createForm')"
    />
    <ClosingCase
      ref="closingForm"
      :visible="closingVisible"
      :model="closingModalMdl"
      @cancel="handleClosingCancel"
      @ok="handleClosingOk"
    />
    <FileCase
      ref="fileForm"
      :visible="fileVisible"
      :model="fileModalMdl"
      @cancel="handleFileCancel"
      @ok="handleFileOk"
    />
    <CopyCase
      ref="copyForm"
      :visible="copyVisible"
      :model="copyModalMdl"
      @cancel="handleCopyCancel"
      @ok="handleCopyOk"
    />
    <EditCase
      ref="editForm"
      :allCustomerList="allCustomerList"
      :visible="editVisible"
      :model="editModalMdl"
      @cancel="handleEditCancel"
      @ok="handleEditOk"
    />
    <CustomHeader
      ref="headerForm"
      :visible="headerVisible"
      :model="headerModalMdl"
      @cancel="handleHeaderCancel"
      @ok="handleHeaderOk"
    />
  </div>
</template>

<script>
import formatParams from "@/utils/formatRestfulParams";
import CreateCase from "./modal/CreateCase.vue";
import ClosingCase from "./modal/ClosingCase.vue";
import FileCase from "./modal/FileCase.vue";
import CopyCase from "./modal/CopyCase.vue";
import EditCase from "./modal/EditCase.vue";
import CustomHeader from "./modal/CustomHeader.vue";
import storage from "store";
const dictList = storage.get("dictList");
import { allCustomerData } from "@/api/customer/customer";
import { allBriefList } from "@/api/login";
import {
  getList,
  add,
  del,
  update,
  queryById,
  detailInfo,
  archiveDownload,
  settle,
  allCaseData,
  caseCauseData,
} from "@/api/case/manage";
let m = 0;
export default {
  name: "My",
  components: {
    CreateCase,
    ClosingCase,
    FileCase,
    CopyCase,
    EditCase,
    CustomHeader,
  },
  data() {
    dictList.case_type.map((item) => {
      item.text = item.label;
    });
    console.log(dictList.case_type);
    return {
      input: "",
      formLabelAlign: {
        status:'99',
        name: "",
        queryConcernPersonName: "",
        type: "",
        number: "",
        acceptUnitName: "",
        queryStageName: "",
        caseCause: {
          id: "",
          name: "",
        },
        caseReason: [],
        caseProgram: {
          name: "",
        },
        weian: [],
        jiean: [],
        queryYear: "",
        hostUser: {
          id: "",
        },
        checkWord: "",
      },
      isTable: true,
      tableData: [],
      total: 0,
      isMultiple: false,
      createModalMdl: null,
      createVisible: false,
      closingModalMdl: null,
      closingVisible: false,
      fileModalMdl: null,
      fileVisible: false,
      copyModalMdl: null,
      copyVisible: false,
      editModalMdl: null,
      editVisible: false,
      headerModalMdl: null,
      headerVisible: false,
      currentPage: 1, // 当前页码
      pageSize: 10, // 每页的数据条数
      caseTypeList: dictList.case_type,
      loading: false,
      allCustomerList: [],
      allCaseList: [],
      user: storage.get("user"),
      caseCausList: [],
      selectCaseList: [],
      briefList: [],
      yearList: ["2019", "2020", "2021", "2022", "2023", "2024"],
      active1: null,
      active2: null,
      active3: null,
    };
  },
  mounted() {
    this.getCaseList();
    this.getAllCaseList();
    this.getAllCustomerList();
    this.getcaseCause();
    this.getAllBriefList();
  },
  methods: {
    handlecollapse(e) {
      if (e === "1") {
        this.active2 = null;
        this.active3 = null;
      } else if (e === "2") {
        this.active1 = null;
        this.active3 = null;
      } else if (e === "3") {
        this.active1 = null;
        this.active2 = null;
      } else {
        this.active1 = null;
        this.active2 = null;
        this.active3 = null;
      }
      this.formLabelAlign.type = "";
      this.formLabelAlign.hostUser.id = "";
      this.formLabelAlign.queryYear = "";
      this.getCaseList();
    },
    handleSelectCase(type, item, index) {
      if (type === "1") {
        this.active1 = index;
        this.formLabelAlign.type = item.value;
      } else if (type === "2") {
        this.active2 = index;
        this.formLabelAlign.queryYear = item;
      } else {
        this.active3 = index;
        this.formLabelAlign.hostUser.id = item.id;
      }
      this.getCaseList();
    },
    setStatus(e){
      this.formLabelAlign.status = e;
      this.getCaseList();
    },
    handleSelect(e) {
      console.log(e);
      this.formLabelAlign.checkWord = e;
      this.getCaseList();
    },
    // 获取所有用户列表
    getAllBriefList() {
      allBriefList().then((res) => {
        this.briefList = res.data;
      });
    },
    filterTag(value, row) {
      console.log(row);
      return row.lawCase.type === value;
    },
    // 获取案由列表
    getcaseCause() {
      caseCauseData().then((res) => {
        this.caseCausList = res.treeData;
      });
    },
    // 表格行背景颜色
    tableRowClassName({ row, rowIndex }) {
      if (row.lawCase.auditStatus === "0" || row.lawCase.auditStatus === "3") {
        return "nopass-bg";
      } else if (row.lawCase.auditStatus === "2") {
        return "pass-bg";
      } else if (row.lawCase.auditStatus === "1") {
        return "to-audit-bg";
      }
      return "";
    },
    //  tableRowClassName({row, rowIndex}) {
    //     if (rowIndex === 1) {
    //       return 'nopass-bg';
    //     } else if (rowIndex === 3) {
    //       return 'pass-bg';
    //     }
    //     return '';
    //   },

    // 获取所有通过审核案件列表
    getAllCaseList() {
      allCaseData().then((res) => {
        this.allCaseList = res.data;
      });
    },
    // 获取客户列表
    getAllCustomerList() {
      allCustomerData().then((res) => {
        this.allCustomerList = res.data;
      });
    },
    //每页条数改变时触发 选择一页显示多少行
    handleSizeChange(val) {
      this.currentPage = 1;
      this.pageSize = val;
    },
    //当前页改变时触发 跳转其他页
    handleCurrentSizeChange(val) {
      this.currentPage = val;
      this.getCaseList();
    },
    typeFormat(type) {
      const typeList = dictList.case_type;
      if (type.lawCase.type) {
        let tp = typeList.filter((item) => type.lawCase.type === item.value);
        return tp[0].label;
      } else {
        return "";
      }
    },
    getCaseList() {
      console.log("==================");
      this.loading = true;
      let formData = new FormData();
      formData.append("status", this.formLabelAlign.status);
      formData.append("pageNo", this.currentPage);
      formData.append("pageSize", this.pageSize);
      formData.append("name", this.formLabelAlign.name);
      formData.append("checkWord", this.formLabelAlign.checkWord);
      formData.append(
        "queryConcernPersonName",
        this.formLabelAlign.queryConcernPersonName
      );
      formData.append("type", this.formLabelAlign.type);
      formData.append("number", this.formLabelAlign.number);
      formData.append("acceptUnitName", this.formLabelAlign.acceptUnitName);
      formData.append("queryStageName", this.formLabelAlign.queryStageName);
      formData.append("caseCause.id", this.formLabelAlign.caseCause.id);
      formData.append("queryYear", this.formLabelAlign.queryYear);
      formData.append("hostUser.id", this.formLabelAlign.hostUser.id);
      formData.append("caseCause.name", this.formLabelAlign.caseCause.name);
      formData.append("caseProgram.name", this.formLabelAlign.caseProgram.name);
      formData.append(
        "queryStartEntrustDate",
        this.formLabelAlign.weian.length && this.formLabelAlign.weian[0]
      );
      formData.append(
        "queryEndEntrustDate",
        this.formLabelAlign.weian.length && this.formLabelAlign.weian[1]
      );
      formData.append(
        "queryStartSettleCaseDate",
        this.formLabelAlign.jiean.length && this.formLabelAlign.jiean[0]
      );
      formData.append(
        "queryEndSettleCaseDate",
        this.formLabelAlign.jiean.length && this.formLabelAlign.jiean[1]
      );

      getList(formData).then((res) => {
        this.loading = false;
        this.tableData = res.page.list;
        this.total = res.page.count;
        this.tableData.map((item) => (item.degree = "1"));
      });
    },
    handleCaseCause(event) {
      let data = this.$refs["cause"].getCheckedNodes()[0];
      if (data) {
        this.formLabelAlign.caseCause.id = data.value;
        this.formLabelAlign.caseCause.name = data.label;
      } else {
        this.formLabelAlign.caseCause.id = "";
        this.formLabelAlign.caseCause.name = "";
      }
    },
    reset() {
      this.active1 = null;
      this.active2 = null;
      this.active3 = null;
      this.formLabelAlign = {
        status:'99',
        checkWord: "",
        name: "",
        queryConcernPersonName: "",
        type: "",
        number: "",
        acceptUnitName: "",
        queryStageName: "",
        caseCause: {
          id: "",
          name: "",
        },
        caseReason: [],
        caseProgram: {
          name: "",
        },
        weian: [],
        jiean: [],
        hostUser: {
          id: "",
        },
        queryYear: "",
      };
      this.getCaseList();
    },
    toggoleShow() {
      this.isTable = !this.isTable;
    },
    handleCurrentChange(e) {
      storage.set("caseInfo", e.lawCase);
      this.$router.push({ path: "/case/detail", query: { id: e.lawCase.id } });
    },
    handleShowSetting() {},
    // 开启批量操作
    toggleMultiple(e) {},
    goPrivy() {
      this.$router.push({ path: "/case/privy" });
    },
    // 显示新建案件弹框
    handleShowCreateModal() {
		if(this.user.id=='15fd4f2685e94cb3bbdd5d4642dcc00f'){
			  
			 return this.$message.error("体验者不能操作");
		}
      this.createVisible = true;
    },
    // 确认新建案件
    handleCreateOk() {
		if(this.user.id=='15fd4f2685e94cb3bbdd5d4642dcc00f'){
			  
			 return this.$message.error("体验者不能操作");
		}
      this.$refs.createForm.$refs.ruleForm.validate((valid) => {
        if (valid) {
          const data = this.$refs.createForm.ruleForm;
          let relatedCase = data.relatedCase;
          let relatedClient = data.relatedClient;

          let relatedCaseList = [],
            relatedClientList = [];
          relatedCase.length &&
            relatedCase.map((item) => {
              this.allCaseList.map((child) => {
                if (item === child.id) {
                  relatedCaseList.push({
                    relationCase: {
                      // 关联案件信息
                      id: child.id,
                      name: child.name, // 案件名称
                    },
                  });
                }
              });
            });
          relatedClient.length &&
            relatedClient.map((item) => {
              this.allCustomerList.map((child) => {
                if (item === child.id) {
                  relatedClientList.push({
                    customer: {
                      // 关联案件信息
                      id: child.id,
                      name: child.name, // 案件名称
                    },
                  });
                }
              });
            });
          const param = {
            lawCase: {
              id: "",
              type: data.caseType, // 案件类型
			  mtypeId:data.mtypeId,
              caseProgram: data.caseProgram, // 审理程序
              name: data.caseName, // 案件名称
              caseCause: data.caseCause, // 案由
              number: data.caseNumber, // 案号
              entrustDate: data.entrustedTime, // 委托时间
              subjectMatter: data.price, // 案件标的
              hostUser: data.hostUser,
              recordDate: data.registerDate, // 立案日期
              trialResult: data.result, // 审理结果
              rulingDate: data.adjudicationDate, // 裁决日期
              settleCaseStatus: data.closingStatus, // 结案状态
              settleCaseDate: data.closingDate, // 结案日期
              archiveUser: {
                // 归档人
                id: "",
                name: data.filePerson, //名称
              },
              archiveDate: data.fileDate, // 归档日期
              custodyPlace: data.keepingPlace, // 档案保管地
              chargeMode: data.chargeType, // 收费方式
              contractMoney: data.contractAmount, // 合同金额
              chargeRemarks: data.chargeRemark, // 收费备注
              actualBackMoney: "", // 实际回款
              winMoney: "", // 胜诉金额
              acceptUnitArea:
                data.acceptUnitArea && data.acceptUnitArea.join("/"), // 受理单位地区 省-市-县
              acceptUnitType: data.acceptUnitType, // 单位类型
              acceptUnitName: data.acceptUnitName, // 单位名称
              status: "", // 案件状态
            },
            // 案件当事人信息
            concernPersonList: data.concernPersonList,
            // 案件承办人员信息
            undertakePersonList: data.undertakePersonList,
            // 案件与案件关联 信息
            caseRelationsList: relatedCaseList,
            // 案件与客户关联 信息
            customerRelationList: relatedClientList,
          };
          add(param).then((res) => {
            if (res.success) {
              this.createVisible = false;
              this.$refs.createForm.$refs.ruleForm.resetFields();
              this.$message({
                showClose: true,
                message: res.msg,
                type: "success",
              });
              this.getCaseList();
            } else {
              this.$message.error(res.msg);
            }
          });
        } else {
          return false;
        }
      });
    },
    // 取消新建案件
    handleCreateCancel(formName) {
      this.createVisible = false;
    },
    // 显示结案弹框
    handleShowClosingModal(scope) {
      this.closingModalMdl = scope.row;
      this.closingVisible = true;
    },
    // 确认结案
    handleClosingOk() {
		if(this.user.id=='15fd4f2685e94cb3bbdd5d4642dcc00f'){
			  
			 return this.$message.error("体验者不能操作");
		}
      this.$refs.closingForm.$refs.ruleForm.validate((valid) => {
        if (valid) {
          const data = this.$refs.closingForm.ruleForm;
          let formData = new FormData();
          formData.append("id", data.id);
          // formData.append("actualBackMoney", data.actualBackMoney);
          formData.append("settleCaseDate", data.settleCaseDate);
          formData.append("settleCaseStatus", data.settleCaseStatus);
          formData.append("remarks", data.remarks);
          formData.append("trialResult", data.trialResult);

          // return
          settle(formData).then((res) => {
            if (res.success) {
              this.closingVisible = false;
              this.$message({
                showClose: true,
                message: res.msg,
                type: "success",
              });
              this.getCaseList();
            } else {
              this.$message.error(res.msg);
            }
          });
        } else {
          return;
        }
      });
    },
    // 关闭结案弹框
    handleClosingCancel() {
      this.closingVisible = false;
    },
    // 归档
    handleShowFileModal() {
		if(this.user.id=='15fd4f2685e94cb3bbdd5d4642dcc00f'){
			  
			 return this.$message.error("体验者不能操作");
		}
      if (!this.selectCaseList.length) {
        return this.$message.error("请选择归档案件");
      }

      let ids = this.selectCaseList.map((item) => item.lawCase.id).join(",");
      this.handlearchive(ids);
    },
    // 确认归档
    handleFileOk() {
		if(this.user.id=='15fd4f2685e94cb3bbdd5d4642dcc00f'){
			  
			 return this.$message.error("体验者不能操作");
		}
      this.fileVisible = false;
    },
    // 关闭归档弹框
    handleFileCancel() {
      this.fileVisible = false;
    },
    // 显示创建副本弹框
    handleShowCopyModal() {
      this.copyVisible = true;
    },
    // 确认创建副本
    handleCopyOk() {
      this.copyVisible = false;
    },
    // 关闭创建副本弹框
    handleCopyCancel() {
      this.copyVisible = false;
    },
    // 显示修改信息弹框
    handleShowEditModal(scope) {
      this.editVisible = true;
      this.editModalMdl = scope.row;
    },
    // 确认修改信息
    handleEditOk() {
		if(this.user.id=='15fd4f2685e94cb3bbdd5d4642dcc00f'){
			 return this.$message.error("体验者不能操作");
		}
      this.$refs.editForm.$refs.ruleForm.validate((valid) => {
        if (valid) {
          const data = this.$refs.editForm.ruleForm.lawCase; 
          let relatedClientList = [];
          let relatedClient = data.relatedClient || [];
          relatedClient.length &&
            relatedClient.map((item) => {
              this.allCustomerList.map((child) => {
                if (item === child.id) {
                  relatedClientList.push({
                    customer: {
                      // 关联案件信息
                      id: child.id,
                      name: child.name, // 案件名称
                    },
                  });
                }
              });
            });
          const param = {
            id: data.id,
            type: data.type, // 案件类型
			mtypeId:data.mtypeId,
            caseProgram: data.caseProgram, // 案件程序
            name: data.name, // 案件名称
            caseCause: data.caseCause, // 案由
            number: data.number, // 案号
            entrustDate: data.entrustDate || "", // 委托时间
            subjectMatter: data.subjectMatter, // 案件标的
            hostUser: data.hostUser,
            recordDate: data.recordDate || "", // 立案日期
            trialResult: data.trialResult, // 审理结果
            rulingDate: data.rulingDate || "", // 裁决日期
            settleCaseStatus: data.settleCaseStatus, // 结案状态
            settleCaseDate: data.settleCaseDate || "", // 结案日期
            archiveUser: data.archiveUser,
            archiveDate: data.archiveDate || "", // 归档日期
            custodyPlace: data.custodyPlace, // 档案保管地
            chargeMode: data.chargeMode, // 收费方式
            contractMoney: data.contractMoney, // 合同金额
            chargeRemarks: data.chargeRemarks, // 收费备注
            actualBackMoney: data.actualBackMoney || 0, // 实际回款
            winMoney: data.winMoney || 0, // 胜诉金额
            acceptUnitArea: data.acceptUnitArea, // 受理单位地区 省-市-县
            acceptUnitType: data.acceptUnitType, // 单位类型
            acceptUnitName: data.acceptUnitName, // 单位名称
            status: "", // 案件状态
            customerRelationList: relatedClientList,
          };
          update(param).then((res) => {
            if (res.success) {
              this.editVisible = false;
              this.$message({
                showClose: true,
                message: res.msg,
                type: "success",
              });
              this.getCaseList();
            } else {
              this.$message.error(res.msg);
            }
          });
        } else {
          return;
        }
      });
    },
    // 关闭修改信息弹框
    handleEditCancel() {
      this.editVisible = false;
    },
    // 显示自定义表头弹框
    handleShowHeaderModal() {
      // this.headerVisible = true;
    },
    // 确认自定义表头
    handleHeaderOk() {
      this.headerVisible = false;
    },
    // 关闭自定义表头弹框
    handleHeaderCancel() {
      this.headerVisible = false;
    },
    //批量删除
    handleMultipleDelete() {
      let ids = this.selectCaseList.map((item) => item.lawCase.id).join(",");
      this.handleDeleteCase(ids);
    },
    handleSelectionChange(e) {
      this.selectCaseList = e;
    },
    // 删除案件
    handleDeleteCase(id) {
		if(this.user.id=='15fd4f2685e94cb3bbdd5d4642dcc00f'){
			 return this.$message.error("体验者不能操作");
		}
      this.$confirm("此操作将永久删除该案件, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          let param = new FormData();
          param.append("ids", id);
          del(param).then((res) => {
            if (res.success) {
              this.getCaseList();
              this.$message({
                type: "success",
                message: "删除成功!",
              });
            } else {
              this.$message.error(res.msg);
            }
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    // 点击操作下拉菜单
    handleCommand(command, scope) {
		if(this.user.id=='15fd4f2685e94cb3bbdd5d4642dcc00f'){
			 return this.$message.error("体验者不能操作");
		}
		
      switch (command) {
        case "closing": // 结案
          this.handleShowClosingModal(scope);
          break;
        case "file": // 归档
          this.handlearchive(scope.row.lawCase.id);
          break;
        case "edit": // 修改基本信息
          this.handleShowEditModal(scope);
          break;
        case "team": // 移至团队
          this.handleShowClosingModal();
          break;
        case "copy": //创建副本
          this.handleShowCopyModal();
          break;
        case "delete": //删除
          this.handleDeleteCase(scope.row.lawCase.id);
          break;
        case "multipleDelete": //批量删除
          this.handleMultipleDelete();
          break;
        default:
          break;
      }
    },
    handleDegree(command, index) {
      this.tableData[index].degree = command;
    },
    handlearchive(ids) {
      let fileName = `批量归档文件`;
      const param = {
        ids,
      };
      formatParams("/law/lawcase/case/archiveDownload", param, fileName, "zip");
    },
  },
};
</script>

<style lang="less" scoped>
.search-input {
  border-bottom: 1px solid #dcdfe6;
}
.input {
  /deep/.el-input__inner {
    border: none;
    background: none;
  }
}
.high-wrap {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin-left: 10px;
  margin-right: 20px;
  height: 34px;
  cursor: pointer;
}
.top-header-divide {
  width: 1px;
  height: 20px;
  background: #c1c4d3;
}
/deep/.el-date-editor {
  width: 100%;
}
.table-box {
  background: #fff;
  color: #111;
  margin-top: 30px;
  /deep/.el-table__row {
    color: #111;
  }
  /deep/.pass-bg {
    background: rgba(216, 252, 216, 0.3);
  }
  /deep/.nopass-bg {
    background: rgba(250, 220, 220, 0.3);
  }
  /deep/ .to-audit-bg {
    background: rgba(248, 248, 222, 0.3);
  }
  .footer-box {
    padding: 20px;
  }
}
/deep/.el-table__row {
  cursor: pointer;
}
.degree-dot {
  width: 10px;
  height: 10px;
  border-radius: 50%;
}
.red {
  background: #ec5050;
}
.orange {
  background: #ffa836;
}
.grey {
  background: #bcbcbc;
}
.left-box {
  background: #fff;
  margin-right: 20px;
  padding: 10px;
  min-width: 150px;
  /deep/  .el-icon-arrow-right:before {
    content: "+";
    font-size: 22px;
  }
   /deep/ .el-collapse-item__arrow.is-active{
    transform: unset;
  }
  /deep/ .el-icon-arrow-right.is-active:before {
    content: "-";
    font-size: 26px;

  }
}
</style>

<template>
  <el-dialog
    title="结案信息"
    :visible="visible"
    width="60%"
    :before-close="handleCancel "
    :close-on-press-escape="false"
    :close-on-click-modal="false"
  >
    <el-form
      :model="ruleForm"
      :rules="rules"
      ref="ruleForm"
      label-width="130px"
      class="ruleForm-box"
    >
      <el-row>
        <el-col :span="12">
          <el-form-item label="结案日期" prop="settleCaseDate">
            <el-date-picker
              v-model="ruleForm.settleCaseDate"
              type="date"
              placeholder="选择日期"
              value-format="yyyy-MM-dd HH:mm:ss"
            >
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="结案状态" prop="settleCaseStatus">
            <el-select
              v-model="ruleForm.settleCaseStatus"
              placeholder="请选择结案状态"
            >
              <el-option
                :label="item.label"
                :value="item.value"
                v-for="item in settle_case_status"
                :key="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="办理结果" prop="trialResult">
            <el-select v-model="ruleForm.trialResult" placeholder="请选择办理结果">
              <el-option
                :label="item.label"
                :value="item.value"
                v-for="item in trial_resultList"
                :key="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <!-- <el-col :span="12">
          <el-form-item label="实际回款" prop="actualBackMoney">
            <el-input placeholder="" v-model="ruleForm.actualBackMoney">
              <template slot="append">元</template>
            </el-input>
          </el-form-item>
        </el-col> -->
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="案件备注" prop="remarks">
            <el-input
              v-model="ruleForm.remarks"
              type="textarea"
              :rows="2"
              placeholder="请输入内容"
            >
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button
        @click="handleCancel('ruleForm')">取 消</el-button
      >
      <el-button
        type="primary"
        @click="handleOk('ruleForm')"
        >确认修改</el-button
      >
    </span>
  </el-dialog>
</template>

<script>
import storage from "store";

export default {
  name: "ClosingCase",
  props: {
    visible: {
      type: Boolean,
      required: true,
    },
    loading: {
      type: Boolean,
      default: () => false,
    },
    model: {
      type: Object,
      default: () => null,
    },
  },
  data() {
    const dictList = storage.get("dictList");

    return {
      settle_case_status: dictList.settle_case_status,
      trial_resultList: dictList.trial_result,
      ruleForm: {
        remarks: "",
        actualBackMoney: "0",
        trialResult: "",
        settleCaseStatus: "",
        settleCaseDate: "",
      },
      rules: {},
    };
  },
  mounted() {
    // 当 model 发生改变时，为表单设置值
    this.$watch(
      "visible",
      (e) => {
        if (e) {
          this.ruleForm = JSON.parse(JSON.stringify(this.model.lawCase)) 
        }
      },
      {
        deep: true,
        immediate: true,
      }
    );
  },
  methods:{
     handleOk(formName) {
      this.$emit("ok");
      // this.$refs[formName].resetFields();
    },
    handleCancel() {
      this.$refs.ruleForm.resetFields();
      this.$emit("cancel");
    },
  }
};
</script>

<style>
</style>
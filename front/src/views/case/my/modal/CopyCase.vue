<template>
  <el-dialog
    title="创建副本"
    :visible="visible"
    width="30%"
    :close-on-press-escape="false"
    :close-on-click-modal="false"
    :before-close="
      () => {
        $emit('cancel');
      }
    "
  >
    <el-form
      :model="ruleForm"
      :rules="rules"
      ref="ruleForm"
      label-width="130px"
      class="ruleForm-box"
    >
      <el-row>
        <el-col :span="24">
          <el-form-item label="复制数量" prop="number1">
            <el-input placeholder="" v-model.number="ruleForm.number1" type="text">
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button
        @click="
          () => {
            $emit('ok');
          }
        "
        >取 消</el-button
      >
      <el-button
        type="primary"
        @click="
          () => {
            $emit('cancel');
          }
        "
        >提交</el-button
      >
    </span>
  </el-dialog>
</template>

<script>
export default {
  name: "CopyCase",
  props: {
    visible: {
      type: Boolean,
      required: true,
    },
    loading: {
      type: Boolean,
      default: () => false,
    },
    model: {
      type: Object,
      default: () => null,
    },
  },
  data() {
    return {
      ruleForm: {
        number1: 1,
      },
      rules: {
        number1: [
          { required: true, message: "请输入复制数量", trigger: "blur" },
          { type: "number", message: "数量必须为数字值" },
        ],
      },
    };
  },
};
</script>

<style>
</style>
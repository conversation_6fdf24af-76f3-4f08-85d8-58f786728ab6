<template>
  <el-dialog
    title="归档信息"
    :visible="visible"
    width="60%"
    :close-on-press-escape="false"
    :close-on-click-modal="false"
    :before-close="
      () => {
        $emit('cancel');
      }
    "
  >
    <el-form
      :model="ruleForm"
      :rules="rules"
      ref="ruleForm"
      label-width="130px"
      class="ruleForm-box"
    >
      <el-row>
        <el-col :span="12">
          <el-form-item label="归档日期" prop="closingDate">
            <el-date-picker
              v-model="ruleForm.closingDate"
              type="date"
              placeholder="选择日期"
              value-format="yyyy-MM-dd HH:mm:ss"
            >
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="归档人" prop="closingStatus">
            <el-select
              v-model="ruleForm.closingStatus"
              placeholder="请选择结案状态"
            >
              <el-option label="裁决" value="shanghai"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="归档保管地" prop="collection">
            <el-input placeholder="" v-model="ruleForm.collection"> </el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="案件备注" prop="remarks">
            <el-input
              v-model="ruleForm.remarks"
              type="textarea"
              :rows="2"
              placeholder="请输入内容"
            >
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button
        @click="
          () => {
            $emit('ok');
          }
        "
        >取 消</el-button
      >
      <el-button
        type="primary"
        @click="
          () => {
            $emit('cancel');
          }
        "
        >提交</el-button
      >
    </span>
  </el-dialog>
</template>

<script>
import storage from "store";

export default {
  name: "ClosingCase",
  props: {
    visible: {
      type: Boolean,
      required: true,
    },
    loading: {
      type: Boolean,
      default: () => false,
    },
    model: {
      type: Object,
      default: () => null,
    },
  },
  data() {
    const dictList = storage.get("dictList");

    return {
      settle_case_status: dictList.settle_case_status,
      ruleForm: {
        remarks: "",
        collection: "",
        result: "",
        closingStatus: "",
        closingDate: "",
      },
      rules: {},
    };
  },
};
</script>

<style>
</style>
<template>
  <el-dialog
    title="自定义表头"
    :visible="visible"
    width="60%"
    :close-on-press-escape="false"
    :close-on-click-modal="false"
    :before-close="
      () => {
        $emit('cancel');
      }
    "
  >
    <el-form
      :model="ruleForm"
      :rules="rules"
      ref="ruleForm"
      label-width="130px"
      class="ruleForm-box"
    >
      <el-checkbox-group v-model="ruleForm.field" class="checkbox-group">
        <div
          v-for="(item, index) in fieldList"
          :key="index"
          class="checkbox-item"
        >
          <el-checkbox :label="item"></el-checkbox>
        </div>
      </el-checkbox-group>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button
        @click="
          () => {
            $emit('ok');
          }
        "
        >取 消</el-button
      >
      <el-button
        type="primary"
        @click="
          () => {
            $emit('cancel');
          }
        "
        >提交</el-button
      >
    </span>
  </el-dialog>
</template>

<script>
export default {
  name: "CopyCase",
  props: {
    visible: {
      type: Boolean,
      required: true,
    },
    loading: {
      type: Boolean,
      default: () => false,
    },
    model: {
      type: Object,
      default: () => null,
    },
  },
  data() {
    return {
      ruleForm: {
        field: ["类型", "案件程序", "案由", "委托人"],
      },
      fieldList: ["类型", "案件程序", "案由", "委托人", "承办人", "合同金额"],
      rules: {
        number1: [
          { required: true, message: "请输入复制数量", trigger: "blur" },
          { type: "number", message: "数量必须为数字值" },
        ],
      },
    };
  },
};
</script>

<style scoped>
.checkbox-group {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
}
.checkbox-item {
  padding: 10px;
  border: 1px solid #e1e4ee;
  margin: 10px;
  border-radius: 6px;
  width: 105px;
}
</style>
<template>
  <el-dialog
    lock-scroll
    append-to-body
    title="基本信息"
    :visible="visible"
    width="60%"
    :before-close="handleCancel"
    :close-on-press-escape="false"
    :close-on-click-modal="false"
  >
    <el-form
      :model="ruleForm"
      :rules="rules"
      ref="ruleForm"
      label-width="90px"
      class="ruleForm-box"
    >
      <el-row>
        <el-col :span="24">
          <el-form-item label="案件名称" prop="caseName">
            <el-input type="text" v-model="ruleForm.lawCase.name"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="委托日期" prop="entrustedTime">
            <el-date-picker
              v-model="ruleForm.lawCase.entrustDate"
              type="date"
              placeholder="选择日期"
              value-format="yyyy-MM-dd HH:mm:ss"
            >
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="案件类型">
            <el-select
              v-model="ruleForm.lawCase.type"
              placeholder="请选择"
              disabled
            >
              <el-option
                :label="item.label"
                :value="item.value"
                v-for="item in caseTypeList"
                :key="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
   
        <!-- <el-col :span="12">
          <el-form-item label="案由" prop="caseReason">
            <el-cascader
              v-model="ruleForm.reasonVal"
              :options="caseCausList"
              :props="{ checkStrictly: true, value: 'id', label: 'name' }"
              clearable
              @change="handleCaseCause($event)"
              ref="cause"
            >
            </el-cascader>
          </el-form-item>
        </el-col> -->
        <el-col :span="12">
          <el-form-item label="案由" prop="caseReason">
            <treeselect
              placeholder="请选择案由"
			    isShowSearch=true
				 @select="handleCaseDesc"
              v-model="ruleForm.lawCase.caseCause.id"
              :options="caseCausList"
              :normalizer="normalizer"
            />
          </el-form-item>
        </el-col>
		<el-col :span="12">
		  <el-form-item label="模板类型" prop="mtypeId">
		    <treeselect
		      placeholder="请选择到最终一层"
		      v-model="ruleForm.lawCase.mtypeId"
		      :options="caseModelTypeList"
		      :normalizer="normalizer2"
		    />
		  </el-form-item>
		</el-col>
      </el-row>
      <el-row>
		  <el-col :span="12">
		    <el-form-item label="立案日期" prop="registerDate">
		      <el-date-picker
		        v-model="ruleForm.lawCase.recordDate"
		        type="date"
		        placeholder="选择立案日期"
		        value-format="yyyy-MM-dd HH:mm:ss"
		      >
		      </el-date-picker>
		    </el-form-item>
		  </el-col>
        <el-col :span="12">
          <el-form-item label="裁决日期" prop="adjudicationDate">
            <el-date-picker
              v-model="ruleForm.lawCase.rulingDate"
              type="date"
              placeholder="选择裁决日期"
              value-format="yyyy-MM-dd HH:mm:ss"
            >
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="审理程序" prop="lawCase">
            <el-cascader
              v-model="ruleForm.processVal"
              :options="processList"
              :props="{ checkStrictly: true, value: 'id', label: 'name' }"
              clearable
              @change="handleCaseProgram($event)"
              ref="progress"
            >
            </el-cascader>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="律所案号" prop="caseNumber">
            <el-input
              type="text"
              v-model="ruleForm.lawCase.number"
              disabled
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="审理结果" prop="result">
            <el-select
              v-model="ruleForm.lawCase.trialResult"
              placeholder="请选择审理结果"
            >
              <el-option
                :label="item.label"
                :value="item.value"
                v-for="item in trial_resultList"
                :key="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="关联客户" prop="relatedClient">
            <el-select
              v-model="ruleForm.relatedClient"
              placeholder="请选择关联客户"
              multiple
            >
              <el-option
                :label="item.name"
                :value="item.id"
                v-for="item in allCustomerList"
                :key="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="收费方式" prop="">
            <el-select
              v-model="ruleForm.lawCase.chargeMode"
              placeholder="请选择收费方式"
            >
              <el-option
                :label="item.label"
                :value="item.value"
                v-for="item in charge_modeList"
                :key="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="合同金额" prop="lawCase.contractMoney">
            <el-input
              type="number" step="0.01"
              v-model.number="ruleForm.lawCase.contractMoney"
              ><template slot="append">元</template></el-input
            >
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="收费备注" prop="chargeRemark">
            <el-input
              type="text"
              v-model="ruleForm.lawCase.chargeRemarks"
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="案件标的" prop="lawCase.subjectMatter">
            <el-input
              placeholder="" type="number" step="0.01"
              v-model.number="ruleForm.lawCase.subjectMatter"
            >
              <template slot="append">元</template>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="胜诉金额">
            <el-input placeholder="" type="number"  v-model.number="ruleForm.lawCase.winMoney">
              <template slot="append">元</template>
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="实际回款" >
            <el-input
              placeholder=""
               type="number" step="0.01"
              v-model.number="ruleForm.lawCase.actualBackMoney"
            >
              <template slot="append">元</template>
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="结案日期" prop="closingDate">
            <el-date-picker
              v-model="ruleForm.lawCase.settleCaseDate"
              type="date"
              placeholder="选择结案日期"
              value-format="yyyy-MM-dd HH:mm:ss"
            >
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="结案状态" prop="closingStatus">
            <el-select
              v-model="ruleForm.lawCase.settleCaseStatus"
              placeholder="请选择结案状态"
            >
              <el-option
                :label="item.label"
                :value="item.value"
                v-for="item in settle_case_statusList"
                :key="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <!-- <el-row>
        <el-col :span="12">
          <el-form-item label="归档日期" prop="fileDate">
            <el-date-picker
              v-model="ruleForm.lawCase.archiveDate"
              type="date"
              placeholder="选择归档日期"
              value-format="yyyy-MM-dd HH:mm:ss"
            >
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="归档人" prop="filePerson">
            <el-input
              v-model="ruleForm.lawCase.archiveUser.name"
              placeholder="请输入归档人"
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="档案保管地" prop="keepingPlace">
            <el-input
              type="text"
              v-model="ruleForm.lawCase.custodyPlace"
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row> -->
      <el-row>
        <el-col :span="24">
          <el-form-item label="其他备注" prop="remarks">
            <el-input
              v-model="ruleForm.lawCase.remarks"
              type="textarea"
              :rows="2"
            >
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleCancel('ruleForm')">取 消</el-button>
      <el-button type="primary" @click="handleOk('ruleForm')"
        >确认修改</el-button
      >
    </span>
  </el-dialog>
</template>

<script>
import storage from "store";
import { isEmpty, cloneDeep } from "lodash";
import { getCaseProgram, caseCauseData,caseModelTypeTemplateData } from "@/api/case/manage";
import Treeselect from "@riophae/vue-treeselect";
// import the styles
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
export default {
  name: "index",
  components: {
    Treeselect,
  },
  props: {
    visible: {
      type: Boolean,
      required: true,
    },
    loading: {
      type: Boolean,
      default: () => false,
    },
    model: {
      type: Object,
      default: () => null,
    },
    allCustomerList: {
      type: Array,
      required: true,
    },
  },
  data() {
    const dictList = storage.get("dictList");

    return {
		normalizer2(node) {
		  return {
		    id: node.id,
		    label: node.pname,
		    children: node.children,
		  };
		},
      normalizer(node) {
        return {
          id: node.id,
          label: node.name,
          children: node.children,
        };
      },
      charge_modeList: dictList.charge_mode,
      caseTypeList: dictList.case_type,
      trial_resultList: dictList.trial_result,
      settle_case_statusList: dictList.settle_case_status,
      processList: [],
      isShowMore: false,
      ruleForm: {
        lawCase: {
			mtypeId:"",
          id: "",
          type: "",
          caseProgram: {
            id: "",
            name: "",
          },
         caseCause: {
             id: '',
             name: ''
         },
          name: "",
          process: [],
          reason: [],
          number: "",
          subjectMatter: 0,
          hostUser: {
            id: "",
            name: "",
            roleIdList: [],
            admin: false,
            roleNames: "",
            roleIds: "",
          },
          trialResult: "",
          settleCaseStatus: "",
          archiveDate: "",
          archiveUser: {
            id: "",
            roleIdList: [],
            admin: false,
            roleNames: "",
            roleIds: "",
          },
          custodyPlace: "",
          chargeMode: "",
          contractMoney: 0,
          chargeRemarks: "",
          isShare: "",
          status: "",
          rulingDate: "",
          actualBackMoney: "",
          winMoney: 0,
          recordDate: "",
          relatedClient: [],
        },
      },
      rules: {
        caseType: [
          { required: true, message: "请选择案件类型", trigger: "change" },
        ],
        process: [
          { required: true, message: "请选择审理程序", trigger: "change" },
        ],
        "lawCase.contractMoney": [
          { required: true, message: "请输入合同金额", trigger: "blur" },
          {pattern:/^(([1-9]{1}\d*)|(0{1}))(\.\d{1,2})?$/, message: '金额仅限两位小数'},
          { type: "number", message: "金额必须为数字值" },
        ],
        "lawCase.winMoney": [
          { required: false, message: "请输入胜诉金额", trigger: "blur" },
          {pattern:/^(([1-9]{1}\d*)|(0{1}))(\.\d{1,2})?$/, message: '金额仅限两位小数'},
          { type: "number", message: "金额必须为数字值" },
        ],
        "lawCase.subjectMatter": [
          { required: true, message: "请输入案件标的", trigger: "blur" },
          {pattern:/^(([1-9]{1}\d*)|(0{1}))(\.\d{1,2})?$/, message: '金额仅限两位小数'},
          { type: "number", message: "金额必须为数字值" },
        ],
        "lawCase.actualBackMoney": [
          { required: false, message: "请输入实际回款", trigger: "blur" },
          {pattern:/^(([1-9]{1}\d*)|(0{1}))(\.\d{1,2})?$/, message: '金额仅限两位小数'},
          { type: "number", message: "金额必须为数字值" },
        ],
      },
      caseCausList: [],
	  caseModelTypeList:[],
    };
  },
  methods: {
	  handleCaseDesc(e) {
	      this.ruleForm.lawCase.caseCause.id = e.id;
	      this.ruleForm.lawCase.caseCause.name = e.name;
	  		  this.ruleForm.lawCase.caseReason=e.name;
			  console.log("==========="+e.name)
			console.log(this.ruleForm.lawCase.caseCause)
	      // this.caseCause = e.id;
	  },
	  getcaseModelType(){
	  		  caseModelTypeTemplateData().then((res) => {
	  			  this.caseModelTypeList =  res.treeData
	  		  });
	  },
    // 获取案由列表
    getcaseCause() {
      caseCauseData().then((res) => {
        this.caseCausList = res.treeData;
      });
    },
    handleCaseProgram(event) {
      let data = this.$refs["progress"].getCheckedNodes()[0];
      this.ruleForm.lawCase.caseProgram.id = data.value;
      this.ruleForm.lawCase.caseProgram.name = data.label;
    },
    handleCaseCause(event) {
      let data = this.$refs["cause"].getCheckedNodes()[0];
      if (data) {
        this.ruleForm.lawCase.caseCause.id = data.value;
        this.ruleForm.lawCase.caseCause.name = data.label;
		console.log(this.ruleForm.lawCase.caseCause)
      } else {
        this.ruleForm.lawCase.caseCause.id = "";
        this.ruleForm.lawCase.caseCause.name = "";
      }
    },
    getCaseProgramList(e) {
      let formdata = new FormData();
      formdata.append("type", e);

      getCaseProgram(formdata).then((res) => {
        this.processList = res.treeData;
      });
    },
    handleOk(formName) { 
      this.$emit("ok");
      // this.$refs[formName].resetFields();
    },
    handleCancel() {
      // this.$refs.ruleForm.resetFields();
      this.$emit("cancel");
    },
  },
  mounted() {
    this.getcaseCause();
	this.getcaseModelType();

    // 当 model 发生改变时，为表单设置值
    this.$watch(
      "visible",
      (e) => {
        if (e) {
          this.ruleForm = JSON.parse(JSON.stringify(this.model));
          this.getCaseProgramList(this.model.lawCase.type);

          this.ruleForm.processVal =
            this.model.lawCase.caseProgram.parentIdsArr;
          this.ruleForm.reasonVal = this.model.lawCase.caseCause.parentIdsArr;
          this.ruleForm.lawCase.caseCause = {
			  id:this.model.lawCase.caseCause.id,
			  name:this.model.lawCase.caseCause.name,
		  }
		  
          console.log(this.ruleForm)
        }
      },
      {
        deep: true,
        immediate: true,
      }
    );
  },
};
</script>

<style lang="less" scoped>
.flex-center {
  color: #409eff;
  cursor: pointer;
}
h3 {
  margin: 20px 0;
}
.ruleForm-box {
  // height: 70vh;
  overflow-y: scroll;
  padding: 20px;
}
</style>
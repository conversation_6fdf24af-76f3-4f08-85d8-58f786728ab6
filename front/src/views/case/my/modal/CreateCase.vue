<template>
  <el-dialog
    title="新建案件"
    :visible="visible"
    width="60%"
	 class="dialogClass"
    :before-close="handleCancel"
    :close-on-press-escape="false"
	 
    :close-on-click-modal="false"
  >
    <el-form
      :model="ruleForm"
      :rules="rules"
      ref="ruleForm"
      label-width="100px"
      class="ruleForm-box"
    >
      <h3 style="margin-top: 1px;margin-bottom: 10px;">一、案件基本信息</h3>
      <el-row>
        <el-col :span="12">
          <el-form-item label="案件类型" prop="caseType">
            <el-select
              v-model="ruleForm.caseType"
              placeholder="请选择"
              @change="handleSelectCaseType"
            >
              <el-option
                :label="item.label"
                :value="item.value"
                v-for="item in caseTypeList"
                :key="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="审理程序" prop="process">
            <el-cascader
              v-model="ruleForm.process"
              :options="processList"
              :props="{ checkStrictly: true, value: 'id', label: 'name' }"
              clearable
              @change="handleCaseProgram($event)"
              ref="progress"
            >
            </el-cascader>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="案件名称" prop="caseName">
            <el-input type="text" v-model="ruleForm.caseName"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <!-- <el-col :span="12">
          <el-form-item label="案由" prop="caseReason">
            <el-cascader
              v-model="ruleForm.caseReason"
              :options="caseCausList"
              :props="{ checkStrictly: true, value: 'id', label: 'name' }"
              clearable
              @change="handleCaseCause($event)"
              ref="cause"
            >
            </el-cascader>
          </el-form-item>
        </el-col> -->
      <el-col :span="12">
		 
          <el-form-item label="案由" prop="caseReason">
            <treeselect
              placeholder="请选择案由"
			  isShowSearch=true
              @getValue="handleCaseDesc"
              v-model="ruleForm.caseCause"
              :options="caseCausList"
              :props="normalizer"
            />
          </el-form-item>
        </el-col> 
		<el-col :span="12">
		 
		  <el-form-item label="模板类型" prop="modeType.id">
		  <treeselect
		    placeholder="请选择到最终一层"
		    @getValue="handleModeType"
		    v-model="ruleForm.modeType"
		    :options="caseModelTypeList"
		    :props="normalizer"
		  />
		  </el-form-item>
		</el-col>
    
      </el-row>
      <el-row>
		  <el-col :span="12">
		    <el-form-item label="律所案号" prop="caseNumber">
		      <el-input
		        type="text"
		        v-model="ruleForm.caseNumber"
		        disabled
		        placeholder="自动生成"
		      ></el-input>
		    </el-form-item>
		  </el-col>
        <el-col :span="12">
          <el-form-item label="委托时间" prop="entrustedTime">
            <el-date-picker
              v-model="ruleForm.entrustedTime"
              type="date"
              placeholder="选择日期"
              value-format="yyyy-MM-dd HH:mm:ss"
            >
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="案件标的" prop="price">
            <el-input placeholder="不涉及财产金额，请输入0" v-model="ruleForm.price" oninput="value=value.replace(/[^0-9.]/g,'')">
              <template slot="append">元</template>
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="主办人员" prop="hostUser.id">
            <el-select
              v-model="ruleForm.hostUser.id"
              placeholder="请选择主办人员"
              @change="handleSelect"
            >
              <el-option
                :label="item.name"
                :value="item.id"
                v-for="item in briefList"
                :key="item.id"
              ></el-option>
            </el-select>
            <!-- <el-input v-model="ruleForm.sponsor" placeholder="请输入">
            </el-input> -->
          </el-form-item>
        </el-col>
      </el-row>

      <h3  style="margin-top: 1px;margin-bottom: 1px;">二、案件当事人</h3>
      <div v-for="(item, index) in ruleForm.concernPersonList" :key="index">
        <el-row style="margin-bottom: -20px;">
          <el-col :span="12">
            <el-form-item label="类型" prop="">
              <el-radio-group v-model="item.type">
                <el-radio
                  :label="item.value"
                  v-for="(item, index) in customer_type"
                  :key="index"
                  >{{ item.label }}</el-radio
                >
              </el-radio-group>
            </el-form-item>
          </el-col>
          <!--<el-col :span="12">
            <el-form-item label="委托方" prop="isEntrust">
              <el-radio-group v-model="item.isEntrust">
                <el-radio
                  :label="item.value"
                  v-for="(item, index) in yes_no"
                  :key="index"
                  >{{ item.label }}</el-radio
                >
              </el-radio-group>
            </el-form-item>
          </el-col>-->
        </el-row>

        <el-row>
          <el-col :span="24">
            <el-form-item
              :label="item.type === '1' ? '姓名' : '单位姓名'"
              :prop="`concernPersonList[${index}].name`"
              :rules="{ required: true, message: '请输入', trigger: 'blur' }"
            >
              <el-input type="text" v-model="item.name"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <div v-if="item.type === '1'" key="0">
          <el-row v-if="1==0">
            <el-col :span="12">
              <el-form-item label="民族" prop="nation">
                <el-input type="text" v-model="item.nation"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12" >
              <el-form-item label="性别" prop="sex">
                <el-radio-group v-model="item.sex">
                  <el-radio
                    :label="item.value"
                    v-for="(item, index) in sexList"
                    :key="index"
                    >{{ item.label }}</el-radio
                  >
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item
                label="联系方式"
                :prop="`concernPersonList[${index}].phone`"
                :rules="{
                  required: true,
                  message: '请输入联系方式',
                  trigger: 'blur',
                }"
              >
                <el-input type="text" v-model="item.phone"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="证件号码" prop="idNumber">
                <el-input type="text" v-model="item.idNumber"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row v-if="1==0">
            <el-col :span="24">
              <el-form-item label="住所地" prop="address">
                <el-input type="text" v-model="item.address"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </div>
        <div v-else key="1">
          <el-row>
            <el-col :span="24">
              <el-form-item label="单位地址" prop="address">
                <el-input type="text" v-model="item.address"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item label="法定代表人" prop="legalRepresentative">
                <el-input
                  type="text"
                  v-model="item.legalRepresentative"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                label="联系方式"
                :prop="`concernPersonList[${index}].phone`"
                :rules="{
                  required: true,
                  message: '请输入联系方式',
                  trigger: 'blur',
                }"
              >
                <el-input type="text" v-model="item.phone"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item
                label="统一社会信用代码"
                prop="unifiedSocialCreditCode"
              >
                <el-input
                  type="text"
                  v-model="item.unifiedSocialCreditCode"
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </div>
        <el-row v-if="1==0">
          <el-col :span="24">
            <el-form-item label="备注" prop="remark">
              <el-input v-model="item.remark" type="textarea" :rows="2">
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <div style="text-align: right">
          <i
            v-if="index === 0"
            class="el-icon-circle-plus-outline cursor primary"
            style="font-size: 14px"
            @click="handleConcernPerson('add')"
            >添加当事人</i
          >
          <i
            v-else
            class="el-icon-remove-outline cursor"
            style="font-size: 14px; color: red"
            @click="handleConcernPerson('del', index)"
            >删除当事人</i
          >
        </div>
      </div>

      <div class="flex-center" @click="isShowMore = true" v-if="!isShowMore">
        添加更多案件信息<i class="el-icon-arrow-down"></i>
      </div>
      <div v-if="isShowMore">
        <h3 style="margin-top: 1px;margin-bottom: 5px;">三、受理单位</h3>
        <el-row style="margin-bottom: -10px;">
          <el-col :span="12">
            <el-form-item label="地区" prop="acceptUnitArea">
              <el-cascader
                v-model="ruleForm.acceptUnitArea"
                :options="options"
              ></el-cascader>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="单位类型" prop="acceptUnitType">
              <el-select
                v-model="ruleForm.acceptUnitType"
                placeholder="请选择单位类型"
              >
                <el-option
                  :label="item.label"
                  :value="item.value"
                  v-for="item in unitTypeList"
                  :key="item.id"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="单位全称" prop="acceptUnitName">
              <!-- <el-autocomplete
                class="inline-input"
                v-model="ruleForm.acceptUnitName"
                :fetch-suggestions="querySearch"
                placeholder="请先选择地区"
              ></el-autocomplete> -->
              <el-input
                type="text"
                v-model="ruleForm.acceptUnitName"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <!-- <h3>承办人信息</h3> -->
        <div v-for="(item, index) in ruleForm.undertakePersonList" :key="index">
          <el-row>
            <el-col :span="12">
              <el-form-item label="承办人" prop="catererName">
                <el-input type="text" v-model="item.name"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="联系电话" prop="catererPhone">
                <el-input type="text" v-model="item.phone"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <!-- <el-row>
            <el-col :span="12">
              <el-form-item label="科室" prop="catererRoom">
                <el-input type="text" v-model="item.department"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="职务" prop="catererPost">
                <el-input type="text" v-model="item.post"></el-input>
              </el-form-item>
            </el-col>
          </el-row> -->
          <el-row v-if="1==0">
            <el-col :span="12">
              <el-form-item label="地址" prop="catererAddress">
                <el-input type="text" v-model="item.address"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="备注" prop="catererRemark">
                <el-input type="text" v-model="item.remark"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <div style="text-align: right; margin-bottom: 10px">
            <i
              v-if="index === 0"
              class="el-icon-circle-plus-outline cursor primary"
              style="font-size: 14px"
              @click="handleUndertaker('add')"
              >添加承办人</i
            >
            <i
              v-else
              class="el-icon-remove-outline cursor"
              style="font-size: 14px; color: red"
              @click="handleUndertaker('del', index)"
              >删除承办人</i
            >
          </div>
        </div>

        <h3  style="margin-top: 1px;margin-bottom: 5px;">四、收费信息</h3>
        <el-row>
          <el-col :span="12">
            <el-form-item label="收费方式" prop="chargeType">
              <el-select   @change="selChargeType" 
                v-model="ruleForm.chargeType"
                placeholder="请选择收费方式"
              >
                <el-option
                  :label="item.label"
                  :value="item.value"
				
                  v-for="item in charge_modeList"
                  :key="item.id"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="合同金额" prop="contractAmount">
              <el-input type="text" v-model="ruleForm.contractAmount"
                ><template slot="append">元</template></el-input
              >
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="收费备注" prop="chargeRemark">
              <el-input type="text" v-model="ruleForm.chargeRemark"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <!-- <h3>归档信息</h3>
        <el-row>
          <el-col :span="12">
            <el-form-item label="立案日期" prop="registerDate">
              <el-date-picker
                v-model="ruleForm.registerDate"
                type="date"
                placeholder="选择立案日期"
                value-format="yyyy-MM-dd HH:mm:ss"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="审理结果" prop="result">
              <el-select v-model="ruleForm.result" placeholder="请选择审理结果">
                <el-option
                  :label="item.label"
                  :value="item.value"
                  v-for="item in trial_resultList"
                  :key="item.id"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="裁决日期" prop="adjudicationDate">
              <el-date-picker
                v-model="ruleForm.adjudicationDate"
                type="date"
                placeholder="选择裁决日期"
                value-format="yyyy-MM-dd HH:mm:ss"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="结案状态" prop="closingStatus">
              <el-select
                v-model="ruleForm.closingStatus"
                placeholder="请选择结案状态"
              >
                <el-option
                  :label="item.label"
                  :value="item.value"
                  v-for="item in settle_case_statusList"
                  :key="item.id"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="结案日期" prop="closingDate">
              <el-date-picker
                v-model="ruleForm.closingDate"
                type="date"
                placeholder="选择结案日期"
                value-format="yyyy-MM-dd HH:mm:ss"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="归档人" prop="filePerson">
         
              <el-input
                v-model="ruleForm.filePerson"
                placeholder="请输入"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="归档日期" prop="fileDate">
              <el-date-picker
                v-model="ruleForm.fileDate"
                type="date"
                placeholder="选择归档日期"
                value-format="yyyy-MM-dd HH:mm:ss"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="档案保管地" prop="keepingPlace">
              <el-input type="text" v-model="ruleForm.keepingPlace"></el-input>
            </el-form-item>
          </el-col>
        </el-row> -->
		
		<!--
        <h3 style="margin-top: 1px;margin-bottom: 5px;">五、其他</h3>

        <el-row>
          <el-col :span="24">
            <el-form-item label="关联案件" prop="relatedCase">
              <el-select
                v-model="ruleForm.relatedCase"
                placeholder="请选择关联案件"
                multiple
              >
                <el-option
                  :label="item.name"
                  :value="item.id"
                  v-for="item in allCaseList"
                  :key="item.id"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="关联客户" prop="relatedClient">
              <el-select
                v-model="ruleForm.relatedClient"
                placeholder="请选择关联客户"
                multiple
              >
                <el-option
                  :label="item.name"
                  :value="item.id"
                  v-for="item in allCustomerList"
                  :key="item.id"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="其他备注" prop="otherRemark">
              <el-input
                v-model="ruleForm.otherRemark"
                type="textarea"
                :rows="2"
              >
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
		-->
      </div>
	  <div style="height: 20px;"></div>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleCancel('ruleForm')">取 消</el-button>
      <el-button type="primary" @click="handleOk('ruleForm')"
        >立即提交</el-button
      >
    </span>
  </el-dialog>
</template>

<script>
import { getCaseProgram, caseCauseData,caseModelTypeTemplateData } from "@/api/case/manage";
import storage from "store";
import options from "@/utils/cities.js";
import { allBriefList } from "@/api/login";
import { allCaseData } from "@/api/case/manage";
import { allCustomerData } from "@/api/customer/customer";
import Treeselect from "@/components/treeSelect"; 
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
// import the styles
export default {
  name: "index",
  components: {
    Treeselect 
  },
  props: {
    visible: {
      type: Boolean,
      required: true,
    },
    loading: {
      type: Boolean,
      default: () => false,
    },
    model: {
      type: Object,
      default: () => null,
    },
    allCaseList: {
      type: Array,
      required: true,
    },
    allCustomerList: {
      type: Array,
      required: true,
    },
  },
  data() {
    const dictList = storage.get("dictList");
      const validateMoney = (rule,value,callback) =>{
          if(!value){
              callback(new Error('不能为空'))
          }else if(value.indexOf(".") !== -1 && value.split('.').length > 2){
              callback(new Error('请输入正确格式的金额')) //防止输入多个小数点
          }else if(value.indexOf(".") !== -1 && value.split('.')[1].length > 2){
              callback(new Error('请输入正确的小数位数')) //小数点后两位
          }else{
              callback();
          }
      };
    return {
		normalizer2(node) {
		  return {
		    id: node.id,
		    label: node.pname,
		    children: node.children,
		  };
		},
      normalizer: {
        id: 'id',
        label: 'name',
        children: 'children',
      },
      options,
	  caseModelTypeList:[],
      sexList: dictList.sex,
      customer_type: dictList.customer_type,
      yes_no: dictList.yes_no,
      charge_modeList: dictList.charge_mode,
      settle_case_statusList: dictList.settle_case_status,
      trial_resultList: dictList.trial_result,
      unitTypeList: dictList.accept_unit_type,
      caseTypeList: dictList.case_type,
      processList: [],
      isShowMore: true,
      ruleForm: {
        name: "",
		contractAmount:"",
        caseType: "",
        caseNumber: "",
        caseCause: {
            id: '',
            name: ''
        },
		modeType: {
		    id: '',
		    name: ''
		},
		mtypeId:'',
		caseReason:'',
        caseName: "",
        price: "",
        entrustedTime: "",
        catererName: "",
        catererPhone: "",
        catererRoom: "",
        catererPost: "",
        catererAddress: "",
        catererRemark: "",
        registerDate: "",
        result: "",
        adjudicationDate: "",
        closingStatus: "",
        closingDate: "",
        filePerson: "",
        fileDate: "",
        keepingPlace: "",
        otherRemark: "",
        relatedClient: [],
        relatedCase: [],
        relatedProject: "",
        process: "",
        caseProgram: {
          // 案件程序
          id: "",
          name: "", // 名称
        },
        hostUser: {
          id: "",
          name: "",
        },
        concernPersonList: [
          {
            lawCase: {
              // 案件信息
              id: "",
            },
            type: "1", // 类型（个人、单位）
            isEntrust: "1", // 委托方（是/否）
            name: "", // 姓名/单位名称
            attribute: "", // 属性
            nation: "", // 民族
            sex: "1", // 性别
            phone: "", // 联系方式
            idNumber: "", // 证件号码
            address: "", // 住所地/单位地址
            legalRepresentative: "", // 法定代表人
            unifiedSocialCreditCode: "", // 统一社会信用代码
          },
        ],
        undertakePersonList: [
          {
            lawCase: {
              // 案件信息
              id: "",
            },
            name: "", // 姓名
            phone: "", // 联系方式
            department: "", // 科室
            post: "", // 职务
            address: "", // 联系地址
          },
        ],
      },
      rules: {
        caseType: [
          { required: true, message: "请选择案件类型", trigger: "change" },
        ],
		caseName: [
		  { required: true, message: "请输入案件名称", trigger: "change" },
		],
		"caseReason": [
		  { required: true, message: "请输入案由", trigger: "change" },
		],
		entrustedTime: [
		  { required: true, message: "请输入委托时间", trigger: "change" },
		],
		chargeType: [
		  { required: true, message: "请选择收费方式", trigger: "change" },
		],
		contractAmount: [
		  { required: true, message: "请输入合同金额", trigger: "change" },
		],
		"modeType.id": [
		  { required: true, message: "请选择模板类型", trigger: "change" },
		],
        process: [
          { required: true, message: "请选择审理程序", trigger: "change" },
        ],
        "hostUser.id": [
          { required: true, message: "请选择主办人员", trigger: "change" },
        ],
        price: [
          { required: true, message: "请输入案件标的", trigger: "blur" },
        ],
        name: [
          { required: true, message: "请输入当事人姓名", trigger: "blur" },
        ],
        concernPersonPhone: [
          { required: true, message: "请输入联系方式", trigger: "blur" },
          { type: "number", message: "请输入数值" },
        ],
      },
      briefList: [],
      caseCausList: [],
    };
  },
  mounted() {
    this.getAllBriefList();
    this.getcaseCause();
	this.getcaseModelType();
    // this.$watch("visible", (e) => {
    //   if (e) {
    //     this.getcaseCause();
    //   }
    // });
  },
  methods: {
	  getcaseModelType(){
	  		  caseModelTypeTemplateData().then((res) => {
				  this.caseModelTypeList = [{
				    id:'0',
				    name: '所有模板',
				    children: res.treeData
				  }] 
	  		  });
	  },
      handleCaseDesc(e) { 
          this.ruleForm.caseCause.id = e.id;
          this.ruleForm.caseCause.name = e.name;
		  this.ruleForm.caseReason=e.name;
          // this.caseCause = e.id;
      },
	  handleModeType(e) {
	      console.log(e);
	      this.ruleForm.modeType.id = e.id;
	      this.ruleForm.modeType.name = e.name;
	  		  this.ruleForm.mtypeId=e.id;
	      // this.caseCause = e.id;
	  },
    handleSelect(e) {
      this.briefList.map((item) => {
        if (item.id === e) {
          this.ruleForm.hostUser.name = item.name;
        }
      });
    },
    // 获取所有用户列表
    getAllBriefList() {
      allBriefList().then((res) => {
        this.briefList = res.data;
      });
    },
    // 获取审理程序列表
    handleSelectCaseType(e) {
      let formdata = new FormData();
      formdata.append("type", e);
      getCaseProgram(formdata).then((res) => {
        this.processList = res.treeData;
      });
    },
    // 获取案由列表
    getcaseCause() {
      caseCauseData().then((res) => {
        this.caseCausList = [{
          id:'0',
          name: '所有案由',
          children: res.treeData
        }]
      });
    },
    handleCaseProgram(event) {
      let data = this.$refs["progress"].getCheckedNodes()[0];
      this.ruleForm.caseProgram.id = data.value;
      this.ruleForm.caseProgram.name = data.label;
    },
    handleCaseCause(event) {
      let data = this.$refs["cause"].getCheckedNodes()[0];
      this.ruleForm.caseCause.id = data.value;
      this.ruleForm.caseCause.name = data.label;
    },
    handleConcernPerson(type, index) {
      if (type === "add") {
        this.ruleForm.concernPersonList.push({
          lawCase: {
            // 案件信息
            id: "",
          },
          type: "1", // 类型（个人、单位）
          isEntrust: "1", // 委托方（是/否）
          name: "", // 姓名/单位名称
          attribute: "", // 属性
          nation: "", // 民族
          sex: "1", // 性别
          phone: "", // 联系方式
          idNumber: "", // 证件号码
          address: "", // 住所地/单位地址
          legalRepresentative: "", // 法定代表人
          unifiedSocialCreditCode: "", // 统一社会信用代码
        });
      } else {
        this.ruleForm.concernPersonList.splice(index, 1);
      }
    },
    handleUndertaker(type, index) {
      if (type === "add") {
        this.ruleForm.undertakePersonList.push({
          lawCase: {
            // 案件信息
            id: "",
          },
          name: "", // 姓名
          phone: "", // 联系方式
          department: "", // 科室
          post: "", // 职务
          address: "", // 联系地址
        });
      } else {
        this.ruleForm.undertakePersonList.splice(index, 1);
      }
    },
    handleOk(formName) {
        console.log(this.ruleForm);
        this.$emit("ok", this.ruleForm);
      // this.$refs[formName].resetFields();
    },
    handleCancel() {
      this.$refs.ruleForm.resetFields();
      this.$emit("cancel");
    },selChargeType(e){
		 if(e==2){
			this.ruleForm.contractAmount=0;
		 }else{
		 this.ruleForm.contractAmount='';
		 }
	},
    querySearch() {},
  },
};
</script>

<style lang="less" scoped>
	 .el-row{
	 	margin-bottom: -5px;
	 	display: flex; 
	 }
	/deep/ .el-dialog__header {
	    padding: 10px 20px 10px;
	}
// 去掉弹框内容的默认padding值 
  /deep/ .el-dialog__body {
    padding: 0px;
    text-align: center;
  }
.flex-center {
  color: #409eff;
  cursor: pointer;
}
h3 {
  margin: 20px 0;
}
.ruleForm-box {
  // height: 70vh;
  overflow-y: scroll;
  padding: 0 20px;
}
.el-cascader {
  width: 100%;
}

</style>

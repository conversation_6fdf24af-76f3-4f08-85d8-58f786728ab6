<template>
  <el-dialog
    :title="model ? '编辑版本信息' : '新增版本信息'"
    :visible="visible"
    width="600px"
    :before-close="handleCancel"
    :close-on-press-escape="false"
    :close-on-click-modal="false"
  >
    <el-form
      :model="ruleForm"
      :rules="rules"
      ref="ruleForm"
      label-width="130px"
      class="ruleForm-box"
    >
      <el-row>
        <el-col :span="24">
          <el-form-item label="版本号" prop="number">
            <el-input
              placeholder="请输入版本号"
              v-model="ruleForm.number"
              type="text"
            >
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24" v-if="!model">
          <el-form-item label="安装包" prop="fileList" ref="file_Rule">
            <el-upload
              class="upload-demo"
              :action="uploadUrl"
              :on-success="handleUpload"
              :data="{ uploadPath: '/lawcase/version' }"
              :headers="header"
              accept=".apk"
              :file-list="ruleForm.fileList"
              :limit="1"
            >
              <el-button type="primary" size="mini" icon="el-icon-upload"
                >上传</el-button
              >
            </el-upload>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="内容介绍" prop="content">
            <el-input
              placeholder="请输入内容介绍"
              v-model="ruleForm.content"
              type="text"
            >
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="是否启用" prop="isEnable">
            <el-switch v-model="ruleForm.isEnable"> </el-switch>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleCancel">取 消</el-button>
      <el-button type="primary" @click="handleOk('ruleForm')">提交</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { isEmpty, cloneDeep } from "lodash";
import storage from "store";

export default {
  name: "CreateStage",
  props: {
    visible: {
      type: Boolean,
      required: true,
    },
    type: {
      type: String,
    },
    loading: {
      type: Boolean,
      default: () => false,
    },
    model: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    const dictList = storage.get("dictList");
    const baseUrl = process.env.VUE_APP_API_BASE_URL;
    return {
      caseTypeList: dictList.case_type,
      ruleForm: {
        number: "",
        fileList: [],
        content: "",
        isEnable: false,
      },
      rules: {
        number: [{ required: true, message: "请输入版本号", trigger: "blur" }],
        fileList: [{ required: true, message: "请上传安装包", trigger: "change" }],
      },
      disabled: false,
      header: {
        token: storage.get("token"),
      },
      uploadUrl: `${baseUrl ? baseUrl : ""}/law/sys/file/webupload/upload`,
    };
  },
  mounted() {
    this.$watch(
      "visible",
      (e) => {
        if (e) {
          if (this.model) {
            this.ruleForm = JSON.parse(JSON.stringify(this.model));
            this.ruleForm.isEnable = this.ruleForm.isEnable === '1'? true : false
          }
        } else {
          this.disabled = false;
          this.ruleForm = {
            number: "",
            fileList: [],
            content: "",
            isEnable: false,
          };
        }
      },
      {
        deep: true,
        immediate: true,
      }
    );
  },
  methods: {
    handleOk() {
      this.$emit("ok", this.ruleForm);
    },
    handleCancel() {
      this.$emit("cancel");
    },
    handleUpload(res, file, fileList) {
      if (res.success) {
        this.ruleForm.fileList.push(res.url)
        this.$refs.ruleForm.validateField('fileList')
        console.log(this.ruleForm);

        // this.ruleForm.url = res.url;
      } else {
        this.$message.error("上传失败");
      }
    },
  },
};
</script>

<style>
</style>
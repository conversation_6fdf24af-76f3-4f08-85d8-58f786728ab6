<template>
  <div class="block">
    <h3 class="title">版本管理</h3>
    <el-button
      type="primary"
      icon="el-icon-circle-plus-outline"
      @click="() => append('add')"
    >
      新增版本
    </el-button>
    <el-divider></el-divider>
    <el-table
      v-loading="loading"
      ref="singleTable"
      :data="tableData"
      style="width: 100%"
    >
      <el-table-column property="number" label="版本号"> </el-table-column>
      <el-table-column
        property="publishDate"
        label="发布日期"
      ></el-table-column>
      <el-table-column property="content" label="内容介绍"></el-table-column>
      <el-table-column label="是否启用">
        <template slot-scope="scope">
          <span>{{ scope.row.isEnable === "1" ? "是" : "否" }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="100">
        <template slot-scope="scope">
          <el-button @click="append('edit', scope.row)" type="text" size="small"
            >编辑</el-button
          >
          <el-button type="text" size="small" @click="remove(scope.row)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <div class="block" style="margin-top: 15px">
      <el-pagination
        background
        align="center"
        @size-change="handleSizeChange"
        @current-change="handleCurrentSizeChange"
        :current-page="currentPage"
        :page-sizes="[1, 5, 10, 20]"
        :page-size="pageSize"
        layout="prev, pager, next"
        :total="total"
      >
      </el-pagination>
    </div>
    <CreateModal
      ref="createForm"
      :visible="visible"
      :model="model"
      @cancel="handleCancel"
      @ok="handleOk"
    />
  </div>
</template>

<script>
import { getList, add, del } from "@/api/case/version";
import CreateModal from "./modal/CreateModal.vue";
import storage from "store";
import moment from "moment";
export default {
  name: "version",
  components: {
    CreateModal,
  },
  data() {
    return {
      tableData: [],
      total: 0,
      currentPage: 1, // 当前页码
      pageSize: 10, // 每页的数据条数
      visible: false,
      model: null,
    };
  },
  created() {
    this.getVersionList();
  },
  methods: {
    getVersionList() {
      this.loading = true;
      let formData = new FormData();
      formData.append("pageNo", this.currentPage);
      formData.append("pageSize", this.pageSize);
      getList(formData).then((res) => {
        this.loading = false;
        this.tableData = res.page.list;
        this.total = res.page.count
      });
    },
    //每页条数改变时触发 选择一页显示多少行
    handleSizeChange(val) {
      this.currentPage = 1;
      this.pageSize = val;
    },
    //当前页改变时触发 跳转其他页
    handleCurrentSizeChange(val) {
      this.currentPage = val;
      this.getCaseList();
    },
    append(type, data) {
      this.type = type;
      if (type === "add") {
        this.model = null;
      } else {
        this.model = data;
      }
      this.visible = true;
    },
    handleCancel() {
      this.visible = false;
    },
    handleOk() {
      this.$refs.createForm.$refs.ruleForm.validate((valid) => {
        if (valid) {
          let data = this.$refs.createForm.ruleForm;
          let formData = new FormData();
          formData.append("number", data.number);
          formData.append("isEnable", data.isEnable?'1':'0');
          formData.append("path", data.path || data.fileList[0]);
          formData.append("content", data.content);
          formData.append(
            "publishDate",
            moment(new Date()).format("YYYY-MM-DD")
          );
          formData.append("id", data.id || '');
          add(formData).then((res) => {
            this.visible = false;
            if (res.success) {
              this.$message({
                showClose: true,
                message: res.msg,
                type: "success",
              });
              this.getVersionList();
            } else {
              this.$message.error(res.msg);
            }
          });
        } else {
          return false;
        }
      });
    },
    remove(data) {
      this.$confirm("确定删除该版本?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          let param = new FormData();
          param.append("ids", data.id);
          del(param).then((res) => {
            if (res.success) {
              this.getVersionList();
              this.$message({
                type: "success",
                message: "删除成功!",
              });
            } else {
              this.$message.error(res.msg);
            }
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
  },
};
</script>

<style lang="less" scoped>
.block {
  padding: 20px;
  background: #fff;
  .title {
    padding: 20px 0;
  }
  /deep/.el-tree-node__content {
    padding: 5px 0;
  }
  .custom-tree-node {
    width: 50%;
    display: flex;
    //   justify-content: space-between;
    .label {
      margin-right: 30px;
    }
  }
}
</style>
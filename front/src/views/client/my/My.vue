<template>
  <div>
    <div class="flex justify-between">
      <div class="flex">
        <el-button
            slot="reference"
            @click="handleShowCreateModal('add')"
            type="primary"
          >
            新建</el-button
          >
        <el-dropdown trigger="click" @command="handleSearch">
          <el-button plain icon="el-icon-arrow-down" style="margin-left: 20px"
            >{{customerTypeName?customerTypeName:'全部'}}</el-button
          >
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item  command="1">个人</el-dropdown-item>
            <el-dropdown-item  command="2">单位</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </div>
      <div class="flex align-center">
        <div class="search-input flex align-center">
          <el-input
            v-model="formLabelAlign.name"
            placeholder="请输入客户名称"
            style="border: none"
            class="input"
          ></el-input>
          <i class="el-icon-search" @click="getCustomerList"></i>
        </div>
        <el-popover placement="bottom" width="400" trigger="click">
          <el-form
            label-position="right"
            label-width="80px"
            :model="formLabelAlign"
          >
            <el-form-item label="客户名称">
              <el-input v-model="formLabelAlign.name"></el-input>
            </el-form-item>
            <el-form-item label="所属行业">
              <el-cascader style="width: 100%"
                v-model="formLabelAlign.industryVal"
                :options="industryList"
                :props="{ value: 'id', label: 'name' }"
                @change="handleSelectIndustry"
                ref="industry"
              ></el-cascader>
            </el-form-item>
            <el-form-item label="合作状态">
              <el-select
                style="width: 100%"
                v-model="formLabelAlign.status"
                placeholder="请选择合作状态"
              >
                <el-option
                  :label="item.label"
                  :value="item.value"
                  v-for="item in cooperate_status"
                  :key="item.id"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="currentPage = 1; getCustomerList()">开始检索</el-button>
              <el-button plain style="margin-left: 30px" @click="currentPage = 1; handleReset()">重置信息</el-button>
            </el-form-item>
          </el-form>
          <div class="high-wrap" slot="reference">
            <i class="el-icon-c-scale-to-original" style="font-size: 20px"></i
            >高级
          </div>
        </el-popover>

        <div class="top-header-divide"></div>
        <div @click="toggoleShow" style="margin-left: 20px">
          <i class="el-icon-s-operation" v-if="isTable"></i>
          <i class="el-icon-menu" v-else></i>
        </div>
      </div>
    </div>
    <div class="table-box">
      <el-table
        v-loading="loading"
        ref="singleTable"
        :data="tableData"
        :highlight-current-row="!isMultiple"
        @current-change="handleCurrentChange"
        style="width: 100%"
      >
        <el-table-column
          align="left"
          type="selection"
          width="55"
          v-if="isMultiple"
        >
        </el-table-column>
        <el-table-column align="left">
          <template slot="header" slot-scope="scope">
            <div style="text-align: center">
              <i class="el-icon-setting" style="font-size: 20px"></i>
            </div>
          </template>
          <template slot-scope="scope">
            <div v-if="scope.row.customer.importance === '1'" style="text-align:center;color:#ec5050">核心</div>
            <div v-if="scope.row.customer.importance === '2'" style="text-align:center;color:#ecb86c">重要</div>
            <div v-if="scope.row.customer.importance === '3'" style="text-align:center;color:#666">一般</div>
            <div v-if="scope.row.customer.importance === '4'" style="text-align:center;color:#999">次要</div>
          </template>
        </el-table-column>

        <el-table-column property="customer.statusName" label="状态">
        </el-table-column>
        <el-table-column property="customer.number" label="客户编号">
        </el-table-column>
        <el-table-column property="customer.typeName" label="客户名称/标识">
          <template slot-scope="scope">
            ({{scope.row.customer.typeName}}){{scope.row.customer.name}}
          </template>
        </el-table-column>
        <el-table-column
          property="customer.contractStartDate"
          label="合同起始日"
        >
        </el-table-column>
        <el-table-column property="customer.contractEndDate" label="合同终止日">
        </el-table-column>
        <!-- <el-table-column property="todo" label="待办事项"> -->
          <!-- <template slot-scope="scope">
            <div v-if="scope.row.todoInfoList.length === 1">
              <span class="mr10">{{
                scope.row.todoInfoList[0].startDate
              }}</span>
              <span>{{ scope.row.todoInfoList[0].title }}</span>
            </div>
            <div v-if="scope.row.todoInfoList.length > 1">
              <el-tooltip placement="top">
                <div slot="content">
                  <div
                    v-for="(item, index) in scope.row.todoInfoList"
                    :key="item.id"
                  >
                    <span class="mr10">{{ item.startDate }}</span>
                    <span>{{ item.name }}</span>
                  </div>
                </div>
                <span>{{ scope.row.todoInfoList.length }}条</span>
              </el-tooltip>
            </div>
          </template> -->
        <!-- </el-table-column> -->
        <el-table-column property="customer.followUpNames" label="跟进人">
        </el-table-column>
        <el-table-column label="操作" fixed="right">
          <template slot-scope="scope">
            <div @click.stop="">
              <el-dropdown
                trigger="click"
                @command="handleCommand($event, scope)"
              >
                <i class="el-icon-more"></i>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item command="edit"
                    >修改基本信息</el-dropdown-item
                  >
                  <!-- <el-dropdown-item command="team">移至团队</el-dropdown-item> -->
                  <el-dropdown-item command="delete">删除</el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <div class="block" style="margin-top: 15px">
        <el-pagination
          background
          align="center"
          @size-change="handleSizeChange"
          @current-change="handleCurrentSizeChange"
          :current-page="currentPage"
          :page-sizes="[1, 5, 10, 20]"
          :page-size="pageSize"
          layout="prev, pager, next"
          :total="total"
        >
        </el-pagination>
      </div>
      <div class="footer-box">
        <el-switch v-model="isMultiple" @change="toggleMultiple"> </el-switch>
        开启批量操作

        <el-dropdown trigger="click" @command="handleCommand">
          <el-button plain icon="el-icon-arrow-down" style="margin-left: 30px"
            >更多批量操作</el-button
          >
          <el-dropdown-menu slot="dropdown">
            <!-- <el-dropdown-item :disabled="!isMultiple" command="edit"
              >修改案件基本信息</el-dropdown-item
            > -->
            <!-- <el-dropdown-item :disabled="!isMultiple"
              >移至团队</el-dropdown-item
            > -->
            <el-dropdown-item :disabled="!isMultiple" command="delete"
              >删除</el-dropdown-item
            >
          </el-dropdown-menu>
        </el-dropdown>
      </div>
    </div>
    <CreateCustomer
      ref="customerForm"
      :visible="createVisible"
      :id="createModalMdlId"
      @cancel="handleCreateCancel('createForm')"
      @ok="handleCreateOk('createForm')"
    />
  </div>
</template>

<script>
import { mapState, mapMutations } from "vuex";
import CreateCustomer from "./modal/CreateCustomer.vue";
import storage from "store";
const dictList = storage.get("dictList");
const customer_type = dictList.customer_type;
const cooperate_status = dictList.cooperate_status;
import {
  getList,
  add,
  deleteCustomer,
  edit,
  industryData,
} from "@/api/customer/customer";
export default {
  name: "My",
  components: {
    CreateCustomer,
  },
  data() {
    return {
      input: "",
      formLabelAlign: {
        name: "",
        status: "",
        industryVal: "",
        industry: {
          id: ''
        },
      },
      isTable: true,
      tableData: [],
      total: 0,
      isMultiple: false,
      createModalMdlId: null,
      createVisible: false,
      currentPage: 1, // 当前页码
      pageSize: 10, // 每页的数据条数
      loading: false,
      cooperate_status,
      industryList: [],
      customerType: '',
      customerTypeName: '',
    };
  },
  mounted() {
    this.getCustomerList();
    this.getIndustryData()
  },
  methods: {
    ...mapMutations(["SET_CUSTOMER"]),
    //选择行业
    handleSelectIndustry(e) {
      let data = this.$refs["industry"].getCheckedNodes()[0];
      this.formLabelAlign.industry.id = data.value;
      this.formLabelAlign.industry.name = data.label;
    },
    //获取所属行业列表
    getIndustryData(e) {
      industryData().then((res) => {
        this.industryList = res.treeData;
      });
    },
    //每页条数改变时触发 选择一页显示多少行
    handleSizeChange(val) {
      this.currentPage = 1;
      this.pageSize = val;
    },
    //当前页改变时触发 跳转其他页
    handleCurrentSizeChange(val) {
      this.currentPage = val;
      this.getCustomerList()
    },
    //重置
    handleReset(){
      this.formLabelAlign = {
        name: "",
        status: "",
        industryVal: "",
        industry: {
          id: ''
        },
      }
      this.getCustomerList()
    },
    getCustomerList() {
      this.loading = true;
      let formData = new FormData()
      formData.append('pageNo', this.currentPage)
      formData.append('pageSize', this.pageSize)
      formData.append('type', this.customerType)
      formData.append('name', this.formLabelAlign.name)
      formData.append('status', this.formLabelAlign.status)
      formData.append('industry.id', this.formLabelAlign.industry.id)
      getList(formData).then((res) => {
        this.loading = false;
        this.tableData = res.page.list;
        this.total = res.page.count
        this.tableData.map((item) => {
          cooperate_status.map((child) => {
            if (item.customer.status === child.value) {
              item.customer.statusName = child.label;
            }
          });
          customer_type.map((child) => {
            if (item.customer.type === child.value) {
              item.customer.typeName = child.label;
            }
          });
        });
      });
    },
    toggoleShow() {
      this.isTable = !this.isTable;
    },
    handleCurrentChange(e) {
      this.SET_CUSTOMER(e.customer);
      this.$router.push({
        path: "/client/detail",
        query: { id: e.customer.id },
      });
    },
    handleShowSetting() {
    },
    // 开启批量操作
    toggleMultiple(e) {
    },
    // 显示新建客户弹框
    handleShowCreateModal(type, scope) {
      this.type = type;
      this.createVisible = true;
      if (type === "edit") {
        this.createModalMdlId = scope.row.customer.id;
      } else {
        this.createModalMdlId = null;
      }
    },
    // 确认新建客户
    handleCreateOk() {
      this.createVisible = false;
      const data = this.$refs.customerForm.ruleForm;
      const param = {
        customer: {
          address: data.address, // 住所地/单位地址
          contractEndDate: data.contractEndDate, // 合同结束日期
          contractStartDate: data.contractStartDate, // 合同开始日期
          id: data.id,
          idNumber: data.idNumber, // 证件号码
          importance: data.importance, // 客户重要性  字典：customer_importance
          industry: data.industry,
          legalRepresentative: data.legalRepresentative, // 法定代表人
          name: data.name, // 姓名/单位名称
          nation: data.nation, // 民族
          number: data.number, // 客户编号
          phone: data.phone, // 联系电话
          remarks: data.remarks, // 备注
          source: data.source, // 客户来源 字典：customer_source
          status: data.status, // 合作状态 字典：cooperate_status
          type: data.type, // 客户标识 字典：customer_type
          unifiedSocialCreditCode: data.unifiedSocialCreditCode, // 统一社会信用代码
          sex: data.sex, // 性别 字典：sex
        },
        customerFollowUpList: data.customerFollowUpList,
        customerContactsList: data.customerContactsList,
      };
      if (this.type === "add") {
        add(param).then((res) => {
          if (res.success) {
            this.$message({
              showClose: true,
              message: res.msg,
              type: "success",
            });
            this.getCustomerList();
          } else {
            this.$message.error(res.msg);
          }
        });
      } else {
        edit(param).then((res) => {
          if (res.success) {
            this.$message({
              showClose: true,
              message: res.msg,
              type: "success",
            });
            this.getCustomerList();
          } else {
            this.$message.error(res.msg);
          }
        });
      }
    },
    // 取消新建案件
    handleCreateCancel(formName) {
      // this.$refs[formName].ruleForm.resetFields();
      this.createVisible = false;
    },
    // 显示修改信息弹框
    handleShowEditModal(scope) {
      this.editVisible = true;
      this.editModalMdl = scope.row;
    },
    // 关闭修改信息弹框
    handleEditCancel() {
      this.editVisible = false;
    },
    // 删除案件
    handleDeleteCase(scope) {
      this.$confirm("此操作将永久删除该案件, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          let param = new FormData();
          param.append("ids", scope.row.lawCase.id);
          del(param).then((res) => {
            if (res.success) {
              this.getCustomerList();
              this.$message({
                type: "success",
                message: "删除成功!",
              });
            } else {
              this.$message.error(res.msg);
            }
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    // 点击操作下拉菜单
    handleCommand(command, scope) {
      switch (command) {
        case "edit": // 修改基本信息
          this.handleShowCreateModal("edit", scope);
          break;
        case "delete": //删除
          this.handleDeleteCase(scope);
          break;
        default:
          break;
      }
    },
    handleSearch(command){
      this.currentPage = 1
      this.customerType = command 
      this.customerTypeName = command==='1'?'个人':'单位'
      this.getCustomerList()

    },
    handleDegree(command, index) {
      this.tableData[index].degree = command;
    },
  },
};
</script>

<style lang="less" scoped>
.search-input {
  border-bottom: 1px solid #dcdfe6;
}
.input {
  /deep/.el-input__inner {
    border: none;
    background: none;
  }
}
.high-wrap {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin-left: 10px;
  margin-right: 20px;
  height: 34px;
  cursor: pointer;
}
.top-header-divide {
  width: 1px;
  height: 20px;
  background: #c1c4d3;
}
/deep/.el-date-editor {
  width: 100%;
}
.table-box {
  background: #fff;
  margin-top: 30px;
  .footer-box {
    padding: 20px;
  }
}
/deep/.el-table__row {
  cursor: pointer;
}
.degree-dot {
  width: 10px;
  height: 10px;
  border-radius: 50%;
}
.red {
  background: #ec5050;
}
.orange {
  background: #ffa836;
}
.grey {
  background: #bcbcbc;
}
</style>
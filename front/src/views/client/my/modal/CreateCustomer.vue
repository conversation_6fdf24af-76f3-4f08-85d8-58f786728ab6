<template>
  <el-dialog
    title="新建客户"
    :visible="visible"
    width="60%"
    :before-close="handleCancel"
    :close-on-press-escape="false"
    :close-on-click-modal="false"
  >
    <el-form
      :model="ruleForm"
      :rules="rules"
      ref="ruleForm"
      label-width="130px"
      class="ruleForm-box"
    >
      <h3>客户基本信息</h3>
      <el-row>
        <el-col :span="12">
          <el-form-item label="客户编号" prop="number">
            <el-input type="text" v-model="ruleForm.number"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="客户标识" prop="type">
            <el-radio-group v-model="ruleForm.type">
              <el-radio
                :label="item.value"
                v-for="(item, index) in customer_type"
                :key="index"
                >{{ item.label }}</el-radio
              >
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="合同起始时间" prop="contractStartDate">
            <el-date-picker
              v-model="ruleForm.contractStartDate"
              type="date"
              placeholder="选择日期"
              value-format="yyyy-MM-dd HH:mm:ss"
            >
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="合同结束时间" prop="contractEndDate">
            <el-date-picker
              v-model="ruleForm.contractEndDate"
              type="date"
              placeholder="选择日期"
              value-format="yyyy-MM-dd HH:mm:ss"
            >
            </el-date-picker>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="跟进人" prop="followw">
            <el-select
              v-model="ruleForm.followw"
              placeholder="请选择"
              multiple
              @change="handleSelectUser"
            >
              <el-option
                :label="item.name"
                :value="item.id"
                v-for="item in briefList"
                :key="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="所属行业" prop="industry">
            <el-cascader
              v-model="ruleForm.industryVal"
              :options="industryList"
              :props="{ value: 'id', label: 'name' }"
              @change="handleSelectIndustry"
              ref="industry"
            ></el-cascader>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="合作状态" prop="status">
            <el-select v-model="ruleForm.status" placeholder="请选择">
              <el-option
                :label="item.label"
                :value="item.value"
                v-for="item in cooperate_status"
                :key="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="客户来源" prop="source">
            <el-select v-model="ruleForm.source" placeholder="请选择">
              <el-option
                :label="item.label"
                :value="item.value"
                v-for="item in customer_source"
                :key="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item
            :label="ruleForm.type === '1' ? '姓名' : '单位姓名'"
            prop="name"
          >
            <el-input type="text" v-model="ruleForm.name"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="客户重要性" prop="importance">
            <el-select v-model="ruleForm.importance" placeholder="请选择">
              <el-option
                :label="item.label"
                :value="item.value"
                v-for="item in customer_importance"
                :key="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <div v-if="ruleForm.type === '1'">
        <el-row>
          <el-col :span="12">
            <el-form-item label="民族" prop="nation">
              <el-input type="text" v-model="ruleForm.nation"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="性别" prop="sex">
              <el-radio-group v-model="ruleForm.sex">
                <el-radio
                  :label="item.value"
                  v-for="(item, index) in sexList"
                  :key="index"
                  >{{ item.label }}</el-radio
                >
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="联系方式" prop="phone">
              <el-input type="text" v-model="ruleForm.phone"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="证件号码" prop="idNumber">
              <el-input type="text" v-model="ruleForm.idNumber"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="住所地" prop="address">
              <el-input type="text" v-model="ruleForm.address"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </div>
      <div v-else>
        <el-row>
          <el-col :span="24">
            <el-form-item label="单位地址" prop="address">
              <el-input type="text" v-model="ruleForm.address"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="法定代表人" prop="legalRepresentative">
              <el-input
                type="text"
                v-model="ruleForm.legalRepresentative"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="联系方式" prop="phone">
              <el-input type="text" v-model="ruleForm.phone"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item
              label="统一社会信用代码"
              prop="unifiedSocialCreditCode"
            >
              <el-input
                type="text"
                v-model="ruleForm.unifiedSocialCreditCode"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </div>
      <el-row>
        <el-col :span="24">
          <el-form-item label="备注" prop="remarks">
            <el-input v-model="ruleForm.remarks" type="textarea" :rows="2">
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <div v-if="!id">
        <div class="flex-center" @click="isShowMore = true" v-if="!isShowMore">
          添加更多客户信息<i class="el-icon-arrow-down"></i>
        </div>
        <div v-if="isShowMore">
          <h3>联系人信息</h3>
          <div
            v-for="(item, index) in ruleForm.customerContactsList"
            :key="index"
          >
            <el-row>
              <el-col :span="12">
                <el-form-item label="姓名" prop="nation">
                  <el-input type="text" v-model="item.name"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="性别" prop="sex">
                  <el-radio-group v-model="item.sex">
                    <el-radio
                      :label="item.value"
                      v-for="(item, index) in sexList"
                      :key="index"
                      >{{ item.label }}</el-radio
                    >
                  </el-radio-group>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="邮箱" prop="email">
                  <el-input type="text" v-model="item.email"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="联系电话" prop="phone">
                  <el-input type="text" v-model="item.phone"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="职务" prop="post">
                  <el-input type="text" v-model="item.post"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="地址" prop="address">
                  <el-input type="text" v-model="item.address"></el-input>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row>
              <el-col :span="24">
                <el-form-item label="备注" prop="remarks">
                  <el-input v-model="item.remarks" type="textarea" :rows="2">
                  </el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <div style="text-align: right">
              <i
                v-if="index === 0"
                class="el-icon-circle-plus-outline cursor primary"
                style="font-size: 14px"
                @click="handleConcernPerson('add')"
                >添加联系人</i
              >
              <i
                v-else
                class="el-icon-remove-outline cursor"
                style="font-size: 14px; color: red"
                @click="handleConcernPerson('del', index)"
                >删除联系人</i
              >
            </div>
          </div>
        </div>
      </div>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleCancel('ruleForm')">取 消</el-button>
      <el-button type="primary" @click="handleOk('ruleForm')"
        >立即提交</el-button
      >
    </span>
  </el-dialog>
</template>

<script>
import storage from "store";
import { allBriefList } from "@/api/login";
import { industryData } from "@/api/customer/customer";
import { queryById } from "@/api/customer/customer";
export default {
  name: "index",
  props: {
    visible: {
      type: Boolean,
      required: true,
    },
    loading: {
      type: Boolean,
      default: () => false,
    },
    id: {
      type: String,
    },
  },
  data() {
    const dictList = storage.get("dictList");
    return {
      sexList: dictList.sex,
      customer_type: dictList.customer_type,
      yes_no: dictList.yes_no,
      cooperate_status: dictList.cooperate_status,
      customer_source: dictList.customer_source,
      customer_importance: dictList.customer_importance,
      isShowMore: false,
      ruleForm: {
        // 客户信息
        address: "", // 住所地/单位地址
        contractEndDate: "", // 合同结束日期
        contractStartDate: "", // 合同开始日期
        id: "",
        idNumber: "", // 证件号码
        importance: "3", // 客户重要性  字典：customer_importance
        industry: {
          // 所属行业
          id: "",
          name: "",
        },
        legalRepresentative: "", // 法定代表人
        name: "", // 姓名/单位名称
        nation: "", // 民族
        number: "", // 客户编号
        phone: "", // 联系电话
        remarks: "", // 备注
        source: "", // 客户来源 字典：customer_source
        status: "", // 合作状态 字典：cooperate_status
        type: "1", // 客户标识 字典：customer_type
        unifiedSocialCreditCode: "", // 统一社会信用代码
        sex: "1", // 性别 字典：sex
        customerContactsList: [
          // 客户联系人
          {
            address: "", // 地址
            email: "", // 邮箱
            id: "",
            name: "", // 姓名
            phone: "", // 联系电话
            post: "", // 职务
            remarks: "", // 备注
            sex: "1", // 性别 字典：sex
          },
        ],
        followw: [],
        customerFollowUpList: []
      },
      rules: {
        name: [{ required: true, message: "请输入姓名", trigger: "blur" }],
        caseType: [
          { required: true, message: "请选择案件类型", trigger: "change" },
        ],
        status: [
          { required: true, message: "请选择合作状态", trigger: "change" },
        ],
        followw: [
          { required: true, message: "请选择跟进人", trigger: "change" },
        ],
        price: [
          { required: true, message: "请输入案件标的", trigger: "blur" },
          { type: "number", message: "请输入数值" },
        ],
      },
      briefList: [],
      industryList: [],
      customerInfo: "",
    };
  },
  mounted() {
    this.$watch("visible", (e) => {
      if (e && this.id) {
        this.getCustomerInfo(this.id);
      }
      if (e) {
        this.getAllBriefList();
        this.getIndustryData();
      }
    });
  },
  methods: {
    getCustomerInfo(id) {
      let formdata = new FormData();
      formdata.append("id", id);
      queryById(formdata).then((res) => {
        this.ruleForm = res.data.customer;
        let followw = res.data.customerFollowUpList.map(
          (item) => item.user.id
        );
        this.ruleForm = {
          ...this.ruleForm,
          followw
        }
        this.ruleForm.customerFollowUpList = res.data.customerFollowUpList;
        this.ruleForm.industryVal = res.data.customer.industry.parentIdsArr;
      });
    },
    handleSelectIndustry(e) {
      let data = this.$refs["industry"].getCheckedNodes()[0];
      this.ruleForm.industry.id = data.value;
      this.ruleForm.industry.name = data.label;
    },
    handleSelectUser(e) {
      let userList = [];
      e.length &&
        this.briefList.map((item) => {
          e.map((child) => {
            if (item.id === child) {
              userList.push({
                user: {
                  id: item.id,
                  name: item.name,
                },
              });
            }
          });
        });
      this.ruleForm.customerFollowUpList = userList;
    },
    // 获取所有用户列表
    getAllBriefList() {
      allBriefList().then((res) => {
        this.briefList = res.data;
      });
    },
    //获取所属行业列表
    getIndustryData(e) {
      industryData().then((res) => {
        this.industryList = res.treeData;
      });
    },
    handleConcernPerson(type, index) {
      if (type === "add") {
        this.ruleForm.customerContactsList.push({
          address: "", // 地址
          customer: {
            // 客户信息
            id: "",
            name: "",
          },
          email: "", // 邮箱
          id: "",
          name: "", // 姓名
          phone: "", // 联系电话
          post: "", // 职务
          remarks: "", // 备注
          sex: "1", // 性别 字典：sex
        });
      } else {
        this.ruleForm.customerContactsList.splice(index, 1);
      }
    },
    handleOk(formName) {
      this.$emit("ok");
      // this.$refs[formName].resetFields();
    },
    handleCancel() {
      this.$refs.ruleForm.resetFields();
      this.$emit("cancel");
    },
    querySearch() {},
  },
};
</script>

<style lang="less" scoped>
.flex-center {
  color: #409eff;
  cursor: pointer;
}
h3 {
  margin: 20px 0;
}
.ruleForm-box {
  // height: 70vh;
  overflow-y: scroll;
  padding: 0 20px;
}
</style>
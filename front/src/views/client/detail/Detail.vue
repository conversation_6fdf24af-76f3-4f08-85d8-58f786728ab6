<template>
  <div>
    <div class="flex justify-between align-center">
      <div class="flex align-center">
        <i class="el-icon-arrow-left" @click="$router.back()"></i>
        <h3>{{ customer.name }}</h3>
      </div>
      <div class="tab-list">
        <div
          v-for="(item, index) in tabList"
          :class="[
            'tab-item',
            index === actTab ? 'active-item' : 'normal-item',
          ]"
          :key="index"
          @click="changeTab(index)"
        >
          {{ item }}
        </div>
      </div>
    </div>
    <section v-if="actTab === 0"><ClientInfo /></section>
  </div>
</template>

<script>
import { mapState, mapMutations } from "vuex";
import ClientInfo from "./ClientInfo.vue";
import { queryById } from "@/api/customer/customer";

export default {
  name: "Detail",
  components: {
    ClientInfo
  },
  data() {
    return {
      tabList: ["客户记录"],
      actTab: 0
    };
  },
  computed: mapState({
    customer: (state) => state.customer.customer,
  }),
  mounted() {
    this.id = this.$route.query.id;
    // this.getCustomerInfo();
  },
  methods: {
    ...mapMutations(['SET_CUSTOMER']),
    changeTab(index) {
      this.actTab = index;
    },
    getCustomerInfo() {
      let formData = new FormData();
      formData.append("id", this.id);
      queryById(formData).then((res) => {
        let customer = res.data.customer;
        this.SET_CUSTOMER(customer)
      });
    },
  },
};
</script>

<style lang="less" scoped>
.el-icon-arrow-left {
  font-size: 22px;
  padding: 0 10px;
  cursor: pointer;
}
.tab-list {
  display: flex;
  .tab-item {
    height: 28px;
    line-height: 28px;
    margin: 0 10px;
    cursor: pointer;
  }
  .normal-item:hover {
    color: #409eff;
  }
  .active-item {
    background: #409eff;
    color: #fff;
    padding: 0 15px;
    border-radius: 15px;
  }
}
</style>
<template>
  <div class="detail-info">
    <el-card class="info-box">
      <div class="flex align-center justify-between">
        <div>
          <i class="el-icon-notebook-2 mr10"></i>
          <span>客户详情</span>
        </div>
        <div class="primary cursor" @click="handleShowEditModal">
          <i class="el-icon-edit mr10"></i>
          <span>编辑案情</span>
        </div>
      </div>
      <el-row class="mg10">
        <el-col :span="6">
          <div class="new-info-item">
            <span class="item-title">客户编号：</span>
            <span class="item-value">{{ customerInfo.number }}</span>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="new-info-item">
            <span class="item-title">合作状态：</span>
            <span class="item-value">{{ customerInfo.statusName }}</span>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="new-info-item">
            <span class="item-title">合同起始日期：</span>
            <span class="item-value">{{ customerInfo.contractStartDate }}</span>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="new-info-item">
            <span class="item-title">合同终止日期：</span>
            <span class="item-value">{{ customerInfo.contractEndDate }}</span>
          </div>
        </el-col>
      </el-row>
      <el-row class="mg10">
        <el-col :span="6">
          <div class="new-info-item">
            <span class="item-title">客户来源：</span>
            <span class="item-value">{{ customerInfo.sourceName }}</span>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="new-info-item">
            <span class="item-title">所属行业：</span>
            <span class="item-value">{{ customerInfo.industry.name }}</span>
          </div>
        </el-col>
      </el-row>
      <div v-if="customerInfo.type === '1'">
        <el-row class="mg10">
          <el-col :span="6">
            <div class="new-info-item">
              <span class="item-title">姓名：</span>
              <span class="item-value">{{ customerInfo.name }}</span>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="new-info-item">
              <span class="item-title">性别：</span>
              <span class="item-value">{{
                customerInfo.sex === "1" ? "男" : "女"
              }}</span>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="new-info-item">
              <span class="item-title">民族：</span>
              <span class="item-value">{{ customerInfo.nation }}</span>
            </div>
          </el-col>
        </el-row>
        <el-row class="mg10">
          <el-col :span="6">
            <div class="new-info-item">
              <span class="item-title">联系方式：</span>
              <span class="item-value">{{ customerInfo.phone }}</span>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="new-info-item">
              <span class="item-title">证件号码：</span>
              <span class="item-value">{{ customerInfo.idNumber }}</span>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="new-info-item">
              <span class="item-title">住所地：</span>
              <span class="item-value">{{ customerInfo.address }}</span>
            </div>
          </el-col>
        </el-row>
      </div>
      <div v-else>
        <el-row class="mg10">
          <el-col :span="6">
            <div class="new-info-item">
              <span class="item-title">单位名称：</span>
              <span class="item-value">{{ customerInfo.name }}</span>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="new-info-item">
              <span class="item-title">统一社会信用代码：</span>
              <span class="item-value">{{
                customerInfo.unifiedSocialCreditCode
              }}</span>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="new-info-item">
              <span class="item-title">注册地址：</span>
              <span class="item-value">{{ customerInfo.address }}</span>
            </div>
          </el-col>
        </el-row>
        <el-row class="mg10">
          <el-col :span="6">
            <div class="new-info-item">
              <span class="item-title">联系方式：</span>
              <span class="item-value">{{ customerInfo.phone }}</span>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="new-info-item">
              <span class="item-title">法定代表人：</span>
              <span class="item-value">{{
                customerInfo.legalRepresentative
              }}</span>
            </div>
          </el-col>
        </el-row>
      </div>
      <el-row class="mg10">
        <el-col :span="24">
          <div class="new-info-item">
            <span class="item-title">客户备注：</span>
            <span class="item-value">{{ customerInfo.remarks }}</span>
          </div>
        </el-col>
      </el-row>
    </el-card>
    <div class="detail-bottom">
      <el-card class="bottom-left">
        <div class="common-detail-tab">
          <div class="tab-item-wrap">
            <div
              :class="['tab-item', tabActive === index ? 'tab-active' : '']"
              v-for="(item, index) in tabList"
              :key="index"
              @click="changeTab(index)"
            >
              {{ item }}
            </div>
          </div>
          <div
            class="tab-operate primary"
            v-if="tabActive === 1"
            @click="handleShowTodoModal('add')"
          >
            <i class="el-icon-circle-plus-outline mr10"></i>
            <span>添加</span>
          </div>
          <div
            class="tab-operate primary"
            v-else-if="tabActive === 0"
            @click="handleShowClientModal('add')"
          >
            <i class="el-icon-circle-plus-outline mr10"></i>
            <span>添加联系人</span>
          </div>
          <div
            class="tab-operate primary"
            v-else
            @click="handleShowContractModal"
          >
            <i class="el-icon-circle-plus-outline mr10"></i>
            <span>新增合同</span>
          </div>
        </div>
        <div class="left-content" v-if="tabActive === 1">
          <div v-if="tableData.length">
            <el-table :data="tableData" style="width: 100%" :key="1">
              <el-table-column type="expand">
                <template slot-scope="props">
                  <el-row>
                    <el-col :span="4">
                      <span>民族:</span>
                      <span>{{ props.row.nation }}</span>
                    </el-col>
                    <el-col :span="4">
                      <span>性别:</span>
                      <span>{{ props.row.sex === "1" ? "男" : "女" }}</span>
                    </el-col>
                    <el-col :span="8">
                      <span>身份证号码:</span>
                      <span>{{ props.row.idNumber }}</span>
                    </el-col>
                    <el-col :span="8">
                      <span>住所地:</span>
                      <span>{{ props.row.address }}</span>
                    </el-col>
                    <el-col :span="7">
                      <span>备注:</span>
                      <span>{{ props.row.remark }}</span>
                    </el-col>
                  </el-row>
                </template>
              </el-table-column>
              <el-table-column label="类型" prop="type">
                <template slot-scope="scope">
                  <div>{{ scope.row.type === "1" ? "个人" : "单位" }}</div>
                </template>
              </el-table-column>
              <el-table-column label="名称" prop="name"> </el-table-column>
              <el-table-column label="委托方" prop="isEntrust">
                <template slot-scope="scope">
                  <div>{{ scope.row.isEntrust === "1" ? "是" : "否" }}</div>
                </template>
              </el-table-column>
              <el-table-column label="属性" prop="attribute"> </el-table-column>
              <el-table-column label="联系电话" prop="phone"> </el-table-column>
              <el-table-column label="地址" prop="address"> </el-table-column>
              <el-table-column label="操作">
                <template slot-scope="scope">
                  <div class="flex align-center">
                    <!-- <el-tooltip effect="dark" content="复制" placement="top">
                      <i class="el-icon-document-copy mr10 cursor" @click="handleShowClientModal('copy')"></i>
                    </el-tooltip> -->
                    <el-tooltip effect="dark" content="编辑" placement="top">
                      <i
                        class="el-icon-edit mr10 cursor"
                        @click="handleShowClientModal('edit', scope)"
                      ></i>
                    </el-tooltip>
                    <el-tooltip effect="dark" content="删除" placement="top">
                      <i
                        class="el-icon-delete cursor"
                        @click="handleDeleteClient(scope)"
                      ></i>
                    </el-tooltip>
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </div>
          <div v-else>
            <NoData />
          </div>
        </div>
        <div class="left-content" v-else-if="tabActive === 0">
          <div v-if="contactsList.length">
            <el-table :data="contactsList" style="width: 100%" key="2">
              <el-table-column prop="name" label="名称"> </el-table-column>
              <el-table-column prop="sex" label="性别">
                <template slot-scope="scope">
                  <span>{{ scope.row.sex === "1" ? "男" : "女" }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="post" label="职务"> </el-table-column>
              <el-table-column prop="phone" label="电话"> </el-table-column>
              <el-table-column prop="email" label="邮箱"> </el-table-column>
              <el-table-column prop="address" label="地址"> </el-table-column>
              <el-table-column prop="remarks" label="备注"> </el-table-column>
              <el-table-column label="操作">
                <template slot-scope="scope">
                  <span
                    class="cursor mr20"
                    style="color: #9fa5b9"
                    @click="handleShowClientModal('edit', scope)"
                    >编辑</span
                  >
                  <span
                    class="cursor"
                    style="color: #9fa5b9"
                    @click="handleDeleteClient(scope)"
                    >删除</span
                  >
                </template>
              </el-table-column>
            </el-table>
          </div>
          <div v-else>
            <NoData />
          </div>
        </div>
        <div class="left-content" v-else>
          <div v-if="contractTableData.length">
            <el-table :data="contractTableData" style="width: 100%" key="2">
              <el-table-column prop="name" label="案件名称"> </el-table-column>
              <el-table-column prop="process" label="序" width="180">
              </el-table-column>
              <el-table-column prop="result" label="审理结果">
              </el-table-column>
              <el-table-column prop="person" label="主办人员">
              </el-table-column>
              <el-table-column label="操作">
                <template slot-scope="scope">
                  <span class="cursor red" @click="handleCancelcontract"
                    >取消关联</span
                  >
                </template>
              </el-table-column>
            </el-table>
          </div>
          <div v-else>
            <NoData />
          </div>
        </div>
      </el-card>
      <div class="bottom-right">
        <el-card class="right-item">
          <div class="flex">
            <i class="el-icon-s-custom icon"></i>
            <span>跟进人管理</span>
          </div>
          <div
            class="content-link-wai flex align-center"
            v-for="item in customerFollowUpList"
            :key="item.id"
          >
            <div class="man-type man-orange">跟进</div>
            <div class="man-name">
              {{ item.user.name }}
            </div>
          </div>
        </el-card>
      </div>
    </div>
    <CreateCustomer
      ref="editForm"
      :visible="editVisible"
      :id="customerId"
      @cancel="handleEditCancel"
      @ok="handleEditOk"
    />
    <CreateContacts
      ref="clientForm"
      :visible="clientVisible"
      :model="clientModalMdl"
      @cancel="handleClientCancel"
      @ok="handleClientOk"
    />
    <CreateContract
      ref="contractForm"
      :visible="contractVisible"
      :model="contractModalMdl"
      @cancel="handlecontractCancel"
      @ok="handlecontractOk"
    />
    <CreateTodo
      ref="todoForm"
      :visible="todoVisible"
      :model="todoModalMdl"
      :relevanceType="'1'"
      @cancel="handleTodoCancel"
      @ok="handleTodoOk"
      :isCalendar="false"
    />
  </div>
</template>

<script>
import { mapMutations, mapState } from "vuex";

import CreateCustomer from "../my/modal/CreateCustomer.vue";
import CreateTodo from "../../case/detail/modal/CreateTodo.vue";
import CreateContacts from "./modal/CreateContacts.vue";
import CreateContract from "./modal/CreateContract.vue";
import NoData from "@/components/NoData";
import { allCustomerData, queryById, edit } from "@/api/customer/customer";
import {
  getContactsList,
  addContact,
  deleteContact,
  queryContactById,
} from "@/api/customer/contacts";

import {
  getPersonList,
  savePerson,
  delPerson,
  queryPerspnById,
} from "@/api/case/concernPerson";

import storage from "store";
const dictList = storage.get("dictList");
const customer_type = dictList.customer_type;
const cooperate_status = dictList.cooperate_status;
const customer_source = dictList.customer_source;
export default {
  name: "customer",
  components: {
    CreateCustomer,
    CreateContacts,
    NoData,
    CreateTodo,
    CreateContract,
  },
  data() {
    return {
      editVisible: false,
      editModalMdl: null,
      clientVisible: false,
      clientModalMdl: null,
      todoVisible: false,
      todoModalMdl: null,
      contractVisible: false,
      contractModalMdl: null,
      tabList: ["联系人"],
      tabActive: 0,
      tableData: [],
      clientVisibleType: "",
      contactsList: [],
      contractTableData: [],
      lawCase: {
        archiveUser: "",
        caseProgram: "",
      },
      allCustomerList: [],
      customerInfo: {
        industry: "",
      },
      customerId: "",
      customerFollowUpList: [],
    };
  },
  mounted() {
    this.id = this.$route.query.id;
    this.getcustomer();
    this.getContactsData();
  },
  computed: mapState({
    customer: (state) => state.customer.customer,
  }),
  methods: {
    ...mapMutations(["SET_CUSTOMER"]),
    // 获取客户列表
    getAllCustomerList() {
      allCustomerData().then((res) => {
        this.allCustomerList = res.data;
      });
    },
    // 获取案件信息
    getcustomer() {
      let formData = new FormData();
      formData.append("id", this.id);
      queryById(formData).then((res) => {
        this.customerInfo = res.data.customer;
        this.SET_CUSTOMER(this.customerInfo);
        this.editModalMdl = res.data.customer;
        this.customerFollowUpList = res.data.customerFollowUpList;
        customer_type.map((item) => {
          if (this.customerInfo.type === item.value) {
            this.customerInfo.typeName = item.label;
          }
        });
        cooperate_status.map((item) => {
          if (this.customerInfo.status === item.value) {
            this.customerInfo.statusName = item.label;
          }
        });
        customer_source.map((item) => {
          if (this.customerInfo.source === item.value) {
            this.customerInfo.sourceName = item.label;
          }
        });
      });
    },
    //获取当事人列表
    getConcernPerson() {
      let formData = new FormData();
      formData.append("lawCase.id", this.id);
      getPersonList(formData).then((res) => {
        this.tableData = res.page.list;
      });
    },
    //获取联系人列表
    getContactsData() {
      let formData = new FormData();
      formData.append("customer.id", this.id);
      getContactsList(formData).then((res) => {
        this.contactsList = res.page.list;
      });
    },
    changeTab(index) {
      this.tabActive = index;
      if (index === 0) {
        // this.getConcernPerson();
      } else if (index === 1) {
        this.getContactsData();
      } else {
      }
    },
    // 显示修改信息弹框
    handleShowEditModal() {
      this.editVisible = true;
      this.customerId = this.id;
    },
    // 确认修改信息
    handleEditOk() {
      const data = this.$refs.editForm.ruleForm;
      const param = {
        customer: {
          address: data.address, // 住所地/单位地址
          contractEndDate: data.contractEndDate, // 合同结束日期
          contractStartDate: data.contractStartDate, // 合同开始日期
          id: data.id,
          idNumber: data.idNumber, // 证件号码
          importance: data.importance, // 客户重要性  字典：customer_importance
          industry: data.industry,
          legalRepresentative: data.legalRepresentative, // 法定代表人
          name: data.name, // 姓名/单位名称
          nation: data.nation, // 民族
          number: data.number, // 客户编号
          phone: data.phone, // 联系电话
          remarks: data.remarks, // 备注
          source: data.source, // 客户来源 字典：customer_source
          status: data.status, // 合作状态 字典：cooperate_status
          type: data.type, // 客户标识 字典：customer_type
          unifiedSocialCreditCode: data.unifiedSocialCreditCode, // 统一社会信用代码
          sex: data.sex, // 性别 字典：sex
        },
        customerFollowUpList: data.customerFollowUpList,
      };
      edit(param).then((res) => {
        this.editVisible = false;
        if (res.success) {
          this.$message({
            showClose: true,
            message: res.msg,
            type: "success",
          });
          this.getcustomer();
        } else {
          this.$message.error(res.msg);
        }
      });
    },
    // 关闭修改信息弹框
    handleEditCancel() {
      this.editVisible = false;
    },
    // 显示新增联系人弹框
    handleShowClientModal(type, scope) {
      if (type === "add") {
        this.clientModalMdl = null;
      } else {
        this.clientModalMdl = scope.row;
      }
      this.clientVisible = true;
    },
    // 确认新增联系人
    handleClientOk() {
      let data = this.$refs.clientForm.ruleForm;
      const { name, sex, phone, address, email, id, post, remarks } = data;
      this.clientVisible = false;
      const param = {
        customer: {
          // 案件信息
          id: this.id,
        },
        name,
        sex,
        phone,
        address,
        email,
        id,
        post,
        remarks,
      };
      let formData = new FormData();
      formData.append("customer.id", this.id);
      formData.append("name", name);
      formData.append("sex", sex);
      formData.append("phone", phone);
      formData.append("address", address);
      formData.append("email", email);
      formData.append("post", post);
      formData.append("remarks", remarks);
      formData.append("id", id);

      addContact(formData).then((res) => {
        if (res.success) {
          this.$message({
            showClose: true,
            message: res.msg,
            type: "success",
          });
          this.getContactsData();
        } else {
          this.$message.error(res.msg);
        }
      });
    },
    // 关闭新增当事人弹框
    handleClientCancel() {
      this.clientVisible = false;
    },
    // 显示新增客户记录弹框
    handleShowTodoModal(type, item) {
      if (type === "edit") {
        this.todoModalMdl = item;
      } else {
        this.todoModalMdl = null;
      }
      this.todoVisible = true;
    },
    // 确认新增客户记录
    handleTodoOk() {
      this.todoVisible = false;
      return;
      let data = this.$refs.strategyForm.ruleForm;
      const { question, result, solution } = data;
      const param = {
        lawCase: {
          // 案件信息
          id: this.id,
        },
        id: data.id ? data.id : "",
        question,
        result,
        solution,
      };
      saveStrategy(param).then((res) => {
        if (res.success) {
          this.$message({
            showClose: true,
            message: res.msg,
            type: "success",
          });
          this.getStrategyData();
        } else {
          this.$message.error(res.msg);
        }
      });
    },
    // 关闭新增策略弹框
    handleTodoCancel() {
      this.todoVisible = false;
    },
    // 显示新增合同弹框
    handleShowContractModal() {
      this.contractVisible = true;
    },
    // 确认新增合同
    handlecontractOk() {
      this.contractVisible = false;
    },
    // 关闭新增合同弹框
    handlecontractCancel() {
      this.contractVisible = false;
    },
    //删除该当事人
    handleDeleteClient(scope) {
      this.$confirm("此操作将永久删除该联系人, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          let formData = new FormData();
          formData.append("ids", scope.row.id);
          deleteContact(formData).then((res) => {
            if (res.success) {
              this.getContactsData();
              this.$message({
                type: "success",
                message: "删除成功!",
              });
            }
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    // 删除策略
    handleDeleteStrategy(item) {
      this.$confirm("此操作将永久删除该办案策略, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          let formData = new FormData();
          formData.append("ids", item.id);
          delStrategy(formData).then((res) => {
            if (res.success) {
              this.getStrategyData();
              this.$message({
                type: "success",
                message: "删除成功!",
              });
            }
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    //取消关联
    handleCancelcontract() {
      this.$confirm("确认取消关联该案件?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.$message({
            type: "success",
            message: "删除成功!",
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
  },
};
</script>

<style lang="less" scoped>
.detail-info {
  height: calc(100vh - 165px);
  margin-top: 20px;
  .info-box {
    width: 100%;
    height: 220px;
    .new-info-item {
      font-size: 14px;
      .item-title {
        color: #858896;
      }
      .item-value {
        color: #303443;
        padding-right: 5px;
        flex: 1;
      }
    }
  }
  .detail-bottom {
    margin-top: 20px;
    display: flex;
    margin-bottom: 20px;
    .bottom-left {
      width: calc(100% - 400px);
      background: #fff;
      margin-right: 20px;
      border-radius: 6px;
      .common-detail-tab {
        display: flex;
        align-items: center;
        height: 56px;
        border-bottom: 1px solid #e1e4ee;
        .tab-item-wrap {
          flex: 1;
          display: flex;
          margin-left: 10px;
          .tab-active {
            color: #303443;
            font-weight: 700;
            border-bottom: 3px solid #303443;
          }
          .tab-item {
            width: 72px;
            text-align: center;
            font-size: 16px;
            color: #858896;
            height: 56px;
            line-height: 56px;
            margin-right: 40px;
            cursor: pointer;
          }
        }
        .tab-operate {
          font-size: 15px;
          cursor: pointer;
          margin-right: 20px;
        }
      }
    }
    .bottom-right {
      width: 420px;
      .right-item {
        height: 100%;
        min-height: 245px;
        background: #fff;
        border-radius: 6px;
        .icon {
          font-size: 22px;
          color: #9fa5b9;
        }
        .content-link-wai {
          padding: 0px 30px;
          margin-top: 30px;
          .man-type {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            color: #fff;
            font-size: 12px;
            text-align: center;
            line-height: 36px;
            background: #f6c379;
          }
          .man-name {
            padding-left: 10px;
            font-size: 14px;
            color: #303443;
            padding-right: 30px;
          }
        }
      }
    }
  }
}
.common-item {
  .common-gray-title {
    height: 40px;
    background: #f7f7fc;
    display: flex;
    align-items: center;
    padding: 0 20px;
    .title-txt {
      font-size: 16px;
      color: #303443;
      flex: 1;
      display: flex;
    }
  }
  .new-strategy {
    display: flex;
    font-size: 14px;
    line-height: 22px;
    margin-top: 20px;
    margin-bottom: 20px;
    padding: 0 20px;
    .title {
      color: #858896;
    }
    .txt {
      flex: 1;
      color: #303443;
      white-space: pre-line;
    }
  }
}
</style>
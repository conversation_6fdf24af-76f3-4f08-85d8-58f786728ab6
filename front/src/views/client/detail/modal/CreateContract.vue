<template>
  <el-dialog
    :title="model === 'edit' ? '编辑合同' : '新建合同'"
    :visible="visible"
    width="60%"
    :before-close="handleCancel"
    :close-on-press-escape="false"
    :close-on-click-modal="false"
  >
    <el-form
      :model="ruleForm"
      :rules="rules"
      ref="ruleForm"
      label-width="100px"
      class="ruleForm-box"
    >
      <el-row>
        <el-col :span="12">
          <el-form-item label="合同名称" prop="name">
            <el-input type="text" v-model="ruleForm.name"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="合同编号" prop="phone">
            <el-input type="text" v-model="ruleForm.phone"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="签约甲方" prop="email">
            <el-input type="text" v-model="ruleForm.email"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="签约乙方" prop="address">
            <el-input type="text" v-model="ruleForm.address"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="关联案件" prop="sex">
            <el-select v-model="ruleForm.hostUser" placeholder="请选择">
              <el-option label="张三" value="zhagsan"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="合同期限" prop="post">
            <el-input placeholder="" v-model.number="ruleForm.price">
              <template slot="append">年</template>
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="起始日期" prop="sex">
            <el-date-picker
              v-model="ruleForm.entrustedTime"
              type="date"
              placeholder="选择日期"
              value-format="yyyy-MM-dd HH:mm:ss"
            >
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="到期日期" prop="post">
            <el-date-picker
              v-model="ruleForm.entrustedTime"
              type="date"
              placeholder="选择日期"
              value-format="yyyy-MM-dd HH:mm:ss"
            >
            </el-date-picker>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="合同状态" prop="sex">
            <el-select v-model="ruleForm.hostUser" placeholder="请选择">
              <el-option label="张三" value="zhagsan"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="自动延期" prop="post">
            <el-input placeholder="" v-model.number="ruleForm.price">
              <template slot="append">年</template>
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="合作内容" prop="remarks">
            <el-input type="text" v-model="ruleForm.remarks"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="上传附件" prop="remarks" class="upload-box">
            <el-upload
              action="https://jsonplaceholder.typicode.com/posts/"
              :on-preview="handlePreview"
              :on-remove="handleRemove"
              :before-remove="beforeRemove"
              multiple
              :limit="3"
              :on-exceed="handleExceed"
              :file-list="fileList"
            >
              <el-button size="small" type="primary">点击上传</el-button>
              <!-- <div slot="tip" class="el-upload__tip">
                只能上传jpg/png文件，且不超过500kb
              </div> -->
            </el-upload>
            <span>上传的文件不能超过10M</span>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="重要条款" prop="remarks">
            <el-input type="textarea" v-model="ruleForm.remarks"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleCancel('ruleForm')">取 消</el-button>
      <el-button type="primary" @click="handleOk('ruleForm')"
        >立即提交</el-button
      >
    </span>
  </el-dialog>
</template>

<script>
import storage from "store";

export default {
  name: "index",
  props: {
    visible: {
      type: Boolean,
      required: true,
    },
    loading: {
      type: Boolean,
      default: () => false,
    },
    model: {
      type: Object,
      default: () => null,
    },
   
  },
  data() {
    const dictList = storage.get("dictList");

    return {
      sexList: dictList.sex,
      customer_type: dictList.customer_type,
      yes_no: dictList.yes_no,
      ruleForm: {
        name: "",
        type: "1",
        isEntrust: "1",
        attribute: "",
        nation: "",
        sex: "1",
        idNumber: "",
        phone: "",
        address: "",
        remarks: "",
        legalRepresentative: "",
        unifiedSocialCreditCode: "",
      },
      rules: {
        name: [
          { required: true, message: "请输入姓名", trigger: "blur" },
          { min: 3, max: 5, message: "长度在 3 到 5 个字符", trigger: "blur" },
        ],
        password: [
          { required: true, message: "请输入密码", trigger: "blur" },
          { min: 6, message: "长度至少为6位", trigger: "blur" },
        ],
      },
      fileList: [
      ],
    };
  },
  mounted() {
    // 当 model 发生改变时，为表单设置值
    this.$watch(
      "model",
      (e) => {
        if (e) {
          this.ruleForm = JSON.parse(JSON.stringify(this.model));
        }
      },
      {
        deep: true,
        immediate: true,
      }
    );
  },
  methods: {
    handleRemove(file, fileList) {
    },
    handlePreview(file) {
    },
    handleExceed(files, fileList) {
      this.$message.warning(
        `当前限制选择 3 个文件，本次选择了 ${files.length} 个文件，共选择了 ${
          files.length + fileList.length
        } 个文件`
      );
    },
    beforeRemove(file, fileList) {
      return this.$confirm(`确定移除 ${file.name}？`);
    },
    handleOk(formName) {
      this.$emit("ok", this.ruleForm);
      this.$refs[formName].resetFields();
    },
    handleCancel() {
      if (this.type === "add") {
        this.$refs.ruleForm.resetFields();
      }
      this.$emit("cancel");
    },
  },
};
</script>

<style scoped>
  .upload-box /deep/.el-form-item__content{
    display: flex;
  }
</style>
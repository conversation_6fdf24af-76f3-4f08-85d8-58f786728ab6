<template>
  <el-dialog
    title="关联案件"
    :visible="visible"
    width="700px"
    :before-close="handleCancel"
    :close-on-press-escape="false"
    :close-on-click-modal="false"
  >
    <el-form
      :model="ruleForm"
      :rules="rules"
      ref="ruleForm"
      label-width="100px"
      class="ruleForm-box"
    >
      <el-row :gutter="20">
        <el-col :span="19">
          <el-form-item label="关联案件" prop="title">
            <el-select
              v-model="ruleForm.relatedClient"
              placeholder="请选择关联案件"
            >
              <el-option label="区域一" value="shanghai"></el-option>
              <el-option label="区域二" value="beijing"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="5">
          <el-button type="primary">关联案件</el-button>
        </el-col>
      </el-row>
    </el-form>
    <div>
      <el-table :data="tableData" style="width: 100%">
        <el-table-column prop="name" label="案件名称">
        </el-table-column>
        <el-table-column prop="process" label="审理程序" width="180">
        </el-table-column>
        <el-table-column prop="result" label="审理结果"> </el-table-column>
        <el-table-column prop="person" label="主办人员"> </el-table-column>
        <el-table-column label="操作">
          <template slot-scope="scope">
            <span class="cursor red" @click="handleCancelRelated">取消关联</span>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button
        @click="
          () => {
            $emit('cancel');
          }
        "
        >取 消</el-button
      >
      <el-button type="primary" @click="handleOk">提交</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { isEmpty, cloneDeep } from "lodash";
export default {
  name: "RelatedCase",
  props: {
    visible: {
      type: Boolean,
      required: true,
    },
    loading: {
      type: Boolean,
      default: () => false,
    },
    model: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      ruleForm: {
        title: "",
      },
      rules: {
        title: [{ required: true, message: "请输入问题描述", trigger: "blur" }],
      },
      tableData:[]
    };
  },
  mounted() {
    // 当 model 发生改变时，为表单设置值
    this.$watch(
      "model",
      (e) => {
        if (!isEmpty(e)) {
          this.ruleForm = e;
        }
      },
      {
        deep: true,
        immediate: true,
      }
    );
  },
  methods: {
    handleOk() {
      this.$emit("ok", this.ruleForm);
      this.$nextTick(() => {
        this.ruleForm = {
          name: "",
        };
      });
    },
    handleCancel() {
      this.$emit("cancel");
      this.$nextTick(() => {
        this.ruleForm = {
          name: "",
        };
      });
    },
    //取消关联
    handleCancelRelated() {
      this.$confirm("确认取消关联该案件?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.$message({
            type: "success",
            message: "删除成功!",
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
  },
};
</script>

<style lang="less" scoped>
  .red{
    color: red;
  }
</style>
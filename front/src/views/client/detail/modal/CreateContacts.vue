<template>
  <el-dialog
    :title="model? '编辑联系人' : '新增联系人'"
    :visible="visible"
    width="60%"
    :before-close="handleCancel"
    :close-on-press-escape="false"
    :close-on-click-modal="false"
  >
    <el-form
      :model="ruleForm"
      :rules="rules"
      ref="ruleForm"
      label-width="100px"
      class="ruleForm-box"
    >
      <el-row>
        <el-col :span="12">
          <el-form-item label="姓名" prop="name">
            <el-input type="text" v-model="ruleForm.name"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="联系电话" prop="phone">
            <el-input type="text" v-model="ruleForm.phone"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="邮箱" prop="email">
            <el-input type="text" v-model="ruleForm.email"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="地址" prop="address">
            <el-input type="text" v-model="ruleForm.address"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="性别" prop="sex">
            <el-radio-group v-model="ruleForm.sex">
              <el-radio
                :label="item.value"
                v-for="(item, index) in sexList"
                :key="index"
                >{{ item.label }}</el-radio
              >
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="职务" prop="post">
            <el-input type="text" v-model="ruleForm.post"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="其他备注" prop="remarks">
            <el-input type="textarea" v-model="ruleForm.remarks"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleCancel('ruleForm')">取 消</el-button>
      <el-button type="primary" @click="handleOk('ruleForm')"
        >立即提交</el-button
      >
    </span>
  </el-dialog>
</template>

<script>
import storage from "store";

export default {
  name: "index",
  props: {
    visible: {
      type: Boolean,
      required: true,
    },
    loading: {
      type: Boolean,
      default: () => false,
    },
    model: {
      type: Object,
      default: () => null,
    },
  },
  data() {
    const dictList = storage.get("dictList");

    return {
      sexList: dictList.sex,
      customer_type: dictList.customer_type,
      yes_no: dictList.yes_no,
      ruleForm: {
        name: "",
        type: "1",
        isEntrust: "1",
        attribute: "",
        nation: "",
        sex: "1",
        idNumber: "",
        phone: "",
        address: "",
        remarks: "",
        legalRepresentative: "",
        unifiedSocialCreditCode: "",
        id: "",
      },
      rules: {
        name: [{ required: true, message: "请输入姓名", trigger: "blur" }],
        password: [
          { required: true, message: "请输入密码", trigger: "blur" },
          { min: 6, message: "长度至少为6位", trigger: "blur" },
        ],
      },
    };
  },
  mounted() {
    // 当 model 发生改变时，为表单设置值
    this.$watch(
      "visible",
      (e) => {
        if (e && this.model) {

          this.ruleForm = JSON.parse(JSON.stringify(this.model));
        } else {

          this.ruleForm = {
            name: "",
            type: "1",
            isEntrust: "1",
            attribute: "",
            nation: "",
            sex: "1",
            idNumber: "",
            phone: "",
            address: "",
            remarks: "",
            legalRepresentative: "",
            unifiedSocialCreditCode: "",
            id: "",
          };
        }
      },
      {
        deep: true,
        immediate: true,
      }
    );
  },
  methods: {
    handleOk(formName) {
      this.$emit("ok", this.ruleForm);
      this.$refs[formName].resetFields();
    },
    handleCancel() {
      if (this.type === "add") {
        this.$refs.ruleForm.resetFields();
      }
      this.$emit("cancel");
    },
  },
};
</script>

<style>
</style>
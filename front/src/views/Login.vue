<template>
  <div class="login qr-login">
    <div class="qr-login-wrap">
      <div class="login-form" style="padding: 0px; margin: 0px auto">
        <div class="user-input user-input-login">
          <img src="../assets/qr-logo.png" class="qr-logo" />
          <el-form
            :model="ruleForm"
            :rules="rules"
            ref="ruleForm"
            label-width="100px"
            class="ruleForm-box"
          >
            <el-form-item label="账号" prop="userName">
              <el-input v-model="ruleForm.userName"></el-input>
            </el-form-item>
            <el-form-item label="密码" prop="password">
              <el-input
                type="password"
                v-model="ruleForm.password"
                show-password
              ></el-input>
            </el-form-item>
            <!-- <el-form-item>
              <div style="text-align: right" class="forgot-cell">
                <span>忘记密码？|</span>
                <span style="margin-left: 10px">立即注册</span>
              </div>
            </el-form-item> -->
            <el-form-item>
              <el-button
                type="primary"
                @click="submitForm('ruleForm')"
                style="width: 100%"
                :loading="loading"
                >登录</el-button
              >
            </el-form-item>
          </el-form>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapActions } from "vuex";
import storage from "store";
import { getUserInfo, getAllDict } from "@/api/login";
export default {
  name: "Home",
  components: {},
  data() {
    return {
      ruleForm: {
        userName: "",
        password: "",
      },
      loading: false,
      rules: {
        userName: [
          { required: true, message: "请输入账号", trigger: "blur" },
          { min: 5, message: "请输入至少5个字符", trigger: "blur" },
        ],
        password: [
          { required: true, message: "请输入密码", trigger: "blur" },
          { min: 5, message: "长度至少为6位", trigger: "blur" },
        ],
      },
    };
  },
  methods: {
    ...mapActions(["Login", "Logout"]),
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.loading = true;
          let data = new FormData();
          data.append("userName", this.ruleForm.userName);
          data.append("password", this.ruleForm.password);
          this.Login(data).then((res) => {
            if (res.success) {
              storage.set("token", res.token, 7 * 24 * 60 * 60 * 1000);
              this.getUserInfo();
              this.getAllDict();
            } else {
              this.loading = false;
              this.$message.error(res.msg);
            }
          });
        } else {
          return false;
        }
      });
    },
    getAllDict() {
      getAllDict().then((res) => {
        storage.set("dictList", res.dictList);
      });
    },
    getUserInfo() {
      getUserInfo().then((res) => {
        if (res.success) {
          this.loginSuccess(res);
          storage.set("user", res.user);
        }
      });
    },
    loginSuccess() {
      // 延迟 1 秒显示欢迎信息
      setTimeout(() => {
        this.loading = false;
        this.$router.replace({ path: "/calendar" });
        this.$message({
          message: "欢迎回来",
          type: "success",
        });
      }, 1000);
    },
    requestFailed(err) {
      this.$message.error("请求出现错误，请稍后再试");
    },
  },
};
</script>
<style lang="less" scoped>
.login {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  height: 100vh;
  background-image: url(../assets/qr-login.jpg);
  background-repeat: no-repeat;
  background-size: cover;
  overflow: hidden;
  .qr-login-wrap {
    background: #fff;
    border-radius: 10px;
    box-shadow: 14px 17px 21px 0 rgb(94 98 193 / 7%);
    margin: 0 auto;
    position: relative;
    overflow: hidden;
  }
  .login-form {
    width: 600px;
    min-width: 500px;
    position: relative;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    padding-left: 40px;
    border-left: 1px solid hsla(0, 0%, 100%, 0.1);
  }
  .login .login-form .user-input {
    width: 100%;
  }
  .qr-logo {
    display: block;
    width: 232px;
    height: 73px;
    margin: 50px auto 0;
  }
  .ruleForm-box {
    padding: 50px;
    padding-right: 100px;
  }
  .forgot-cell {
    text-align: right;
    cursor: pointer;
  }
}
</style>

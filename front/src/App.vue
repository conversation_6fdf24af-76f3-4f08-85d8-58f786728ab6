<template>
  <div id="app">
    <!-- <div id="nav">
      <router-link to="/">Home</router-link> |
      <router-link to="/about">About</router-link>
    </div> -->
    <router-view />
  </div>
</template>

<style lang="less">
* {
  margin: 0;
  padding: 0;
  word-break: break-all;
    word-wrap: break-word;
}
body,
html {
  width: 100%;
  height: 100%;
  background: #eee8d8;
}
#app {
  font-family: Avenir, Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #2c3e50;
  height: 100%;
}

#nav {
  padding: 30px;

  a {
    font-weight: bold;
    color: #2c3e50;

    &.router-link-exact-active {
      color: #42b983;
    }
  }
}
.flex {
  display: flex;
}
.justify-between {
  justify-content: space-between;
}
.justify-center {
  justify-content: center;
}
.align-center {
  align-items: center;
}
.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}
.primary {
  color: #409eff;
}
.cursor {
    cursor: pointer;
}
.mr20{
  margin-right: 20px;
}

.mr10{
  margin-right: 10px;
}

.mg10 {
  margin: 10px 0;
}
.el-select{
  width: 100%;
}
.text-ellipsis {
    overflow: hidden;
    white-space: nowrap!important;
    text-overflow: ellipsis;
}
</style>

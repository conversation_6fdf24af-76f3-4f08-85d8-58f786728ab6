<template>
  <el-select v-model="valueTitle" ref="select" :clearable="clearable" @clear="clearHandle" :placeholder="placeholder" >
    <el-input
        v-if="isShowSearch"
        class="selectInput"
        :placeholder="placeholder"
        v-model="filterText">
    </el-input>

    <el-option :value="valueTitle" :label="valueTitle" class="options"   >
      <el-tree  id="tree-option"
                ref="selectTree"
				
                :accordion="accordion"
                :data="options"
                :props="props"
                :node-key="props.value"
                :default-expanded-keys="defaultExpandedKey"
                :filter-node-method="filterNode"
                @node-click="handleNodeClick">
      </el-tree>
    </el-option>
  </el-select>
</template>

<script>
export default {
  name: "el-tree-select",
  props:{
    /* 配置项 */
    props:{
      type: Object,
      default:()=>{
        return {
          value:'id',             // ID字段名
          label: 'title',         // 显示名称
          children: 'children'    // 子级字段名
        }
      }
    },
    /* 选项列表数据(树形结构的对象数组) */
    options:{
      type: Array,
      default: ()=>{ return [] }
    },
    isShowSearch: Boolean,
    /* 初始值 */
    value:{
      type: Number,
      default: ()=>{ return null }
    },
    /* 可清空选项 */
    clearable:{
      type:Boolean,
      default:()=>{ return true }
    },
    /* 自动收起 */
    accordion:{
      type:Boolean,
      default:()=>{ return true }
    },
    placeholder:{
      type:String,
      default:()=>{return "检索关键字"}
    }
  },
  data() {
    return {
      filterText: '',
      valueId:this.value,    // 初始值
      valueTitle:'',
      defaultExpandedKey:[]
    }
  },
  mounted(){
    this.initHandle()
  },
  methods: {
    // 初始化值
    initHandle(){
      if(this.valueId){
        this.valueTitle = this.$refs.selectTree.getNode(this.valueId).data[this.props.label]     // 初始化显示
        this.$refs.selectTree.setCurrentKey(this.valueId)       // 设置默认选中
        this.defaultExpandedKey = [this.valueId]      // 设置默认展开
      }
      this.initScroll()
    },
    // 初始化滚动条
    initScroll(){
      this.$nextTick(()=>{
        let scrollWrap = document.querySelectorAll('.el-scrollbar .el-select-dropdown__wrap')[0]
        let scrollBar = document.querySelectorAll('.el-scrollbar .el-scrollbar__bar')
        scrollWrap.style.cssText = 'margin: 0px; max-height: none; overflow: hidden;'
        scrollBar.forEach(ele => ele.style.width = 0)
      })
    },
    // 切换选项
    handleNodeClick(node){
      this.valueTitle = node[this.props.label]
      this.valueId = node.id
        console.log(node);
        this.$emit('getValue',node)
      this.$refs.select.blur();
      this.defaultExpandedKey = []
    },
    // 清除选中
    clearHandle(){
      this.valueTitle = ''
      this.valueId = null
      this.defaultExpandedKey = []
      this.clearSelected()
      this.$emit('getValue',null)
    },
    /* 清空选中样式 */
    clearSelected(){
      let allNode = document.querySelectorAll('#tree-option .el-tree-node')
      allNode.forEach((element)=>element.classList.remove('is-current'))
    },
    filterNode(value, data) {
      if (!value) return true;
      return data.name.indexOf(value) !== -1;
    }
  },
  watch: {
    value(){
      this.valueId = this.value
      this.initHandle()
    },
    filterText(val) {
      this.$refs.selectTree.filter(val);
    }
  },
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
.el-scrollbar .el-scrollbar__view .el-select-dropdown__item{
  height: auto;
  max-height: 274px;
  padding: 0;
  overflow: hidden;
  overflow-y: auto;
}
.el-select-dropdown__item.selected{
  font-weight: normal;
}
ul li >>>.el-tree .el-tree-node__content{
  height:auto;
  padding: 0 20px;
}
.el-tree-node__label{
  font-weight: normal;
}
.el-tree >>>.is-current .el-tree-node__label{
  color: #409EFF;
  font-weight: 700;
}
.el-tree >>>.is-current .el-tree-node__children .el-tree-node__label{
  color:#606266;
  font-weight: normal;
}
.selectInput{
  padding: 0 5px;
  box-sizing: border-box;
}
#tree-option >>>.el-tree-node {
  position: relative;
}
#tree-option >>>.el-tree-node__children .el-tree-node__expand-icon {
  position: relative;
}
#tree-option >>> .el-tree-node__children {
  overflow: hidden;
}
#tree-option >>> .el-tree-node__children .el-tree-node__expand-icon:after {
  content: "";
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  left: -200%;
  z-index: -1;
  right: 10px;
  height: 0;
  border-top: 2px dotted #C0C4CC;
}
#tree-option >>>.el-tree-node:before {
  content: "";
  position: absolute;
  top: 24px;
  z-index: 1;
  left: 0;
  width: 12px;
  background: #ffffff;
  border-right: 2px dotted #C0C4CC;
  height: 100%;
}
#tree-option >>>.el-icon-caret-right:before {
  content: "\e6d9";
  font-weight:bold;
  color:red;
  font-size:15px;
}
#tree-option >>>.el-tree-node__expand-icon.expanded {
  transform: rotate(0) !important;
}
#tree-option >>>.el-tree-node__expand-icon.expanded:before {
  content: "\e6d8";
}
#tree-option >>>.is-leaf:before {
  content: "\e6d8";
}
</style>

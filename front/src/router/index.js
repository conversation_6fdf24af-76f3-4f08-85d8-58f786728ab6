import Vue from 'vue'
import VueRouter from 'vue-router'
import Home from '../views/Home.vue'
import Login from '../views/Login.vue'
import storage from 'store'

Vue.use(VueRouter)

const routes = [{
        path: '/',
        name: 'Login',
        component: Login
    },
    {
        path: '/home',
        name: 'Home',
        component: Home,
        children: [{
                path: '/calendar',
                name: 'Calendar',
                component: () =>
                    import ('../views/calendar/Calendar.vue')
            },
            {
                path: '/case',
                name: 'Case',
                component: () =>
                    import ('../views/case/my/My.vue')
            },
			{
			    path: '/caseConflict',
			    name: 'caseConflict',
			    component: () =>
			        import ('../views/caseConflict/CaseConflict.vue')
			},
			{
			    path: '/nocase',
			    name: 'nocase',
			    component: () =>
			        import ('../views/nouser/nouser.vue')
			},
			{
			    path: '/noConflict',
			    name: 'noConflict',
			    component: () =>
			        import ('../views/nouser/nouser.vue')
			},
			{
			    path: '/nouser',
			    name: 'nouser',
			    component: () =>
			        import ('../views/nouser/nouser.vue')
			},
			{
			    path: '/noprogram',
			    name: 'noprogram',
			    component: () =>
			        import ('../views/nouser/nouser.vue')
			},
			{
			    path: '/nocheck',
			    name: 'nocheck',
			    component: () =>
			        import ('../views/nouser/nouser.vue')
			},
			{
			    path: '/nocost',
			    name: 'nocost',
			    component: () =>
			        import ('../views/nouser/nouser.vue')
			},
			{
			    path: '/nofinance',
			    name: 'nofinance',
			    component: () =>
			        import ('../views/nouser/nouser.vue')
			},
            {
                path: '/client',
                name: 'Client',
                component: () =>
                    import ('../views/client/my/My.vue')
            },
            {
                path: '/case/privy',
                name: 'Privy',
                component: () =>
                    import ('../views/case/privy/Privy.vue')
            },
            {
                path: '/case/detail',
                name: 'caseDetail',
                component: () =>
                    import ('../views/case/detail/Detail.vue')
            },

            {
                path: '/client/detail',
                name: 'clientDetail',
                component: () =>
                    import ('../views/client/detail/Detail.vue')
            },
            {
                path: '/caseProgram',
                name: 'caseProgram',
                component: () =>
                    import ('../views/caseProgram/CaseProgram.vue')
            },
            {
                path: '/caseCause',
                name: 'caseCause',
                component: () =>
                    import ('../views/caseCause/CaseCause.vue')
            },
        
			{
			    path: '/caseReview',
			    name: 'caseReview',
			    component: () =>
			        import ('../views/caseReview/CaseReview.vue')
			},
			{
			    path: '/manageTemplate',
			    name: 'manageTemplate',
			    component: () =>
			        import ('../views/manageTemplate/ManageTemplate.vue')
			},
            {
                path: '/user',
                name: 'user',
                component: () =>
                    import ('../views/user/User.vue')
            },
            {
                path: '/version',
                name: 'version',
                component: () =>
                    import ('../views/version/Version.vue')
            },
            {
                path: '/cost',
                name: 'cost',
                component: () =>
                    import ('../views/cost/Cost.vue')
            },
            {
                path: '/finance',
                name: 'cost',
                component: () =>
                    import ('../views/finance/Finance.vue')
            },
            {
                path: '/caseFinance',
                name: 'caseFinance',
                component: () =>
                    import ('../views/case/finance/Finance.vue')
            },
            {
                path: '/timingTask',
                name: 'timingTask',
                component: () =>
                    import ('../views/timingTask/TimingTask.vue')
            },
        ]
    },
    {
        path: '/case/createFile',
        name: 'createFile',
        component: () =>
            import ('../views/case/detail/CreateFile.vue')
    },
    {
        path: '/case/viewFile',
        name: 'viewFile',
        component: () =>
            import ('../views/case/detail/ViewFile.vue')
    },
    {
        path: '*',
        name: '404',
        component: () =>
            import ('../views/404.vue')
    },
]

const router = new VueRouter({
    mode: 'history',
    base: process.env.BASE_URL,
    routes
})

router.beforeEach((to, from, next) => {
    //如果进入到的路由是登录页或者注册页面，则正常展示
    if (to.path == '/') {
        next();
    } else if (!(storage.get('token'))) {
        next('/'); //转入login登录页面，登录成功后会将token存入localStorage
    } else {
        next();
    }
})

export default router
{"name": "case-cloud", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"@fullcalendar/core": "^5.9.0", "@fullcalendar/daygrid": "^5.9.0", "@fullcalendar/interaction": "^5.9.0", "@fullcalendar/timegrid": "^5.9.0", "@fullcalendar/vue": "^5.9.0", "@riophae/vue-treeselect": "^0.4.0", "axios": "^0.21.1", "core-js": "^3.16.0", "dayjs": "^1.10.6", "element-ui": "^2.15.3", "eslint": "^7.32.0", "lodash": "^4.17.21", "moment": "^2.29.1", "simple-uploader.js": "^0.5.6", "store": "^2.0.12", "storejs": "^1.1.0", "vue": "^2.6.11", "vue-full-calendar": "^2.8.1-0", "vue-pdf": "^4.3.0", "vue-router": "^3.2.0", "vuedraggable": "^2.24.3", "vuex": "^3.4.0"}, "devDependencies": {"@vue/cli-plugin-babel": "~4.5.0", "@vue/cli-plugin-eslint": "~4.5.0", "@vue/cli-plugin-router": "~4.5.0", "@vue/cli-plugin-vuex": "~4.5.0", "@vue/cli-service": "~4.5.0", "babel-eslint": "^10.1.0", "eslint-plugin-vue": "^6.2.2", "less": "^3.0.4", "less-loader": "^5.0.0", "vue-template-compiler": "^2.6.11"}}
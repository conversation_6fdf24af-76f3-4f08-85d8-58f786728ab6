# 律师事务所管理系统

一个基于Spring Boot + Vue.js的律师事务所案件管理系统，包含完整的前后端分离架构和admin后台管理功能。

## 🚀 快速启动

### 环境要求
- **Java**: JDK 1.8+
- **Node.js**: 12.0+
- **MySQL**: 5.7+

### 手动启动

#### 1. 启动后端
```bash
cd server/java_web_project
./mvnw clean install -DskipTests
cd jeeplus-web
./mvnw spring-boot:run
```

#### 2. 启动前端
```bash
cd front
npm install
npm run serve
```

**注意**: 首次启动需要修改配置文件，详见[完整启动文档](./项目启动文档.md)

## 📋 访问信息

| 服务 | 地址 | 说明 |
|------|------|------|
| 前端系统 | http://localhost:8001/ | 主要业务界面 |
| 后端API | http://localhost:8081/law/ | REST API服务 |
| API文档 | http://localhost:8081/law/doc.html | Swagger文档 |

## 🔐 登录信息

- **用户名**: `admin`
- **密码**: `123456`
- **用户类型**: 超级管理员

## 🎯 Admin后台功能

登录后可访问以下管理功能：

| 功能 | 访问地址 | 说明 |
|------|----------|------|
| 用户管理 | http://localhost:8001/user | 用户增删改查、权限管理 |
| 模板管理 | http://localhost:8001/manageTemplate | 文档模板管理 |
| 定时任务 | http://localhost:8001/timingTask | 系统定时任务管理 |
| 版本管理 | http://localhost:8001/version | 系统版本管理 |

## 💼 业务功能

- **案件管理**: 案件详情、审查、程序管理
- **客户管理**: 客户信息维护
- **财务管理**: 费用管理、财务统计
- **日程管理**: 日历功能、事件提醒
- **文档管理**: WPS在线文档集成

## 🛠️ 环境要求

- **Java**: JDK 1.8+
- **Node.js**: 12.0+
- **MySQL**: 5.7+
- **Maven**: 3.6+（可选，项目包含Maven Wrapper）

## 📁 项目结构

```
oa版本-wps/
├── server/java_web_project/          # 后端Java项目
│   ├── jeeplus-web/                  # 启动模块
│   ├── jeeplus-platform/             # 平台核心模块
│   ├── jeeplus-plugins/              # 插件模块
│   └── jeeplus-module/               # 业务模块
├── front/                            # 前端Vue项目
├── start.sh                          # Linux/macOS启动脚本
├── start.bat                         # Windows启动脚本
├── 项目启动文档.md                   # 详细启动文档
└── README.md                         # 本文件
```

## 🔧 常用命令

### 基本命令

```bash
# 后端相关
cd server/java_web_project
./mvnw clean install -DskipTests    # 编译项目
cd jeeplus-web
./mvnw spring-boot:run              # 启动后端

# 前端相关
cd front
npm install                         # 安装依赖
npm run serve                       # 启动开发服务器
npm run build                       # 构建生产版本

# 数据库相关
mysql -h 127.0.0.1 -P 3306 -u law_case_manage -pCTfbTMSXx8FcpEht -D law_case_manage

# 服务检查
curl http://localhost:8081/law/actuator/health  # 后端健康检查
curl http://localhost:8001/                     # 前端访问检查
```

## 🐛 常见问题

### 1. 配置文件必须修改
**首次启动前必须修改以下配置**：
- `server/java_web_project/jeeplus-web/src/main/resources/application-development.yml`
- `front/vue.config.js`

详细配置说明请查看[完整启动文档](./项目启动文档.md)

### 2. 端口被占用
```bash
# 查看端口占用
lsof -i :8081  # 后端端口
lsof -i :8001  # 前端端口

# 杀死进程
kill -9 PID
```

### 3. Java环境问题
```bash
# 检查Java版本
java -version

# 设置JAVA_HOME（如果需要）
export JAVA_HOME=/path/to/java
export PATH=$JAVA_HOME/bin:$PATH
```

### 4. 数据库连接失败
```bash
# 测试数据库连接
mysql -h 127.0.0.1 -P 3306 -u law_case_manage -pCTfbTMSXx8FcpEht -e "SELECT 1;"
```

### 5. 编译失败
```bash
# 清理重新编译
rm -rf ~/.m2/repository
./mvnw clean install -DskipTests
```

## 📚 文档

- **[详细启动文档](./项目启动文档.md)** - 完整的安装、配置和故障排除指南
- **[API文档](http://localhost:8081/law/doc.html)** - 后端API接口文档（需要先启动后端服务）

## 🔄 服务管理

### 停止服务
在启动终端按 `Ctrl+C` 正常停止服务

### 重启服务
重新执行启动命令即可

### 查看日志
- **后端日志**：在启动终端查看实时输出
- **前端日志**：浏览器开发者工具Console
- **数据库日志**：MySQL日志文件

## ⚠️ 重要提醒

1. **首次启动必须修改配置文件**，否则会启动失败
2. **必须先启动后端，再启动前端**
3. **确保MySQL服务正在运行**
4. **确保端口8081和8001未被占用**

## 📞 技术支持

如遇到问题，请按以下顺序排查：
1. 查看[详细启动文档](./项目启动文档.md)中的故障排除部分
2. 检查环境要求是否满足
3. 确认配置文件是否正确修改
4. 查看启动日志中的错误信息

---

**版本**: v1.0
**更新时间**: 2025-01-25
**文档状态**: ✅ 完整可用
